/**
 * TODO List of Planned Projects and Improvements
 * 
 * This section outlines upcoming development tasks and feature enhancements:
 * 
 */
Projects:
    ☐ Add feature to edit selected jiraprojects (mostly to update the token) @high 
       ☐ change create and edit button text for icons
    ☐ On jiraproject creation form: 
        ☐ change token input from text to password type @high 
    ☐ Add pagination to sonarqube projects  (/cqm/sonarqube/projects) @low
    ☐ Fix the theme selector @low 
    ☐ Finish Edit functionality for Sonarqube projects @high 
    ☐ Add in config default per page and use that in all pagination pages
    ☐ Rename subdomain fields from jiraproject table to "jira_url" 
    ☐ Add default jiraURL to intranet config
    ☐ Add uniqueness validation to jira_url + project_key
    ☐ Display in the jiraproject list "{projectname} - {jira_url}"
    ☐ After Jiraproject creation, select the jira project to the currently created jiraproject



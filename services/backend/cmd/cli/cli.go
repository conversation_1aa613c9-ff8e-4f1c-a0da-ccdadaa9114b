// Package cli implements the command line interface functionality for the Backend service
package cli

import (
	"app/backend/http/api"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/spf13/cobra"
)

func Run() error {
	rootCmd := &cobra.Command{
		Use:   "backend",
		Short: "Backend service",
		Long:  `Backend service that handles UI requests and business logic`,
	}

	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Manage the server",
		Long:  `Server commands allow you to manage the Backend server instance`,
	}

	startCmd := &cobra.Command{
		Use:   "start",
		Short: "Start the server",
		Long:  `Start the Backend server and begin handling requests`,
		RunE:  runServer(),
	}
	// Add server command to root
	rootCmd.AddCommand(serverCmd)
	// Add start command to server
	serverCmd.AddCommand(startCmd)

	return rootCmd.Execute()
}

func runServer() func(cmd *cobra.Command, args []string) error {
	return func(cmd *cobra.Command, args []string) error {
		app := httpsvr.NewApp()
		api.RegisterRoutes(app.Router)

		return app.Run()
	}
}

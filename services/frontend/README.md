# Frontend



## Run database migrations

```bash
go run cmd/main.go db migrate
```

## Run database rollback


```bash
 go run cmd/main.go db rollback
```

## Run db seed

```bash
go run cmd/db/seed/main.go
```

## Run CLI Operator

### Update user with role

```bash
go run cmd/main.go operator user:roles <EMAIL> guest
```


## Standards

### Git commits

- (https://www.conventionalcommits.org/en/v1.0.0/)[https://www.conventionalcommits.org/en/v1.0.0/]

### Go style guides

- (https://go.dev/doc/effective_go)[https://go.dev/doc/effective_go]
- (https://go.dev/wiki/CodeReviewComments)[https://go.dev/wiki/CodeReviewComments]
- (https://github.com/uber-go/guide/tree/master)[https://github.com/uber-go/guide/tree/master]


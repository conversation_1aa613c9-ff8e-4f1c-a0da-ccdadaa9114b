// Package assets provides embedded filesystem functionality for serving static frontend assets
package assets

import (
	"embed"
	"io/fs"
)

//go:embed public
var PublicFS embed.FS

var wikiFS, buildFS fs.FS

type AssetFS struct {
	WikiFS  fs.FS
	BuildFS fs.FS
}

func EmbedFS() AssetFS {
	if wikiFS == nil {
		subFS, err := fs.Sub(PublicFS, "public/wiki")
		if err != nil {
			panic("failed to create WikiFS: " + err.Error())
		}

		wikiFS = subFS
	}

	if buildFS == nil {
		subFS, err := fs.Sub(PublicFS, "public/build")
		if err != nil {
			panic("failed to create BuildFS: " + err.Error())
		}

		buildFS = subFS
	}

	return AssetFS{
		WikiFS:  wikiFS,
		BuildFS: buildFS,
	}
}

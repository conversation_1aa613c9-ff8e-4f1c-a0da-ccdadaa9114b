import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import react from '@vitejs/plugin-react';

export default defineConfig({
    plugins: [
        laravel({
            input: 'resources/js/app.jsx',
            publicDirectory: 'public',
            buildDirectory: 'build',
            refresh: true,
        }),
        react({ include: /\.(mdx|js|jsx|ts|tsx)$/ }),
    ],
    build: {
        manifest: true, // Generate manifest.json file
        outDir: 'public/build',
        rollupOptions: {
            input: 'resources/js/app.jsx',
            output: {
                entryFileNames: 'assets/[name].js',
                chunkFileNames: 'assets/[name].js',
                assetFileNames: 'assets/[name].[ext]',
                // manualChunks: undefined, // Disable automatic chunk splitting
                manualChunks: (id) => {
                    if (id.includes('node_modules')) {
                        if (id.includes('react')) {
                            return 'react-vendor'
                        }
                        if (id.includes('@inertiajs')) {
                            return 'inertia-vendor'
                        }
                        return 'vendor'
                    }
                },
            },
        },

        chunkSizeWarningLimit: 500,
        minify: 'terser',
        terserOptions: {
            compress: {
                drop_console: true,
                drop_debugger: true
            }
        }
    },
    server: {
        hmr: {
            host: 'localhost',
        },
    },
});

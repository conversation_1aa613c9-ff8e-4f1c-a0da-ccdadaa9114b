# https://taskfile.dev

version: '3'

tasks:
  setup:
    desc: 'Setup the frontend'
    cmds:
      - cmd: npm install
      - cmd: which air || go install github.com/air-verse/air@latest
      - cmd: which golangci-lint || go install github.com/golangci/golangci-lint/v2/cmd/golangci-lint@latest
      - cmd: which runner || (go get cirello.io/runner/v3 && go install cirello.io/runner/v3)
      - cmd: which git-chglog || go install github.com/git-chglog/git-chglog/cmd/git-chglog@latest
      - cmd: which gocov || go install github.com/axw/gocov/gocov@latest
      - cmd: which gocov-html || go install github.com/matm/gocov-html/cmd/gocov-html@latest
      - cmd: which gotest || go install github.com/rakyll/gotest@latest
      - cmd: which gotestsum || go install gotest.tools/gotestsum@latest
      - cmd: which govulncheck || go install golang.org/x/vuln/cmd/govulncheck@latest
  silent: true
  run-security-scan:
    cmds:
    - govulncheck -show verbose ./...
    silent: false
  run-dev:
    desc: 'Serve the frontend in dev mode'
    cmds:
      - runner -only "npm-dev web-dev"
    silent: true
  build-prod:
    desc: 'Build the frontend'
    cmds:
      - npm run build
      - rm -f public/hot
      - go build -ldflags="-s -w" -gcflags="-N -l=4"  -o dist/frontend cmd/main.go
      - cp -rf public dist/
      - cp -rf resources dist/
      - cp -rf bootstrap dist/
    silent: true
  run-prod:
    desc: 'Serve the frontend in prod mode'
    cmds:
      - ./frontend server start
    dir: ./dist
    silent: true
  run-prod-ssr:
    desc: 'Serve the frontend in prod mode with SSR'
    cmds:
      - runner -only "npm-ssr web-prod"
    silent: true
  build-container:
    desc: 'Build the container'
    cmds:
      - docker build -f devops/Dockerfile --target prod-frontend-ssr -t sa-intranet-prod-frontend-ssr ../
    silent: true

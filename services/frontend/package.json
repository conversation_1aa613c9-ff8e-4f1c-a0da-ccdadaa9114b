{"name": "vuego", "version": "1.0.0", "main": "index.js", "type": "module", "license": "MIT", "scripts": {"dev": "vite --host 0.0.0.0 --port 13714", "build": "vite build && vite build --config ssr.config.js", "build-watch": "vite build --watch && vite build --config ssr.config.js", "ssr": "node bootstrap/assets/ssr.js"}, "devDependencies": {"@inertiajs/react": "^2.0.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.12", "axios": "^1.6.4", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "tailwindcss": "^3.4.17", "terser": "^5.39.0", "vite": "^6.3.5"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^2.2.0", "@tailwindcss/vite": "^4.0.6", "daisyui": "^4.12.23", "react": "^18.3.1", "react-dom": "^18.3.1"}}
// Package cli implements the command line interface functionality for the frontend service
package cli

import (
	"context"
	"fmt"
	"sa-intranet/cli"

	"app/frontend/internal/service/provider"

	"github.com/emilioforrer/hexa/bootstrap"
	"github.com/samber/do"

	// inertia "github.com/romsar/gonertia"
	"github.com/spf13/cobra"
)

func Bootstrap(i *do.Injector) *bootstrap.DefaultBootStrapper {
	return bootstrap.NewDefaultBootStrapper(i,
		provider.NewAppServiceProvider(i),
	)
}

func Run() error {
	ctx := context.Background()
	injector := do.New()
	bootstrapper := Bootstrap(injector)

	if err := bootstrapper.Register(ctx); err != nil {
		return fmt.Errorf("failed to register services: %w", err)
	}

	rootCmd := &cobra.Command{
		Use:   "frontend",
		Short: "Frontend service",
		Long:  `Frontend service that handles UI requests and business logic`,
	}

	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Manage the server",
		Long:  `Server commands allow you to manage the Frontend server instance`,
	}

	startCmd := &cobra.Command{
		Use:   "start",
		Short: "Start the server",
		Long:  `Start the Frontend server and begin handling requests`,
		RunE:  runServer(bootstrapper),
	}
	// Add server command to root
	rootCmd.AddCommand(serverCmd)
	rootCmd.AddCommand(cli.NewDBCommand(injector))
	//  Add operator command to root
	rootCmd.AddCommand(cli.NewOperatorCommand(injector))
	// Add start command to server
	serverCmd.AddCommand(startCmd)

	return rootCmd.Execute()
}

func runServer(bootstrapper *bootstrap.DefaultBootStrapper) func(cmd *cobra.Command, args []string) error {
	return func(cmd *cobra.Command, args []string) error {
		ctx := context.Background()

		return bootstrapper.Boot(ctx)
	}
}

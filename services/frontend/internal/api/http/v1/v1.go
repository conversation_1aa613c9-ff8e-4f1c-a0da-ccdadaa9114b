// Package v1 implements version 1 of the HTTP API
package v1

import (
	"fmt"
	"log"
	"net/http"

	"app/frontend/internal/api/app"
)

func RegisterRoutes(app app.App) {
	r := app.Router()

	r.GET("/api/healthz", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "OK")
	})

	r.GET("/webhooks/sonarqube", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "OK")
	})

	r.GET("/api/v1/hello", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Welcome to API v1")
	})

	r.GET("/api/v1/helloerr", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)

		_, err := w.Write([]byte("Noooo"))
		if err != nil {
			log.Printf("failed to write response: %v", err)
		}
	})

	r.GET("/api/v1/hellocerr", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusUnprocessableEntity)

		_, err := w.Write([]byte("Unprocessable request entity"))
		if err != nil {
			log.Printf("failed to write response: %v", err)
		}
	})
}

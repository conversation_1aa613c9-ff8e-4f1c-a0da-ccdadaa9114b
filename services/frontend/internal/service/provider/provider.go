package provider

import (
	"context"
	"fmt"
	"sa-intranet/http"

	"app/frontend/internal/api"
	"app/frontend/internal/ui"
	v1 "app/frontend/internal/ui/http/v1"

	"github.com/emilioforrer/hexa/bootstrap"
	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/samber/do"
)

func NewAppServiceProvider(i *do.Injector) bootstrap.ServiceProvider {
	app := ui.NewApp(i)
	return &AppServiceProvider{
		app:      app,
		injector: i,
	}
}

type AppServiceProvider struct {
	app      *ui.App
	injector *do.Injector
}

func (a *AppServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	// Register the app
	do.Provide(a.injector, func(i *do.Injector) (*ui.App, error) {
		return a.app, nil
	})

	err := a.app.Register()
	if err != nil {
		return fmt.Errorf("failed to register app: %w", err)
	}
	return nil
}

func (a *AppServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	a.setupRouter()
	// Start the server
	return a.app.Run()
}

func (a *AppServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	return a.app.Stop(ctx)
}

func (a *AppServiceProvider) setupRouter() {
	app := a.app

	// Set up middlewares
	app.Router().Use(httpsvr.RecoveryMiddleware())

	if app.AppConfig.AuthAwsCognitoEnabled {
		skipPaths := []string{
			"^/api.*$",
			"^/webhooks.*$",
			"^/health.*$",
		}
		app.Router().Use(
			http.SkipMiddlewaresForPaths(skipPaths,
				http.AuthMiddleware(app.AppConfig.AwsCognitoSigner),
			),
		)
		app.Router().Use(
			http.SkipMiddlewaresForPaths(skipPaths,
				http.UserProvisioningMiddleware(app.Injector()),
			),
		)
		app.Router().Use(
			http.SkipMiddlewaresForPaths(skipPaths,
				http.UserAuthorizationMiddleware(app.Injector()),
			),
		)
	}
	// Register routes
	v1.RegisterRoutes(app)

	api.RegisterRoutes(app)
}

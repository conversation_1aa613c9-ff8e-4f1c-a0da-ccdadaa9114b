package ui

import (
	"github.com/emilioforrer/hexa/cmd"
)

type SSRApp struct {
	*cmd.ProcessRunner
	healthy chan bool
}

var _ cmd.Runner = (*SSRApp)(nil)

func NewSSRApp() cmd.Runner {
	config := cmd.ProcessConfig{
		Name:    "ssr",
		Command: "npm",
		Args:    []string{"run", "ssr"},
	}
	runner := cmd.NewProcessRunner(config)
	app := &SSRApp{
		ProcessRunner: runner,
		healthy:       make(chan bool, 1),
	}

	go func() {
		for stdout := range runner.Stdout() {
			if stdout == "Inertia SSR server started." {
				app.healthy <- true
				break
			}
		}
	}()

	return app
}

func (a *SSRApp) Healthy() chan bool {
	return a.healthy
}

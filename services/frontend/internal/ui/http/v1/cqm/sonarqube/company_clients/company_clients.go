package company_clients

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"
	"strconv"

	"app/frontend/internal/ui"

	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

func RegisterRoutes(app *ui.App) {
	baseRoute := "/cqm/company/clients"
	usecase := do.MustInvoke[*interactor.CompanyClientsInteractor](app.Injector())
	crtl := &Controller{
		inertia: app.Inertia(),
		usecase: usecase,
		config:  app.AppConfig,
	}

	app.Router().GET(baseRoute, nil, crtl.IndexHandler())
	app.Router().POST(baseRoute, nil, crtl.CreateCompanyClient())
	app.Router().PUT(baseRoute+"/{clientID}", nil, crtl.UpdateCompanyClient())
}

type Controller struct {
	inertia *inertia.Inertia
	usecase *interactor.CompanyClientsInteractor
	config  *ui.AppConfig
}

func (c *Controller) IndexHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {

		filter := r.URL.Query().Get("name")

		pageParam := r.URL.Query().Get("page")
		page := 1
		pageInt, err := strconv.Atoi(pageParam)
		if err == nil {
			page = pageInt
		}

		data, err := c.usecase.InitialData(filter, page, c.config.Intranet.DefaultPageSize)
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
		err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
			"companyClients":           data.CompanyClients,
			"companyClientsPagination": data.Pagination,
		})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) CreateCompanyClient() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		var data interactor.CompanyClientValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		errors := []string{}

		companyClient, validationErrors, err := c.usecase.CreateCompanyClient(data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), "CQM/Sonarqube/Projects/Index", inertia.Props{})
		} else {
			err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
				"errors":               errors,
				"createdCompanyClient": companyClient,
			})

		}

		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) UpdateCompanyClient() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		var data interactor.CompanyClientValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		clientID := r.PathValue("clientID")
		errors := []string{}

		companyClient, validationErrors, err := c.usecase.UpdateCompanyClient(clientID, data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), "CQM/Sonarqube/Projects/Index", inertia.Props{})
		} else {
			err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
				"errors":               errors,
				"updatedCompanyClient": companyClient,
			})

		}

		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}

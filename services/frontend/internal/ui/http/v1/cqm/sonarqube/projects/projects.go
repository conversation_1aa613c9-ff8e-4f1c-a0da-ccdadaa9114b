package projects

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"
	"strconv"

	"app/frontend/internal/ui"

	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

func RegisterRoutes(app *ui.App) {
	baseRoute := "/cqm/sonarqube/projects"
	usecase := do.MustInvoke[*interactor.SonarProjectsInteractor](app.Injector())
	crtl := &Controller{
		inertia: app.Inertia(),
		usecase: usecase,
		config:  app.AppConfig,
	}

	app.Router().GET(baseRoute, nil, crtl.IndexHandler())
	app.Router().POST(baseRoute, nil, crtl.CreateSonarProject())
	app.Router().PUT(baseRoute+"/{projectID}", nil, crtl.UpdateSonarProject())
}

type Issues struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Controller struct {
	inertia *inertia.Inertia
	usecase *interactor.SonarProjectsInteractor
	config  *ui.AppConfig
}

func (c *Controller) IndexHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		filter := r.URL.Query().Get("name")

		pageParam := r.URL.Query().Get("page")
		page := 1
		pageInt, err := strconv.Atoi(pageParam)
		if err == nil {
			page = pageInt
		}

		data, err := c.usecase.InitialData(filter, page, c.config.Intranet.DefaultPageSize)
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
			"sonarProjects":           data.SonarProjects,
			"sonarProjectsPagination": data.Pagination,
			"env": inertia.Props{
				"jiraURL": c.config.Intranet.JiraURL,
			},
		})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) CreateSonarProject() func(next http.Handler) http.Handler {
	i := c.inertia
	// ctx := context.Background()
	fn := func(w http.ResponseWriter, r *http.Request) {
		errors := []string{}

		var data interactor.SonarProjectValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			errors = append(errors, "Invalid JSON")
		}

		sonarProject, validationErrors, err := c.usecase.CreateSonarProject(data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), "CQM/Sonarqube/Projects/Index", inertia.Props{})
		} else {
			err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
				"errors":              errors,
				"createdSonarProject": sonarProject,
			})
		}

		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) UpdateSonarProject() func(next http.Handler) http.Handler {
	i := c.inertia
	// ctx := context.Background()
	fn := func(w http.ResponseWriter, r *http.Request) {
		errors := []string{}

		var data interactor.SonarProjectValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			errors = append(errors, "Invalid JSON")
		}

		projectID := r.PathValue("projectID")

		sonarProject, validationErrors, err := c.usecase.UpdateSonarProject(projectID, data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), "CQM/Sonarqube/Projects/Index", inertia.Props{})
		} else {
			err = i.Render(w, r, "CQM/Sonarqube/Projects/Index", inertia.Props{
				"errors":              errors,
				"updatedSonarProject": sonarProject,
			})
		}

		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}

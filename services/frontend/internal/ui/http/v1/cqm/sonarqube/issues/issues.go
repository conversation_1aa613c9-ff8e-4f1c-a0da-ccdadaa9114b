package issues

import (
	"log"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"
	"strconv"

	"app/frontend/internal/ui"

	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

func RegisterRoutes(app *ui.App) {
	usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](app.Injector())
	crtl := &Controller{
		inertia: app.Inertia(),
		usecase: usecase,
		config:  app.AppConfig,
	}

	app.Router().GET("/cqm/sonarqube/projects/{projectID}/issues", nil, crtl.IndexHandler())
	app.Router().POST("/cqm/sonarqube/projects/{projectID}/issues/{issueID}", nil, crtl.CreateJiraIssueHandler())
}

type Issues struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Controller struct {
	inertia *inertia.Inertia
	usecase *interactor.SonarIssuesInteractor
	config  *ui.AppConfig
}

func (c *Controller) IndexHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		projectID := r.PathValue("projectID")
		pageParam := r.URL.Query().Get("page")
		page := 1
		pageInt, err := strconv.Atoi(pageParam)
		if err == nil {
			page = pageInt
		}

		data, err := c.usecase.InitialData(projectID, page, c.config.Intranet.DefaultPageSize)
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}

		err = i.Render(w, r, "CQM/Sonarqube/Issues/Index", inertia.Props{
			"projectID":     data.ProjectID,
			"issues":        data.SonarIssues,
			"errors":        []string{},
			"updatedIssues": []interactor.SonarIssue{},
			"pagination":    data.Pagination,
			"jiraProject":   data.JiraProject,
		})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) CreateJiraIssueHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		projectID := r.PathValue("projectID")
		props := inertia.Props{
			"projectID": projectID,
			"errors":    []string{},
		}
		issueID := r.PathValue("issueID")

		errors := []string{}

		issue, err := c.usecase.CreateJiraIssue(projectID, issueID)
		if err != nil {
			errors = append(errors, err.Error())
		}

		r.RequestURI = r.Referer()

		if len(errors) > 0 {
			props["errors"] = errors
		}

		props["updatedIssues"] = []interactor.SonarIssue{
			issue,
		}

		err = i.Render(w, r, "CQM/Sonarqube/Issues/Index", props)
		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}

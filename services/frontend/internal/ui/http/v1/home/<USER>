// Package home handles the routing and handlers for the home page and build assets.
package home

import (
	"log"
	"net/http"

	"app/frontend/internal/ui"

	"github.com/NYTimes/gziphandler"

	inertia "github.com/romsar/gonertia/v2"
)

func RegisterRoutes(app *ui.App) {
	eFS := app.EFS()
	i := app.Inertia()
	homeHandler := func(http.ResponseWriter, *http.Request) {
	}
	buildHandler := func(http.ResponseWriter, *http.Request) {
	}

	buildMiddleware := func(next http.Handler) http.Handler {
		compressed := gziphandler.GzipHandler(ui.GetBuildHandler(eFS))
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Cache-Control", "public, max-age=********, immutable")
			compressed.ServeHTTP(w, r)
		})
	}

	homeMiddleware := func(next http.Handler) http.Handler {
		compressed := gziphandler.GzipHandler(i.Middleware(HomeHandler(i)))
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.<PERSON><PERSON>().Set("Cache-Control", "public, max-age=3600") // 1 hour cache for home page
			compressed.ServeHTTP(w, r)
		})
	}

	app.Router().GET("/", homeHandler, homeMiddleware)
	app.Router().GET("/build/", buildHandler, buildMiddleware)
}

func HomeHandler(i *inertia.Inertia) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		err := i.Render(w, r, "Home/Index", inertia.Props{
			"text": "Inertia.js with React and Go! 💚",
		})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return http.HandlerFunc(fn)
}

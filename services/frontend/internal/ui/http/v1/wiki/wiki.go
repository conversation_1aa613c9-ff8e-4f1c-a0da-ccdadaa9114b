// Package wiki provides functionality for handling wiki-related HTTP routes.
package wiki

import (
	"net/http"

	"app/frontend/internal/ui"
)

func RegisterRoutes(app *ui.App) {
	wikiHandler := func(w http.ResponseWriter, r *http.Request) {
		fs := http.FileServer(http.FS(app.EFS().WikiFS))
		fileServer := http.StripPrefix("/wiki/", fs)
		fileServer.ServeHTTP(w, r)
	}

	app.Router().GET("/wiki/", wikiHandler)
	app.Router().GET("/wiki/*", wikiHandler)
}

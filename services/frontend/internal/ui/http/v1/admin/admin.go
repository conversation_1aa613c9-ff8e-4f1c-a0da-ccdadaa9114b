package admin

import (
	"log"
	"net/http"

	"app/frontend/internal/ui"
	"app/frontend/internal/ui/http/v1/admin/users"

	inertia "github.com/romsar/gonertia/v2"
)

func RegisterRoutes(app *ui.App) {
	c := &controller{app: app}
	app.Router().GET("/admin", c.IndexHandler())
	users.RegisterRoutes(app)
}

type controller struct {
	app *ui.App
}

func (c *controller) IndexHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		err := c.app.Inertia().Render(w, r, "Admin/Index", inertia.Props{})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}
}

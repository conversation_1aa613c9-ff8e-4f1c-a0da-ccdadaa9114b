package sonarqube

import (
	"crypto/subtle"
	"encoding/json"
	"log/slog"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"

	"app/frontend/internal/ui"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/samber/do"
)

func RegisterRoutes(app *ui.App, defaultMiddlewares ...httpsvr.Middleware) {
	baseRoute := "/webhooks/sonarqube"
	crtl := &Controller{
		analyzeUsecase: do.MustInvoke[*interactor.SonarAnalizerInteractor](app.Injector()),
	}

	username := app.AppConfig.SonarQubeWebhookAuthUsername
	password := app.AppConfig.SonarQubeWebhookAuthPassword

	analizeMiddlewares := append(defaultMiddlewares, AuthMiddleware(username, password))

	app.Router().POST(baseRoute+"/analyze", crtl.Analyze(), analizeMiddlewares...)
}

type Controller struct {
	analyzeUsecase *interactor.SonarAnalizerInteractor
}

func (c *Controller) Analyze() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		// Parse the JSON payload from the request body
		var payload interactor.SonarQubeWebhook

		if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
			slog.Error("failed to decode payload", "err", err)
			errMsg := "failed to analyze payload"
			http.Error(w, errMsg, http.StatusBadRequest)
			return
		}
		options := interactor.AnalizeOptions{}
		response, err := c.analyzeUsecase.Analyze(ctx, payload, options)
		if err != nil {
			http.Error(w, err.Error(), http.StatusUnprocessableEntity)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		if err := json.NewEncoder(w).Encode(response); err != nil {
			slog.Error("failed to encode response", "err", err)
		}
	}
}

func AuthMiddleware(expectedUsername string, expectedPassword string) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			username, password, ok := r.BasicAuth()

			if !ok {
				w.Header().Set("WWW-Authenticate", `Basic realm="Restricted"`)
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}

			// Use constant-time operations for all comparisons
			usernameMatch := subtle.ConstantTimeCompare([]byte(username), []byte(expectedUsername))
			passwordMatch := subtle.ConstantTimeCompare([]byte(password), []byte(expectedPassword))
			credentialsValid := (usernameMatch & passwordMatch) == 1

			if credentialsValid && len(expectedUsername) > 0 && len(expectedPassword) > 0 {
				next.ServeHTTP(w, r)
				return
			}

			http.Error(w, "Forbidden", http.StatusForbidden)
		})
	}
}

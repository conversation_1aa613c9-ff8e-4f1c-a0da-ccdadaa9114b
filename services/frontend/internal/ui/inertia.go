package ui

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"

	assets "app/frontend"

	inertia "github.com/romsar/gonertia/v2"
)

var ErrAssetNotFound = fmt.Errorf("asset not found")

func GetBuildHandler(publicFS assets.AssetFS) http.Handler {
	// buildFS, err := fs.Sub(publicFS, "public/build")
	// if err != nil {
	// 	panic("failed to create BuildFS: " + err.Error())
	// }

	// Check if in development mode (Vite hot reload)
	if _, err := os.Stat("./public/hot"); err == nil {
		// Dev mode: Serve directly from filesystem
		return http.StripPrefix("/build/", http.FileServer(http.Dir("./public/build")))
	}

	// Prod mode: Serve from embedded files
	// buildSubFS, err := fs.Sub(buildFS, "public/build")
	// if err != nil {
	// 	log.Fatal(err)
	// }

	// return http.StripPrefix("/build/", http.FileServer(http.FS(buildSubFS)))
	return http.StripPrefix("/build/", http.FileServer(http.Dir("./public/build")))
}

func InitInertia(publicFS assets.AssetFS) *inertia.Inertia {
	viteHotFile := "./public/hot"
	rootViewFile := "resources/views/root.html"

	// check if laravel-vite-plugin is running in dev mode (it puts a "hot" file in the public folder)
	_, err := os.Stat(viteHotFile)
	if err == nil {
		i, initErr := inertia.NewFromFile(
			rootViewFile,
			inertia.WithSSR("http://localhost:13714"),
		)

		if initErr != nil {
			log.Fatal(initErr)
		}

		i.ShareTemplateFunc("vite", func(entry string) (string, error) {
			content, readErr := os.ReadFile(viteHotFile)
			if readErr != nil {
				return "", readErr
			}

			url := strings.TrimSpace(string(content))

			if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
				url = url[strings.Index(url, ":")+1:]
			} else {
				url = "//localhost:8000"
			}

			if entry != "" && !strings.HasPrefix(entry, "/") {
				entry = "/" + entry
			}

			return url + entry, nil
		})

		i.ShareTemplateData("hmr", true)

		return i
	}

	// laravel-vite-plugin not running in dev mode, use build manifest file
	manifestPath := "./public/build/manifest.json"

	// check if the manifest file exists, if not, rename it
	if _, statErr := os.Stat(manifestPath); os.IsNotExist(statErr) {
		// move the manifest from ./public/build/.vite/manifest.json to ./public/build/manifest.json
		// so that the vite function can find it
		renameErr := os.Rename("./public/build/.vite/manifest.json", "./public/build/manifest.json")
		if renameErr != nil {
			return nil
		}

	}

	i, initErr := inertia.NewFromFile(
		rootViewFile,
		// inertia.WithVersionFromFile(manifestPath),
		inertia.WithSSR("http://localhost:13714"),
	)
	if initErr != nil {
		log.Fatal(initErr)
	}

	i.ShareTemplateFunc("vite", vite(manifestPath, "/build/"))

	return i
}

func vite(manifestPath, buildDir string) func(path string) (string, error) {
	f, err := os.Open(filepath.Clean(manifestPath))
	if err != nil {
		log.Printf("cannot open provided vite manifest file: %s", err)
		return nil
	}

	defer f.Close()

	viteAssets := make(map[string]*struct {
		File   string `json:"file"`
		Source string `json:"src"`
	})
	err = json.NewDecoder(f).Decode(&viteAssets)
	// print content of viteAssets
	for k, v := range viteAssets {
		log.Printf("%s: %s\n", k, v.File)
	}

	if err != nil {
		log.Printf("cannot unmarshal vite manifest file to json: %s", err)
		return nil
	}

	return func(p string) (string, error) {
		if val, ok := viteAssets[p]; ok {
			return path.Join("/", buildDir, val.File), nil
		}

		return "", fmt.Errorf("%w: %q", ErrAssetNotFound, p)
	}
}

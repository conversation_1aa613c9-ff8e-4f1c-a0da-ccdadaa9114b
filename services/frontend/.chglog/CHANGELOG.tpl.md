{{- define "commitEmoji" -}}
    {{- if regexMatch "^feat" . -}}✨ Features
    {{- else if regexMatch "^fix" . -}}🐛 Bug Fixes
    {{- else if regexMatch "^doc" . -}}📝 Documentation
    {{- else if regexMatch "^perf" . -}}⚡️ Performance
    {{- else if regexMatch "^refactor" . -}}♻️ Refactor
    {{- else if regexMatch "^style" . -}}🎨 Styling
    {{- else if regexMatch "^test" . -}}✅ Testing
    {{- else if regexMatch "^revert" . -}}⏪ Revert
    {{- else if regexMatch "^build" . -}}👷 Build
    {{- else if regexMatch "^ci" . -}}👷 CI
    {{- else if regexMatch "^cd" . -}}🚀 Deployment
    {{- else if regexMatch "^chore\\(security\\)" . -}}🛡️ Security
    {{- else if regexMatch "^chore\\(deps\\)" . -}}📌 Dependencies
    {{- else if regexMatch "^chore\\(config\\)" . -}}🔧 Configurations
    {{- else if regexMatch "^chore" . -}}⚙️ Miscellaneous Tasks
    {{- else -}}💼
    {{- end -}}
{{- end -}}

{{- define "renderCommit" -}}
    {{- $skip_patterns := list "^chore\\(wip\\)" "\\[automated\\]" "\\[bot\\]" -}}
    {{- $scope := "" -}}
    {{- $txt := "" -}}
    {{- if .Scope -}}
        {{- $txt = printf "**%s:** %s" .Scope .Subject -}}
        {{- $scope = printf "%s(%s): %s" .Type .Scope .Subject  -}}
    {{- else -}}
        {{- $txt = .Subject -}}
        {{- $scope = .Type  -}}
    {{- end -}}
    {{- $found := false -}}
    {{- range $pattern := $skip_patterns -}}
        {{- if (regexMatch $pattern $scope)  -}}
            {{- $found = true -}}
        {{- end -}}
    {{- end -}}
    {{- if not $found -}}
    - {{ $txt }}
    {{- end -}}
{{- end -}}



# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

{{ if .Versions -}}
<a name="unreleased"></a>
## [Unreleased]

{{ if .Unreleased.CommitGroups -}}
{{ range .Unreleased.CommitGroups -}}
### {{ template "commitEmoji" .Title | lower }} 
{{ range .Commits -}}
{{ template "renderCommit" . }} 
{{ end }}
{{ end -}}
{{ end -}}
{{ end -}}

{{ range .Versions }}
<a name="{{ .Tag.Name }}"></a>
## {{ if .Tag.Previous }}[{{ .Tag.Name }}]{{ else }}{{ .Tag.Name }}{{ end }} - {{ datetime "2006-01-02" .Tag.Date }}
{{ range .CommitGroups -}}
### {{ template "commitEmoji" .Title | lower }} 

{{ range .Commits -}}
{{ template "renderCommit" . }} 
{{ end }}
{{ end -}}

{{- if .RevertCommits -}}
### Reverts
{{ range .RevertCommits -}}
- ⏪ {{ .Revert.Header }}
{{ end }}
{{ end -}}

{{- if .MergeCommits -}}
### Pull Requests
{{ range .MergeCommits -}}
- 🔀 {{ .Header }}
{{ end }}
{{ end -}}

{{- if .NoteGroups -}}
{{ range .NoteGroups -}}
### {{ .Title }}
{{ range .Notes }}
{{ .Body }}
{{ end }}
{{ end -}}
{{ end -}}
{{ end -}}

{{- if .Versions }}
[Unreleased]: {{ .Info.RepositoryURL }}/compare/HEAD..{{ $latest := index .Versions 0 }}{{ $latest.Tag.Name }}
{{ range .Versions -}}
{{ if .Tag.Previous -}}
[{{ .Tag.Name }}]: {{ $.Info.RepositoryURL }}/compare/{{ .Tag.Name }}..{{ .Tag.Previous.Name }}
{{ end -}}
{{ end -}}
{{ end -}}
# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

<a name="unreleased"></a>
## [Unreleased]


<a name="v1.6.0"></a>
## [v1.6.0] - 2025-06-11
### 📝 Documentation 

- Add Copilot instructions for monorepo 

### ✨ Features 

- Enhance CQM module, core functionalities, and CLI user role management 
- Refactor RBAC policy validation and improve SonarQube API handling 
- Refactor Jira project retrieval to use DefaultClient 
- Refactor Jira issue creation to use default client 
- Enhance error handling and client request management 
- Refactor Jira project update and SonarQube issue retrieval 
- Comment out cyclomatic complexity check in golangci configuration 
- Enhance CQM module with core repository integration and API error handling 
- Add golangci-lint to mise 
- **frontend:** Improve sidebar styling and add admin link 
- **frontend:** Implement active link highlighting in sidebar 
- **frontend:** Integrate Font Awesome icons in admin index page 
- **frontend:** Integrate FontAwesome icons in Admin Index 
- **frontend:** Close sidebar drawer on link click 
- **frontend:** Implement basic admin index page 
- **grafana:** Update project dashboard with enhanced metrics display and remove obsolete SQL schema 
- **grafana:** Add CQM overview dashboard and trim whitespace from quality gate status 
- **grafana:** Add Grafana dashboard prototype with SonarQube data integration 
- **intranet:** Add write and delete actions to ea-service policy 
- **intranet:** Implement user listing and role update functionalities 
- **intranet:** Integrate SonarQube scanning into CI pipeline 
- **rbac:** Grant write access to ea-service resources 
- **tests:** Add test suite for various packages with error logging refactor(coverage): Update coverage configuration and cleanup tasks 
- **wiki:** Enable Mermaid diagrams and improve code highlighting 
- **wiki:** Add critical score category to code quality metrics 
- **wiki:** Enhance Business Architect page with resources and formatting 
- **wiki:** Enhance Business Architect page with additional resources and templates 
- **wiki:** Add onboarding section and detail collaboration for Business Architects 
- **wiki:** Remove Best Practices and Technical Breakdown pages from Arch Handbook 
- **wiki:** Add Architects Metrics page to Architecture Handbook 

### 🐛 Bug Fixes 

- Improve cache testing and fix race conditions in tests 
- **frontend:** Use <a> tag for logout link to prevent React errors 
- **intranet:** Improve error handling, validation, and logging 
- **intranet:** Allow dynamic page size for user listing 
- **wiki:** Correct typo in Playlists section of Business Architect documentation 

### ♻️ Refactor 

- Enhance error handling with static errors and consistent formatting 
- Improve migration lock handling with reusable function 
- Simplify UpdateUserViewModel by aliasing CreateUserViewModel 
- Rename validator extension package and update usages 
- **intranet:** Refactor code structure for improved readability and maintainability 
- **intranet:** Improve code structure and update dependencies 
- **tests:** Update test configurations for improved reliability and clarity 


<a name="v1.5.0"></a>
## [v1.5.0] - 2025-05-13
### ✨ Features 

- Release v1.5.0 
- **auth:** Enhance user management and authorization 
- **auth:** Implement user provisioning service and integrate with middleware 
- **auth:** Implement User model and repository with CRUD operations 
- **auth:** Implement user provisioning with Cognito JWT data 
- **cache:** Implement DefaultCache with basic cache operations and tests 
- **core:** Implement generic repository functions for CRUD operations 
- **cqm:** Enhance SonarQube issue display with link to SonarQube, effort, author, and tags 
- **devops:** Integrate go test and golangci-lint into sonar scan 
- **frontend:** Implement CLI operator command for user role management 
- **frontend:** Refactor application setup with dependency injection and bootstrap 
- **intranet:** Enhance FindAll function with field selection and count control 
- **intranet:** Implement RBAC policies for admin and enterprise architect roles 
- **intranet:** Implement core cache functionality 
- **intranet:** Decouple config loading from intranet package 
- **intranet:** Enhance Save function with update field control 
- **intranet:** Add first name and last name to user provisioning from JWT 
- **intranet:** Refactor RBAC policy for enhanced flexibility and security 
- **intranet:** Implement user authorization middleware and integrate with frontend 
- **intranet:** Implement RBAC policy for business architect role 
- **rbac:** Enhance RBAC with deny rules, wildcard path matching, and improved validation 
- **rbac:** Implement RBAC policy and validation logic with JSON configuration 

### 🐛 Bug Fixes 

- **app:** Remove TODO comment regarding intranet config loading in production 
- **auth:** Add JWT expiration time validation with clock skew tolerance 
- **cqm:** Correctly process SonarQube analysis measures 
- **devops:** Enhance SonarQube integration and code coverage reporting 
- **devops:** Improve SonarQube analysis and test coverage 

### ♻️ Refactor 

- **intranet:** Rename UserProvisioningService to UserProvisioningInteractor and improve error handling 


<a name="v1.4.0"></a>
## [v1.4.0] - 2025-04-23
### 📝 Documentation 

- **changelog:** Update CHANGELOG.md for v1.4.0 release 
- **standards:** Add Git commits and Go style guides 

### ✨ Features 

- **changelog:** Enhance emoji support for commit types and improve formatting 
- **cqm:** Validate SonarQube webhook branch and add issue search endpoint 


<a name="v1.3.0"></a>
## [v1.3.0] - 2025-04-21
### 👷 CI 

- **changelog:** Add automatic CHANGELOG generation, based on conventional commits (https://www.conventionalcommits.org/) 

### 📝 Documentation 

- **changelog:** Update CHANGELOG.md 

### ✨ Features 

- **dependency:** Add git-chglog tool 
- **web:** Add SonarQube webhook 


<a name="v1.2.0"></a>
## [v1.2.0] - 2025-04-15

<a name="v1.1.0"></a>
## [v1.1.0] - 2025-04-10

<a name="v1.0.0"></a>
## v1.0.0 - 2025-04-10

[Unreleased]: /compare/HEAD..v1.6.0
[v1.6.0]: /compare/v1.6.0..v1.5.0
[v1.5.0]: /compare/v1.5.0..v1.4.0
[v1.4.0]: /compare/v1.4.0..v1.3.0
[v1.3.0]: /compare/v1.3.0..v1.2.0
[v1.2.0]: /compare/v1.2.0..v1.1.0
[v1.1.0]: /compare/v1.1.0..v1.0.0

<!DOCTYPE html>
<html lang="en" dir="ltr" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Add preload hints -->
    <link rel="preload" href="/build/assets/app.css" as="style">
    <link rel="preload" href="/build/assets/app.js" as="script">
    <link rel="preload" href="/build/assets/Index.js" as="script">
    
    <!-- Add Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link href="/build/assets/app.css" rel="stylesheet" type="text/css" /> 
        
    {{ .inertiaHead }}

    {{ if .hmr }}

    <script type="module">
        import RefreshRuntime from '{{ vite "@react-refresh" }}'
        RefreshRuntime.injectIntoGlobalHook(window)
        window.$RefreshReg$ = () => { }
        window.$RefreshSig$ = () => (type) => type
        window.__vite_plugin_react_preamble_installed__ = true
    </script>
    {{ end }}
</head>

<body class="font-sans antialiased">
    {{ .inertia }}
    <script defer type="module" src="{{ vite "resources/js/app.jsx" }}"></script>
</body>

</html>

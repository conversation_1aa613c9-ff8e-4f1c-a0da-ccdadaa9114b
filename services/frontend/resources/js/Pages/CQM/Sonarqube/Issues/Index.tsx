import { usePage } from '@inertiajs/react';
import { router } from '@inertiajs/react'
import React, { useState, useEffect } from 'react';
import Paginator from '@/components/Paginator';
import Breadcrumbs from '@/components/Breadcrumbs';

interface TextRange {
  startLine: number;
  endLine: number;
  startOffset: number;
  endOffset: number;
}

interface Location {
  component?: string;
  textRange: TextRange;
  message?: string;
}

interface Flow {
  locations?: Location[];
}

interface MessageFormatting {
  start: number;
  end: number;
  type: string; // Could be "CODE", "STRONG", etc.
}

interface Issue {
  key: string;
  rule: string;
  severity: string;
  component: string;
  project: string;
  line?: number;
  hash?: string;
  status: string;
  message: string;
  type: string;
  creationDate: string;
  updateDate: string;
  tags: string[];
  author?: string;
  effort?: string;
  debt?: string;
  resolution?: string;
  closeDate?: string;
  textRange?: TextRange;
  flows: Flow[];
  scope?: string;
  quickFixAvailable?: boolean;
  messageFormattings: MessageFormatting[];
  url?: string;
  sonarURL?: string;
  jiraIssueKey?: string;
}


interface Pagination {
  totalCount : number;
  currentPage : number;
  perPage: number;
}

interface JiraProject {
  id: string;
  projectKey: string;
  name: string;
  
}


// Define the page props interface
interface PageProps {
  projectID: string;
  issues: Issue[];
  errors: string[];
  updatedIssues: Issue[];
  pagination: Pagination;
  jiraProject: JiraProject;
  [key: string]: any; // Add an index signature to satisfy the constraint
}

export default function Issues() {
  const [currentPage, setCurrentPage] = useState(1);
  // Use the Page type from Inertia
  const page = usePage<PageProps>();
  const { projectID, errors, issues, updatedIssues, pagination, jiraProject } = page.props;

  // Initialize local state from props
  const [sonarIssues, setSonarIssues] = useState(issues);
  const [errorsState, setErrors] = useState(errors);
  // Add loading state to track which issues are being processed
  const [loadingIssues, setLoadingIssues] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const pageParam = params.get('page');
    if (pageParam) {
      setCurrentPage(Number(pageParam));
    }
  }, []);


  // Detect changes in errors prop
  useEffect(() => {
    setSonarIssues(issues);
  }, [issues]);


  // Detect changes in errors prop
  useEffect(() => {
    setErrors(errors);
  }, [errors]);

  // Detect changes in errors prop
  useEffect(() => {
    setSonarIssues(issues);
  }, [issues]);

  useEffect(() => {
    const issues = sonarIssues.map((issue) => {
      const updatedIssue = Array.from(updatedIssues).find((updated) => updated.key === issue.key);
      if (updatedIssue) {
        Object.assign(issue, updatedIssue);
      }
      return issue;
    })
    setSonarIssues(issues);
  }, [updatedIssues]);

  // Function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Function to get severity badge class
  const getSeverityBadgeClass = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'blocker':
        return 'badge-error';
      case 'critical':
        return 'badge-error';
      case 'major':
        return 'badge-warning';
      case 'minor':
        return 'badge-info';
      case 'info':
        return 'badge-ghost';
      default:
        return 'badge-secondary';
    }
  };

  // Function to handle creating Jira issue
  const handleCreateJiraIssue = (issueKey: string) => {
    // Set loading state for this specific issue
    setLoadingIssues(prev => ({ ...prev, [issueKey]: true }));
    
    router.post(
      `/cqm/sonarqube/projects/${projectID}/issues/${issueKey}`,
      {},
      {
        only: ['errors', 'updatedIssues'],
        onSuccess: (opts) => {
          console.log('Issue created successfully:', opts);
        },
        onFinish: (opts) => {
          console.log('Issue created Finished', opts);
          setSonarIssues(issues);
          // Remove loading state when finished
          setLoadingIssues(prev => ({ ...prev, [issueKey]: false }));
        },
        onError: (errors) => {
          console.error('Error:', errors);
          // Remove loading state on error too
          setLoadingIssues(prev => ({ ...prev, [issueKey]: false }));
        },
      }
    );
  };

  return (
    <div className="container mx-auto p-4">
      <Breadcrumbs 
        items={[
          { title: 'Home', href: '/' },
          { title: 'SonarQube Projects', href: '/cqm/sonarqube/projects' },
          { title: 'Issues' }
        ]} 
      />
      <h1 className="text-2xl font-bold mb-6">Jira project: {jiraProject.name} ({jiraProject.projectKey})</h1>
      <h2 className="text-1xl font-bold mb-6">SonarQube issues ({pagination.totalCount})</h2>
      

      {/* Error Rendering */}
      {errorsState.length > 0 && (
        <div className="alert alert-error mt-4">
          <ul>
            {errorsState.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr className="bg-base-200">
              <th className="text-left">Severity</th>
              <th className="text-left">Message</th>
              <th className="text-left">Effort</th>
              <th className="text-left">Status</th>
              <th className="text-left">Author</th>
              <th className="text-left">Tags</th>
              <th className="text-left">Updated</th>
              <th className="text-left">Jira Issue</th>
            </tr>
          </thead>
          <tbody>
            {sonarIssues.map((issue: Issue) => (
              <tr key={issue.key} className="hover">
                <td>
                  <span className={`badge ${getSeverityBadgeClass(issue.severity)}`}>
                    {issue.severity}
                  </span>
                </td>
                <td className="max-w-md truncate">
                  
                  <a className="" target="_blank" href={`${issue.sonarURL}`}>
                    {issue.message}
                  </a>
                </td>
                <td className="font-mono text-xs truncate max-w-xs">
                  {issue.effort === '' ? '--' : issue.effort}
                </td>
                <td>
                  <span className={`badge ${issue.status === 'OPEN' ? 'badge-warning' : 'badge-success'}`}>
                    {issue.status}
                  </span>
                </td>
                <td>{issue.author === '' ? '--' : issue.author}</td>
                <td>{issue.tags}</td>
                <td>{formatDate(issue.updateDate)}</td>
                <td>

                  {!issue.jiraIssueKey ? (
                    
                    <button
                      onClick={() => handleCreateJiraIssue(issue.key)}
                      className="btn btn-sm btn-primary"
                      disabled={loadingIssues[issue.key]}
                    >
                      {loadingIssues[issue.key] ? 'Creating...' : 'Create'}
                    </button>
                  ) : (
                    <a className="btn btn-sm btn-secondary" target="_blank" href={`${issue.url}`}>{issue.jiraIssueKey}</a>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
      </div>

      <div className="flex justify-center mt-4">
          <Paginator
            currentPage={currentPage}
            totalItems={pagination.totalCount}
            itemsPerPage={pagination.perPage}
            firstNumberOfPages={3}
            lastNumberOfPages={1}
            onPageChange={(nextPage) => {
              router.get(`/cqm/sonarqube/projects/${projectID}/issues`, { page: nextPage }, { only: ['issues', 'errors'] })
              setCurrentPage(nextPage)
            }}
          />
        </div>

      {sonarIssues.length === 0 && (
        <div className="alert alert-info mt-4">
          <div className="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-6 h-6 mx-2 stroke-current">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <label>No issues found.</label>
          </div>
        </div>
      )}

    </div>
  );
}

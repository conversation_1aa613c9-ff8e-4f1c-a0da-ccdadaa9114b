import React from 'react';
import { Link } from '@inertiajs/react';
import Breadcrumbs from '@/components/Breadcrumbs';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers } from '@fortawesome/free-solid-svg-icons';


interface AdminSection {
  title: string;
  description: string;
  icon: any; // Changed to accept FontAwesome icon objects
  href: string;
}

export default function Admin() {
  // Define admin sections - easy to add more in the future
  const adminSections: AdminSection[] = [
    {
      title: 'User Management',
      description: 'Manage users, roles and permissions',
      icon: faUsers,
      href: '/admin/users',
    },
  ];

  return (
    <div className='container mx-auto p-4'>
      <Breadcrumbs items={[{ title: 'Home', href: '/' }, { title: 'Admin' }]} />

      <div className="mt-6">
        <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {adminSections.map((section) => (
            <Link
              key={section.title}
              href={section.href}
              
              className="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300"
            >
              <div className="card-body">
                <div className="flex items-center gap-4">
                  <div className="bg-opacity-10 p-3 rounded-lg text-primary">
                    <FontAwesomeIcon key={section.title} icon={section.icon} className="text-2xl" />
                  </div>
                  <div>
                    <h2 className="card-title">{section.title}</h2>
                    <p className="text-sm opacity-70">{section.description}</p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}

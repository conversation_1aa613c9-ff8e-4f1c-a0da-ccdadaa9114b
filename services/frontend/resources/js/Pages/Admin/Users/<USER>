import React, { useState } from 'react';
import { usePage, router } from '@inertiajs/react';
import { ColumnProps } from '@/Types/ColumnProps';
import Table from '@/components/Table';
import Breadcrumbs from '@/components/Breadcrumbs';
import { Pagination } from '@/Types/Pagination';

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  first_name: string;
  last_name: string;
}

interface Role {
  id: string;
  name: string;
}

interface Error {
  message: string;
  code: string;
}


// Define the page props interface
interface PageProps {
  users: User[];
  usersPagination: Pagination;
  errors: Error[];
  roles: Role[];
  [key: string]: any; // Add an index signature to satisfy the constraint
}

export default function Users() {
  const page = usePage<PageProps>();
  const queryParams = new URLSearchParams(page.url.split('?')[1]);
  const search = queryParams.get('email') || '';
  const pageParam = queryParams.get('page') || 1;
  const { errors, users, usersPagination, roles, updateError } = page.props;
  const [searchQuery, setSearchQuery] = useState<string>(search);
  const [currentPage, setCurrentPage] = useState<number>(pageParam);

  const [localUsers, setLocalUsers] = React.useState<User[]>(users);

  React.useEffect(() => {
    setLocalUsers(users);
  }, [users]);

  const handleRoleChange = (row: User, event: React.ChangeEvent<HTMLSelectElement>) => {
    const userId = row.id;
    const newRole = event.target.value;

    // Update local state immediately for responsive UI
    setLocalUsers(prevUsers =>
      prevUsers.map(user => {
        if (user.id === userId) {
          user.role !== newRole && (user.role = newRole);
        }
        return user;
      }

      )
    );

    router.put(
      `/admin/users/${userId}/role`,
      { role: newRole },
      {
        only: ['updateError'],
      }
    );
  };

  const searchConfig = {
    searchQuery: searchQuery,
    placeholder: 'Search by user email',
    onSearchChange: (query: string) => {
      setCurrentPage(1);
      setSearchQuery(query);
      fetchUsers(query, 1);
    },
  };

  // Fetch projects from the server
  function fetchUsers(query: string, page: number) {
    router.get('/admin/users', { email: query, page: page }, { preserveState: true, replace: true });
  }


  // Define table columns
  const columns: Array<ColumnProps<User>> = [
    { key: 'id', title: 'ID' },
    { key: 'username', title: 'Username' },
    { key: 'email', title: 'Email' },
    { key: 'role', title: 'Role' },
    { key: 'first_name', title: 'First Name' },
    { key: 'last_name', title: 'Last Name' },
    {
      key: 'actions',
      title: 'Actions',
      render: (column, row) => (
        <div className='flex flex-col gap-1'>
          <select
            className="border rounded px-2 py-1"
            value={row.role}
            onChange={(e) => handleRoleChange(row, e)}
          >
            {roles && roles.map((role: Role) => (
              <option key={role.id} value={role.id}>
                {role.name}
              </option>
            ))}
          </select>
          {updateError && updateError.id === row.id && (
            <div className="text-red-500 text-sm">{updateError.message}</div>
          )}
        </div>
      ),
    },
  ];


  const pageChanged = (page: number) => {
    setCurrentPage(page);
    fetchUsers(searchQuery, page);
  };


  return (
    <div className='container mx-auto p-4'>
      <Breadcrumbs items={[{ title: 'Home', href: '/' }, { title: 'Admin', href: '/admin' }, { title: 'Users' }]} />
      <h1 className='text-2xl font-bold mb-6'>Users ({page.props.usersPagination.totalCount})</h1>
      <Table data={page.props.users} columns={columns} searchConfig={searchConfig} pagination={page.props.usersPagination} onPageChange={(page: number) => pageChanged(page)}
        currentPage={currentPage} />

    </div>
  );
}

import Button from '@/components/Button';
import TextFieldNew from '@/components/fields/TextField/TextFieldNew';
import { CompanyClient } from '@/Types/sonarProjects';
import { useForm } from '@inertiajs/react';
import React, { useEffect } from 'react';

interface CompanyClientFormProps {
  companyClient?: CompanyClient;
  onCreate?: (client: CompanyClient) => void;
  onUpdate?: (client: CompanyClient) => void;
}
const CompanyClientForm = (props: CompanyClientFormProps) => {
  const { companyClient, onCreate, onUpdate } = props;
  const { data, setData, post, processing, errors, put } = useForm({
    name: '',
  });
  const isFormValid = data.name.trim() !== '';

  const closeClientForm = (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.preventDefault();
    document.getElementById('companyClientForm').classList.add('hidden');
    document.getElementById('companyClientForm').classList.remove('flex');
  };

  const submitClientForm = (event: React.FormEvent) => {
    event.preventDefault();
    if (companyClient) {
      put(`/cqm/company/clients/${companyClient.id}`, {
        only: ['errors', 'updatedCompanyClient'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        onSuccess: (opts) => {
          closeClientForm();
          onUpdate?.(opts.props.updatedCompanyClient as CompanyClient);
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    } else {
      post(`/cqm/company/clients`, {
        only: ['errors', 'createdCompanyClient'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        onSuccess: (opts) => {
          closeClientForm();
          onCreate?.(opts.props.createdCompanyClient as CompanyClient);
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    }
  };

  // Set the form data if companyClient is provided
  useEffect(() => {
    setData('name', companyClient?.name || '');
  }, [companyClient]);

  return (
    <form
      className='flex-col gap-2 hidden border rounded-lg p-2 w-full'
      id='companyClientForm'
      onSubmit={(event: React.FormEvent) => submitClientForm(event)}
    >
      <div className='flex flex-row gap-2 items-center mb-2'>
        <div className='flex flex-col gap-2 w-1/2'>
          <TextFieldNew
            label='Client Name'
            name='name'
            placeholder='Client Name'
            className='w-full'
            type='text'
            errors={[errors.name]}
            value={data.name}
            required={true}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('name', e.target.value)}
          />
        </div>
      </div>
      <div className='flex flex-row gap-2 justify-end w-full mt-4'>
        <Button
          label='Close'
          className='btn-secondary shrink'
          onClick={(event: React.MouseEvent<HTMLButtonElement>) => closeClientForm(event)}
        />
        <Button
          label={companyClient ? 'Update' : 'Save'}
          loadingLabel={companyClient ? 'Updating...' : 'Saving...'}
          processing={processing}
          disabled={!isFormValid}
          className='btn-primary'
          type='submit'
          form='companyClientForm'
        />
      </div>
    </form>
  );
};

export default CompanyClientForm;

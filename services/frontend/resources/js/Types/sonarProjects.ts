export interface JiraProject {
  id: string;
  name: string;
  jiraURL: string;
  projectKey: string;
  username: string;
  active: boolean;
  companyClient: CompanyClient;
}

export interface CompanyClient {
  id: string;
  name: string;
}

export interface Project {
  id: string;
  projectName: string;
  projectKey: string;
  jiraProject: JiraProject;
  branch: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}
import { renderToString } from 'react-dom/server';
import { createInertiaApp } from '@inertiajs/react';
import createServer from '@inertiajs/react/server';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import Layout from './components/Layout';
import '../css/app.css'
import setup from './setup';
// import { hydrateRoot } from 'react-dom/client'



createServer((page) =>
    createInertiaApp({
        resolve: (name) => resolvePageComponent(`./Pages/${name}.tsx`, import.meta.glob('./Pages/**/*.tsx')),
        page,
        render: renderToString,
        setup({ App, props }) {
            // return hydrateRoot(el, 
            //     <Layout>
            //         <App {...props} />
            //     </Layout>
            // )
            return (
                <Layout>
                    <App {...props} />
                </Layout>
            )
        },
        ...setup.options,
    })
);

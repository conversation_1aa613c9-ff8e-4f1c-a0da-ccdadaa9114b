import React, { useState, useEffect } from 'react';
import { ColumnProps } from '@/Types/ColumnProps';
import { useDebounce } from '@/utils/debounce';
import Paginator from './Paginator';
import { Pagination } from '@/Types/Pagination';

type Props<T> = {
  columns: Array<ColumnProps<T>>;
  data?: T[];
  searchConfig?: SearchConfig;
  pagination?: Pagination;
  onPageChange?: (page: number) => void;
  currentPage?: number;
};

interface SearchConfig {
  placeholder?: string;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
}

const getNestedValue = (obj: any, path: string) => path.split('.').reduce((acc, key) => acc?.[key], obj);

const Table = <T,>({ data, columns, searchConfig, pagination, onPageChange, currentPage }: Props<T>) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchConfig.searchQuery || '');

  const debouncedSearchQuery = useDebounce(localSearchQuery, 500);

  useEffect(() => {
    // Call the handleSearchChange function from the searchConfig with the debounced query
    if (debouncedSearchQuery !== searchConfig.searchQuery) {
      searchConfig.onSearchChange(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, searchConfig.searchQuery, searchConfig]);

  const headers = columns.map((column, index) => {
    return (
      <th key={`headCell-${column.key}`} className='text-left'>
        {column.title}
      </th>
    );
  });

  const rows = !data?.length ? (
    <tr>
      <td colSpan={columns.length} className='text-center'>
        No data
      </td>
    </tr>
  ) : (
    data?.map((row, index) => {
      return (
        <tr key={`row-${Object.values(row)[0]}`}>
          {columns.map((column) => {
            const value = column.render ? column.render(column, row) : (getNestedValue(row, column.key) as string);

            return <td key={`cell-${column.key}`}>{value}</td>;
          })}
        </tr>
      );
    })
  );

  return (
    <div className='flex flex-col gap-2'>
      <div className='overflow-x-auto'>
        {searchConfig && (
          <div className='mb-4'>
            <input
              type='text'
              className='input input-bordered w-full'
              placeholder={searchConfig.placeholder || 'Search...'}
              value={localSearchQuery}
              onChange={(e) => setLocalSearchQuery(e.target.value)}
            />
          </div>
        )}
        <table className='table table-zebra w-full'>
          <thead>
            <tr className='bg-base-200'>{headers}</tr>
          </thead>

          <tbody>{rows}</tbody>
        </table>
      </div>
      {pagination && (
        <div className='self-center'>
          <Paginator
            currentPage={currentPage}
            totalItems={pagination.totalCount}
            itemsPerPage={pagination.perPage}
            firstNumberOfPages={3}
            lastNumberOfPages={1}
            onPageChange={(nextPage: number) => {
              onPageChange?.(nextPage);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default Table;

import React from 'react';
import 'daisyui/dist/full.css'; // Ensure DaisyUI styles are available in your project

interface ButtonProps {
  label: string;
  loadingLabel?: string;
  onClick: React.MouseEventHandler<HTMLButtonElement>;
  processing?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode; // Accepts icon as a ReactNode, so we can pass Heroicons easily
  className?: string;
  type?: 'button' | 'submit' | 'reset'; // Support for different button types
  tooltip?: string; // Tooltip message for the button
  form?: string; // Form ID for the button
}

const Button: React.FC<ButtonProps> = ({
  label,
  loadingLabel,
  onClick,
  processing = false,
  disabled = false,
  icon,
  className = '',
  type = 'button',
  form = '',
  tooltip,
}) => {
  return (
    <button
      type={type}
      className={`btn ${className} ${disabled || processing ? 'opacity-50 cursor-not-allowed' : ''} ${tooltip ? 'tooltip' : ''}`}
      onClick={onClick}
      disabled={disabled || processing}
      data-tip={tooltip}
      form={form}
    >
      {processing ? (
        <>{loadingLabel || 'Processing...'}</>
      ) : (
        <>
          {icon && <span className='flex items-center'>{icon}</span>}
          {label && <span className='text-sm font-semibold'>{label}</span>}
        </>
      )}
    </button>
  );
};

export default Button;

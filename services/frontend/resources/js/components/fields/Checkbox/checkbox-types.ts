import type { ForwardedRef, HTMLAttributes } from 'react';

export interface CheckboxProps
  extends Omit<HTMLAttributes<HTMLInputElement>, 'onChange' | 'checked' | 'disabled' | 'type'> {
  label?: string;
  checked?: boolean;
  readOnly?: boolean;
  setChecked?: (checked: boolean) => void;
  disabled?: boolean;
  onChange?: (checked: boolean) => void;
  errors?: string[];
  inputRef?: ForwardedRef<HTMLInputElement>;
  containerProps?: HTMLAttributes<HTMLDivElement>;
  labelProps?: HTMLAttributes<HTMLLabelElement>;
  inputProps?: HTMLAttributes<HTMLInputElement>;
}
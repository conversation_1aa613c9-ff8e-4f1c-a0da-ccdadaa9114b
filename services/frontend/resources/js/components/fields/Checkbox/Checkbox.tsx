// src/components/Checkbox/Checkbox.tsx
import React, { useId } from 'react';
import { CheckboxProps } from './checkbox-types';

const Checkbox = (props: CheckboxProps) => {
  const { id, label, checked, disabled, onChange, containerProps, labelProps, inputProps, readOnly } = props;

  const idByUseId = useId().replaceAll(/\W+/g, '');
  const htmlFor = id || idByUseId;
  const { className: _className, ...inputPropsWithoutClassName } = inputProps || {};

  return (
    <div {...containerProps} className={`flex items-center gap-2 ${containerProps?.className}`}>
      <input
        id={htmlFor}
        type='checkbox'
        checked={checked}
        onChange={onChange}
        readOnly={readOnly}
        disabled={disabled}
        className={`checkbox checkbox-primary ${inputProps?.className} ${readOnly ? 'pointer-events-none' : ''}`}
        {...inputPropsWithoutClassName}
      />
      {label && (
        <span {...labelProps} htmlFor={htmlFor}>
          {label}
        </span>
      )}
    </div>
  );
};

export default Checkbox;

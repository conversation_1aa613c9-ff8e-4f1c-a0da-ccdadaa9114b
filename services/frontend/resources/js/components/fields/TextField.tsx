import React, { useId } from 'react';

interface ValidationRules {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
}

interface TextFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'search';
  placeholder?: string;
  containerClassName?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  errors?: string[];
  disabled?: boolean;
  required?: boolean;
  rows?: number;
  readOnly?: boolean;
}

const TextField: React.FC<TextFieldProps> = ({
  label,
  id,
  name,
  type = 'text',
  placeholder = '',
  containerClassName = '',
  value,
  onChange,
  errors = [],
  disabled = false,
  required = false,
  readOnly = false,
  rows = 1,
}) => {
  const idByUseId = useId().replaceAll(/\W+/g, '');
  const htmlFor = id || name || idByUseId;
  const isTextarea = rows > 1;
  const Element = isTextarea ? 'textarea' : 'input';

  return (
    <fieldset className={`flex flex-col space-y-1 ${containerClassName}`}>
      <legend className='text-sm font-semibold text-gray-700'>
        {label} {required ? <span className='pl-1 text-red-600'>*</span> : undefined}
      </legend>

      <Element
        id={htmlFor}
        name={name}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        autoComplete='off'
        disabled={disabled}
        required={required}
        rows={rows}
        className='input input-bordered w-full'
        readOnly={readOnly}
      />

      {errors.length > 0 && (
        <div className='text-sm text-red-600 space-y-1'>
          {errors.map((error, index) => (
            <p key={`${name}-${error}`}>{error}</p>
          ))}
        </div>
      )}
    </fieldset>
  );
};

export default TextField;

import type { ForwardedRef, HTMLAttributes, ReactNode, TextareaHTMLAttributes } from 'react';


export type InputBase = {
  label?: ReactNode;
  errors?: string[];
  helper?: ReactNode;
  endAdornment?: ReactNode;
  startAdornment?: ReactNode;
  sideAdornment?: ReactNode;
  fixedHeightWithHelperText?: boolean;
  inputRef?: ForwardedRef<HTMLTextAreaElement & HTMLInputElement>;
  type?: HTMLInputElement['type'];
};

export type InputProps = {
  containerProps?: HTMLAttributes<HTMLDivElement>;
  helperProps?: HTMLAttributes<HTMLDivElement>;
  inputContainerProps?: HTMLAttributes<HTMLDivElement>;
  labelProps?: HTMLAttributes<HTMLLabelElement>;
  startAdornmentProps?: HTMLAttributes<HTMLDivElement>;
  endAdornmentProps?: HTMLAttributes<HTMLDivElement>;
  sideContainerProps?: HTMLAttributes<HTMLDivElement>;
} & InputBase &
  Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'>;
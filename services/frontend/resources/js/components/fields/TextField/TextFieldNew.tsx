import React, { useId, ForwardedRef } from 'react';
import { InputProps } from './input-types';

const TextFieldNew = (props: InputProps) => {
  const {
    id,
    type,
    size,
    name,
    errors = [],
    value,
    resize,
    loading,
    inputRef,
    required,
    className,
    labelProps,
    helperProps,
    endAdornment,
    sideAdornment,
    startAdornment,
    containerProps,
    endAdornmentProps,
    sideContainerProps,
    startAdornmentProps,
    inputContainerProps,
    fixedHeightWithHelperText,
    rows = 1,
    label: labelProp,
    helper: helperProp,
    disabled: disabledProp,
    ...restOfProps
  } = props;

  const idByUseId = useId().replaceAll(/\W+/g, '');
  const htmlFor = id || name || idByUseId;
  const disabled = disabledProp || loading;
  const isTextarea = rows > 1;
  const Element = isTextarea ? 'textarea' : 'input';

  return (
    <div className={`group flex flex-col text-gray-950 ${className}`} {...containerProps}>
      {labelProp ? (
        <label htmlFor={htmlFor} {...labelProps} className={labelProps?.className}>
          {labelProp}

          {required ? <span className='text-red-500'>*</span> : undefined}
        </label>
      ) : undefined}

      <div
        {...inputContainerProps}
        className={`flex items-center gap-2 rounded-lg bg-white outline outline-1 -outline-offset-1 outline-gray-400 hover:outline-gray-500 px-4 py-2 ${inputContainerProps?.className}`}
      >
        {startAdornment ? (
          <div
            {...endAdornmentProps}
            className={`flex items-center justify-center fill-inherit text-inherit ${startAdornmentProps?.className}`}
          >
            {startAdornment}
          </div>
        ) : undefined}

        <div {...sideContainerProps} className={`flex flex-1 gap-1 ${sideContainerProps?.className}`}>
          {sideAdornment}
          <Element
            id={htmlFor}
            name={name}
            type={type}
            {...(restOfProps as ForwardedRef<HTMLInputElement>)}
            value={value}
            autoComplete='off'
            disabled={disabled}
            required={required}
            rows={rows}
            className='w-full text-ellipsis bg-transparent leading-6 text-inherit placeholder-gray-200 outline-none disabled:opacity-50'
          />
          {endAdornment ? (
            <div
              {...endAdornmentProps}
              className={`flex items-center justify-center fill-inherit text-inherit ${endAdornmentProps?.className}`}
            >
              {endAdornment}
            </div>
          ) : undefined}
        </div>
      </div>

      {errors.length > 0 && (
        <div className='text-sm text-red-600 space-y-1'>
          {errors.map((error, index) => (
            <p key={`${name}-${error}`}>{error}</p>
          ))}
        </div>
      )}
    </div>
  );
};

export default TextFieldNew;

import type { InputProps } from '../TextField/input-types';

export type SelectOption<TData> = {
  name: string;
  id: string;
  data?: TData;
};

export type SelectMultipleProps<TData> = Omit<InputProps, 'autoComplete' | 'value' | 'setValue'> & {
  autoComplete?: boolean;
  valueKey?: keyof TData;
  options: SelectOption<TData>[];
  value: SelectOption<TData>[];
  setValue?: (value: SelectOption<TData>[]) => void;
  onSelectOption?: (option: SelectOption<TData>) => void;
  onInputChange?: (value: string) => void;
  onClearAll?: () => void;
  transformOptionName?: (option: SelectOption<TData>) => string;
};
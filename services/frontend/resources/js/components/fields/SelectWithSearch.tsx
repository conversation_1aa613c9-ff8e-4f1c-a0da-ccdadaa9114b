import { useDebounce } from '@/utils/debounce';
import React, { useState, useEffect, useRef } from 'react';

interface Option {
  id: string | number;
  name: string;
}

interface SelectWithSearchProps {
  label: string;
  name: string;
  placeholder?: string;
  value?: string | number;
  onChange: (value: string | number) => void;
  fetchOptions: (searchTerm: string) => Promise<Option[]>;
  renderOption?: (option: Option) => React.ReactNode;
  errors?: string[];
}

const SelectWithSearch: React.FC<SelectWithSearchProps> = ({
  label,
  name,
  placeholder = 'Search...',
  value,
  onChange,
  fetchOptions,
  renderOption = (option) => option.name,
  errors = [],
}) => {
  const [options, setOptions] = useState<Option[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  // Use the debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // :TODO: only search on real ineraction
    // currently fetches on every isOpen change (click in the input)
    if (!isOpen) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await fetchOptions(debouncedSearchTerm);
        setOptions(data);
      } catch (error) {
        console.error('Error fetching options:', error);
      }
      setLoading(false);
    };

    fetchData();
  }, [debouncedSearchTerm, fetchOptions, isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sync selected value when `value` changes externally
  useEffect(() => {
    const newSelected = options.find((opt) => opt.id === value) || null;
    setSelectedOption(newSelected);
  }, [value, options]);

  // Handle option selection
  const handleSelect = (option: Option) => {
    setSelectedOption(option);
    onChange(option); // Update parent state
    setSearchTerm(option.name); // Keep input updated with selection
    setIsOpen(false);
  };

  const renderData = (
    options: Option[],
    selectedOption: Option | null,
    handleSelect: (option: Option) => void,
    renderOption: (option: Option) => React.ReactNode
  ) => {
    return options.map((option) => (
      <li key={option.id} className={`p-2 cursor-pointer ${selectedOption?.id === option.id ? 'bg-gray-300' : 'hover:bg-gray-200'}`}>
        <button type='button' onClick={() => handleSelect(option)} className='w-full text-left'>
          {renderOption(option)}
        </button>
      </li>
    ));
  };

  let dropdownContent;

  if (loading) {
    dropdownContent = <li className='p-2'>Loading...</li>;
  } else if (options.length === 0) {
    dropdownContent = <li className='p-2'>No options found</li>;
  } else {
    dropdownContent = renderData(options, selectedOption, handleSelect, renderOption);
  }

  return (
    <div ref={containerRef} className='relative flex flex-col space-y-1 w-full'>
      {label && <label className='text-sm font-semibold text-gray-700'>{label}</label>}

      {/* Input field for search and selected value */}
      <input
        type='text'
        id={`${name}-search`}
        placeholder={placeholder}
        className='input input-bordered w-full'
        value={searchTerm}
        onFocus={() => setIsOpen(true)}
        onChange={(e) => {
          setSearchTerm(e.target.value);
          setIsOpen(true);
          setSelectedOption(null);
          onChange(null);
        }}
      />

      {/* Dropdown list */}
      {isOpen && (
        <ul className='absolute top-full z-10 bg-white w-full border border-gray-300 mt-1 max-h-60 overflow-y-auto'>{dropdownContent}</ul>
      )}

      {/* Display errors if any */}
      {errors.length > 0 && (
        <div className='text-sm text-red-600 space-y-1'>
          {errors.map((error, index) => (
            <p key={`${name}-${error}`}>{error}</p>
          ))}
        </div>
      )}
    </div>
  );
};

export default SelectWithSearch;

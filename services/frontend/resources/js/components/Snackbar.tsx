import React, { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, InformationCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const commonIconClass = 'w-6 h-6 self-start stroke-2';
// Define a type for Snackbar types
type SnackbarType = 'success' | 'error' | 'info' | 'warning';

const iconMap = {
  success: {
    icon: <CheckCircleIcon className={commonIconClass} />,
    bg: 'bg-green-100',
    title: 'Success',
    text: 'text-green-800',
  },
  error: { icon: <XCircleIcon className={commonIconClass} />, bg: 'bg-red-100', title: 'Error', text: 'text-red-800' },
  info: { icon: <InformationCircleIcon className={commonIconClass} />, bg: 'bg-blue-100', title: 'Info', text: 'text-blue-800' },
  warning: {
    icon: <ExclamationTriangleIcon className={commonIconClass} />,
    bg: 'bg-yellow-100',
    title: 'Warning',
    text: 'text-yellow-800',
  },
};

interface SnackbarProps {
  title?: string;
  message: string;
  type?: SnackbarType;
  duration?: number;
  autoDismiss?: boolean;
  uniqueKey: number;
  onClose?: () => void;
}

const Snackbar = ({ title, message, type = 'success', duration = 3000, autoDismiss = true, onClose, uniqueKey }: SnackbarProps) => {
  const [visible, setVisible] = useState(false);

  const handleExit = () => {
    setVisible(false);
    onClose?.();
  };

  useEffect(() => {
    setVisible(true);
  }, [message, uniqueKey]);

  useEffect(() => {
    if (autoDismiss) {
      const timer = setTimeout(() => {
        handleExit();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [message, uniqueKey, autoDismiss, duration, onClose]);

  if (!visible) return null;

  return (
    <div
      className={`mx-auto mt-4 py-4 flex justify-between items-center rounded-md ${iconMap[type].bg} ${iconMap[type].text}  w-[90%] max-w-[1280px] px-4 sm:px-6.5 shadow-[0px_0px_8px_rgba(0,0,0,0.16)] left-1/2 -translate-x-1/2 fixed z-50 flex-row`}
    >
      <div className='flex items-start justify-between gap-2 mr-auto'>
        <div>{iconMap[type].icon}</div>
        <div className='flex flex-col'>
          <div className='font-bold text-base'>{title || iconMap[type].title}</div>
          <div className='description text-base'>{message}</div>
        </div>
      </div>
      <div className='flex ml-auto'>
        {/* change later to a reusable button */}
        <button className='bg-transparent font-bold' onClick={() => handleExit()}>
          Dismiss
        </button>
      </div>
    </div>
  );
};

export default Snackbar;

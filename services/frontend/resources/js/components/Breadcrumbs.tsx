import React from 'react';
import { Link } from '@inertiajs/react';

interface BreadcrumbItem {
  title: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  return (
    <div className="text-sm breadcrumbs mb-4">
      <ul>
        {items.map((item, index) => (
          <li key={index}>
            {item.href ? (
              <Link href={item.href} className="text-base-content">
                {item.title}
              </Link>
            ) : (
              <span className="text-base-content">{item.title}</span>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Breadcrumbs;
  
import React from 'react';


interface PaginatorProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  firstNumberOfPages: number;
  lastNumberOfPages: number;
  onPageChange: (page: number) => void;
}

const Paginator: React.FC<PaginatorProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  firstNumberOfPages,
  lastNumberOfPages,
  onPageChange,
}) => {
  const currentPageNumber = Number(currentPage);
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const renderPageNumbers = () => {
    const pages = [];
    let leftSide = Math.max(1, currentPageNumber - Math.floor(firstNumberOfPages / 2));
    let rightSide = Math.min(totalPages, leftSide + firstNumberOfPages - 1);

    if (rightSide === totalPages) {
      leftSide = Math.max(1, rightSide - firstNumberOfPages + 1);
    }

    for (let i = leftSide; i <= rightSide; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`btn btn-ghost min-w-[40px] ${
            currentPageNumber === i ? 'font-bold' : ''
          }`}
        >
          {i}
        </button>
      );
    }

    if (rightSide < totalPages - lastNumberOfPages) {
      pages.push(
        <span key="dots-right" className="px-2">
          ...
        </span>
      );
    }

    if (rightSide < totalPages) {
      for (let i = Math.max(rightSide + 1, totalPages - lastNumberOfPages + 1);
           i <= totalPages; i++) {
        pages.push(
          <button
            key={i}
            onClick={() => onPageChange(i)}
            className={`btn btn-ghost min-w-[40px] ${
              currentPageNumber === i ? 'font-bold' : ''
            }`}
          >
            {i}
          </button>
        );
      }
    }

    return pages;
  };

  if (totalItems === 0 || totalPages <= 1) {
    return null;
  }

  return (
    <div className="join flex items-center gap-1">
      <button
        className="btn btn-ghost join-item"
        onClick={() => onPageChange(1)}
        disabled={currentPageNumber === 1}
        title="Primera página"
      >
        {`<<`}
      </button>
      
      <button
        className="btn btn-ghost join-item"
        onClick={() => onPageChange(Math.max(1, currentPageNumber - 1))}
        disabled={currentPageNumber === 1}
        title="Página anterior"
      >
         {`<`}
      </button>

      {renderPageNumbers()}

      <button
        className="btn btn-ghost join-item"
        onClick={() => onPageChange(Math.min(totalPages, currentPageNumber + 1))}
        disabled={currentPageNumber === totalPages}
        title="Página siguiente"
      >
         {`>`}
      </button>

      <button
        className="btn btn-ghost join-item"
        onClick={() => onPageChange(totalPages)}
        disabled={currentPageNumber === totalPages}
        title="Última página"
      >
        {`>>`}
      </button>
    </div>
  );
};

export default Paginator;
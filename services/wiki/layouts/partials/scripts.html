<script>    
    window.baseUrl = "{{ .Site.BaseURL | relURL }}";
</script>
<script src="{{ "js/minisearch.js" | relURL }}"></script>
<script src="{{ "js/search.js" | relURL }}"></script>
<script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
<script>
     window.onload = function() {
        mermaid.initialize({ 
            startOnLoad: false, // we handle rendering manually
            theme: document.querySelector('html').getAttribute('data-theme') === 'dark' ? 'dark' : 'default',
            securityLevel: 'loose'
        });

        document.querySelectorAll('.language-mermaid').forEach(function(el) {
            const div = document.createElement('div');
            div.classList.add('mermaid');
            div.textContent = el.textContent;
            const pre = el.parentElement;
            pre.parentElement.replaceChild(div, pre);
        });

        mermaid.init(undefined, '.mermaid');

        // Update mermaid theme when site theme changes
        const themeController = document.querySelector('.theme-controller');
        if (themeController) {
            themeController.addEventListener('change', (e) => {
                const isDark = e.target.checked;
                const html = document.querySelector('html');
                html.setAttribute('data-theme', isDark ? 'dark' : 'light');
                
                // Update mermaid theme when site theme changes
                mermaid.initialize({ 
                    startOnLoad: false,
                    theme: isDark ? 'dark' : 'default'
                });
                mermaid.init(undefined, '.mermaid');
            });
        }
    };
</script>


<div class="text-sm breadcrumbs">
    <ol class="" itemscope itemtype="https://schema.org/BreadcrumbList">
        {{- $data := newScratch }}

        {{- $ancestors := .Ancestors.Reverse }}
        {{- if .Params.customAncestors }}
            {{- $ancestors = .Params.customAncestors }}
        {{- end }}

        {{- range $index, $value := $ancestors }}
            <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem" class="">
                {{- $data.Set "counter" $index }}
                {{- $data.Add "counter" 1 }}
                {{- if reflect.IsMap $value }}
                    <a itemprop="item" href="{{ or $value.Permalink $value.permalink }}" class="">
                        <span itemprop="name">{{ or $value.Title $value.title }}</span>
                    </a>
                {{- else }}
                    <a itemprop="item" href="{{ $value.Permalink }}" class="">
                        <span itemprop="name">{{ $value.Title }}</span>
                    </a>
                {{- end }}
                <span class="mx-2">/</span>
                <meta itemprop="position" content='{{ $data.Get "counter"}}' />
            </li>         
        {{- end }}
            <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem" class="">
                {{- $data.Add "counter" 1 }}
                    <span itemprop="name" class="inline-flex items-center gap-2">{{.Title}}</span>
                <meta itemprop="position" content='{{ $data.Get "counter"}}' />
            </li>
    </ol>
</div>

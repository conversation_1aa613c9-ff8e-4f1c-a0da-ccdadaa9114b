{{ $file := .Get "file" }}
{{ $resource := resources.Get $file }}
{{ $csvOptions := dict "delimiter" "," }}
{{ with $resource }}
  {{ $parsedCSV := . | transform.Unmarshal $csvOptions }}
  <table class="table-auto border-collapse border border-gray-300 w-full">
    <thead class="bg-gray-200">
      <tr>
        {{ range index $parsedCSV 0 }}
          <th class="border border-gray-300 px-4 py-2 text-left">{{ . }}</th>
        {{ end }}
      </tr>
    </thead>
    <tbody>
      {{ range after 1 $parsedCSV }}
        <tr>
          {{ range . }}
            <td class="border border-gray-300 px-4 py-2">{{ . }}</td>
          {{ end }}
        </tr>
      {{ end }}
    </tbody>
  </table>
{{ else }}
  <p class="text-red-500">Failed to load CSV data from '{{ $file }}'.</p>
{{ end }}

{{- $pages := where .Site.Pages "Section" "!=" "" -}}
{
    "data": [
        {{- range $index, $page := $pages }}
        {{- if gt $index 0 }},{{ end }}
        {
            "title": {{ .Title | jsonify }},
            "content": {{ .Content | plainify | jsonify }},
            "summary": {{ .Summary | plainify | jsonify }},
            "permalink": {{ .Permalink | jsonify }}
        }
        {{- end }}
    ]
}

<!DOCTYPE html>
<html lang="{{ or site.Language.LanguageCode }}" dir="{{ or site.Language.LanguageDirection `ltr` }}" data-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net;">
    <link rel="manifest" href="/manifest.json"/>
    <link rel="apple-touch-icon" href="/apple-touch-icon.png"/>
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"/>
    <link rel="icon" href="/favicon.svg" type="image/svg+xml"/>
    <link rel="icon" href="/favicon.png" sizes="any"/>
    <title>{{ .Title }} - {{ .Site.Title }}</title>
    {{ $css := resources.Get "css/style.css" }}
    {{ $css = $css | css.PostCSS }}
    {{ if hugo.IsProduction }}
    {{ $css = $css | minify | fingerprint | resources.PostProcess }}
    {{ end }}
    <link rel="stylesheet" href="{{ $css.RelPermalink }}">
</head>

<body>
    <div class="drawer">
        <input id="my-drawer" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content">
            <div class="min-h-screen flex flex-col">
                <!-- Header -->
                {{ partial "header.html" . }}
                

                <!-- Main Content -->
                <main class="flex-grow container mx-auto px-4 py-8">
                    <div class="card bg-base-100 shadow-xl">
                        <div class="card-body">
                            
                            {{ block "main" . }}{{ end }}
                        </div>
                    </div>
                </main>

                <!-- Footer -->
                <footer class="footer footer-center p-4 bg-base-200 text-base-content">
                    <div>
                        <p>© {{ now.Format "2006" }} {{ .Site.Title }}. Built with Hugo</p>
                    </div>
                </footer>
            </div>
        </div>
        {{ partial "drawer-side.html" . }}
    </div>
    {{ partial "scripts.html" . }}
</body>
</html>

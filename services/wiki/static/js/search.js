// // Load Fuse.js
// const fuseScript = document.createElement('script');
// fuseScript.src = '/js/fuse.js';
// document.head.appendChild(fuseScript);

// fuseScript.onload = function() {
//     const searchInput = document.getElementById('search-input');
//     const searchResults = document.getElementById('search-results');
//     let fuse;

//     // Fetch the search index
//     fetch('/index.json')
//         .then(response => response.json())
//         .then(data => {
//             const options = {
//                 keys: [
//                     { name: 'title', weight: 0.3 },
//                     { name: 'content', weight: 0.5 },  // Give more weight to content
//                     { name: 'summary', weight: 0.2 }
//                 ],
//                 threshold: 0.2,  // Lower for better match sensitivity
//                 ignoreLocation: true,  // Ignore exact position matching
//                 includeScore: true  // Show match confidence
//             };
//             fuse = new Fuse(data.data, options);
//             console.log("Search index loaded:", data.data);
//         });

//     searchInput.addEventListener('input', function(e) {
//         if (!fuse) return;
        
//         const results = fuse.search(e.target.value);
//         console.log("Search results for:", e.target.value, results);


        
//         if (e.target.value.length === 0) {
//             searchResults.classList.add('hidden');
//             return;
//         }

//         searchResults.innerHTML = results
//         .slice(0, 5)
        // .map((result, index) => `
        //     <a href="${result.item.permalink}">
        //         <div class="p-4 hover:bg-base-200 cursor-pointer mx-2">
        //             <div class="font-bold">${highlightAndTruncate(result.item.title, searchInput.value)}</div>
        //             <div class="text-sm opacity-70">${highlightAndTruncate(result.item.summary, searchInput.value)}</div>
        //         </div>
        //     </a>
        //     ${index < 4 ? '<div class="my-5"><hr class="border-t border-gray-200"></div>' : ''}
        // `)
        // .join('');

//         searchResults.classList.remove('hidden');
//     });

//     // Close search results when clicking outside
//     document.addEventListener('click', function(e) {
//         if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
//             searchResults.classList.add('hidden');
//         }
//     });
// }

// function highlightAndTruncate(text, searchTerm, maxLength = 160) {
//     // Early return for empty inputs
//     if (!text) return '';
//     if (!searchTerm) return text.slice(0, maxLength);
    
//     // Create replacement map once
//     const replacements = [
//         [/<br\s*\/?>/gi, '\n'],
//         [/<\/p>/gi, '\n'],
//         [/<\/h[1-6]>/gi, '\n'],
//         [/<li>/gi, '• '],
//         [/<\/li>/gi, '\n'],
//         [/<\/(ul|ol)>/gi, '\n'],
//         [/<[^>]*>/g, '']
//     ];
    
//     // Single pass HTML cleaning
//     let cleanText = replacements.reduce((text, [pattern, replacement]) => 
//         text.replace(pattern, replacement), text);
    
//     const lowerText = cleanText.toLowerCase();
//     const lowerSearchTerm = searchTerm.toLowerCase();
//     const index = lowerText.indexOf(lowerSearchTerm);
    
//     // Calculate bounds
//     const start = index !== -1 ? Math.max(0, index - 60) : 0;
//     const end = Math.min(cleanText.length, start + maxLength);
    
//     // Build result string with minimal concatenations
//     const parts = [];
//     if (start > 0) parts.push('...');
//     parts.push(cleanText.slice(start, end));
//     if (end < cleanText.length) parts.push('...');
    
//     const truncated = parts.join('');
    
//     return index !== -1 
//         ? truncated.replace(new RegExp(searchTerm, 'gi'), 
//             match => `<mark class="bg-yellow-200">${match}</mark>`)
//         : truncated;
// }




// Load MiniSearch
document.addEventListener("DOMContentLoaded", async function () {
    // Fetch search index from JSON file
    const url = new URL(window.baseUrl, window.location.origin);
    url.pathname = url.pathname.replace(/\/$/, '') + '/index.json';
    
    const response = await fetch(url);
    const data = await response.json();

    // Initialize MiniSearch
    let miniSearch = new MiniSearch({
        fields: ['title', 'content', 'summary'],  // Fields to search
        storeFields: ['title', 'permalink', 'summary'],  // Fields to return in results
        idField: 'permalink',  // Use permalink as the unique ID
        searchOptions: {
            boost: { content: 2 },  // Give more weight to content
            prefix: true,  // Allow partial matches
            fuzzy: 0.2  // Allow typo-tolerant search
        }
    });

    // Add all pages to MiniSearch index
    miniSearch.addAll(data.data);
    // console.log("MiniSearch index loaded:", miniSearch);

    // DOM Elements
    const searchInput = document.getElementById('search-input');
    const searchResults = document.getElementById('search-results');

    // Handle search input
    searchInput.addEventListener("input", function (e) {
        let query = e.target.value.trim();
        if (!query) {
            searchResults.innerHTML = "";
            searchResults.classList.add('hidden');
            return;
        }

        let results = miniSearch.search(query, { fuzzy: 0.2, prefix: true });
        // console.log("Search results for:", query, results);

        // Render search results
        searchResults.innerHTML = results
            .slice(0, 5)  // Limit to top 5 results
            .map((result, index) => `
                <a href="${result.permalink}">
                    <div class="p-4 hover:bg-base-200 cursor-pointer mx-2">
                        <div class="font-bold">${highlightAndTruncate(result.title || "Untitled", searchInput.value)}</div>
                        <div class="text-sm opacity-70">${highlightAndTruncate(result.summary, searchInput.value)}</div>
                    </div>
                </a>
                ${index < 4 ? '<div class="my-5"><hr class="border-t border-gray-200"></div>' : ''}
            `)
            .join('');

        searchResults.classList.remove('hidden');
    });

    // Hide results when clicking outside
    document.addEventListener('click', function (e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

});

// Utility function to highlight and truncate results
function highlightAndTruncate(text, searchTerms, maxLength = 200) {
    // Split search input into terms and filter out empty strings
    const terms = searchTerms.toLowerCase().split(' ').filter(term => term.length > 0);
    
    // Create a MiniSearch instance for highlighting
    const miniSearch = new MiniSearch({
        fields: ['text'],
        storeFields: ['text']
    });

    // Add the text as a document
    miniSearch.add({
        id: 1,
        text: text
    });

    // Get matches using MiniSearch
    const matches = miniSearch.search(searchTerms, {
        prefix: true,
        fuzzy: 0.2
    });

    let highlightedText = text;
    
    // Sort terms by length (longest first) to avoid highlighting issues
    const sortedTerms = terms.sort((a, b) => b.length - a.length);
    
    // Highlight each term
    sortedTerms.forEach(term => {
        const regex = new RegExp(`(${term})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });

    // Truncate the text around the first highlight
    const firstHighlightIndex = highlightedText.indexOf('<mark>');
    if (firstHighlightIndex >= 0) {
        const start = Math.max(0, firstHighlightIndex - 100);
        const end = Math.min(highlightedText.length, firstHighlightIndex + 100);
        highlightedText = (start > 0 ? '...' : '') + 
                         highlightedText.substring(start, end) + 
                         (end < highlightedText.length ? '...' : '');
    }

    return highlightedText;
}


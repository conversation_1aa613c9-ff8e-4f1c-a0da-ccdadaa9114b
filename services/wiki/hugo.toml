baseURL = "https://example.com/wiki"
title = "Wiki"

[taxonomies]
  category = 'categories'
  tag = 'tags'

[outputs]
  home = ["HTML", "RSS", "JSON"]

[asset]
postCSS = true

[markup]
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true
  [markup.highlight]
    codeFences = true
    guessSyntax = false
    lineNos = false
    noClasses = false
  [markup.goldmark.parser]
    attribute = true
    autoHeadingID = true

[build]
  writeStats = true

[module]
  [[module.mounts]]
    source = "assets"
    target = "assets"
  [[module.mounts]]
    source = "static"
    target = "static"
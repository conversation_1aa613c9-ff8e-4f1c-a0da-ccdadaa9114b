---
title: "SteerCo Metrics Playbook"
categories:
  - Agile
  - Architecture
  - Playbooks
menu:
  main:
    name: SteerCo Metrics Playbook
    parent: Playbooks
---

## **Introduction**

This **SteerCo Metrics Playbook** provides a structured approach to tracking and analyzing key performance indicators 
(KPIs) for the Steering Committee (SteerCo). It outlines the metrics that are essential for evaluating the effectiveness 
while reviewing project direction and ensuring alignment with their goals.

The SteerCo presentation file is generated by the Project Managers and should be share with Agile facilitators to 
complete the analysis and insights described in this playbook. The playbook is designed to be flexible, allowing
for customization based on the specific needs of each project and team. It serves as a guide for effectively tracking.

## **1. Project Performance Metrics**

Each field found in this section is a metric that should be tracked and analyzed to assess the performance of the 
project and the team. 

### **1.1. Metrics**

<table>
  <thead>
    <tr>
      <th>Metric</th>
      <th>Considerations</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Cycle Time</td>
      <td rowspan=4>
        <li> Must show the 75th percentile of last 7 days, today inclusive.
        <li> Must use Client level to calculate this metric.
        <li> Data must be converted to hours using:
          <ul>
            <li><code>1 day = 24 hours</code></li>
          </ul>
        <li> Use decimals based on the <code>60m</code> per hour conversion          
      </td>
    </tr>
    <tr>
      <td>Coding Time</td>
    </tr>
    <tr>
      <td>Review Time</td>
    </tr>
    <tr>
      <td>Deploy Time</td>
    </tr>
    <tr>
      <td>Planning Accuracy</td>
      <td rowspan=2>
        <li> Must show the last <strong>open iteration.</strong>
      </td>
    </tr>
    <tr>
      <td>Throughput</td>
    </tr>
</table>

### **1.2. Charts**

<table>
  <thead>
    <tr>
      <th>Chart</th>
      <th>Consideration</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Cycle Time</td>
      <td>
        <ul>
          <li>Calculated using the 75th percentile of the last 8 weeks of activity.</li>
          <li>Must include all the teams of a client.</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Planning Accuracy</td>
      <td>
        <ul>
          <li>Use the Planning Accuracy calculated for the last 6 sprints.</li>
          <li>Only applicable for Scrum projects.</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Throughput</td>
      <td>
        <ul>
          <li>Use the Throughput calculated for the last 8 weeks of activity.</li>
          <li>Only applicable for Kanban projects.</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Work Breakdown</td>
      <td>
        <ul>
          <li>Contains the New Code, Rework and Refactor details</li>
          <li>Calculated using the 75th percentile of the last 8 weeks of activity.</li>
        </ul>
      </td>
    </tr>
  </tbody>
</table>

### **1.3 How to proceed**

#### **Prepare the Slide**

1. Lookup for the PPT that needs to be updated. It's not found request the latest version from the Project Manager.
1. Create a new slide using the layout the corresponding layout:
    1. If the project is Scrum use the layout called `Scrum Project Performance Metrics`.
    1. If the project is Kanban use the layout called `Kanban Project Performance Metrics`.
1. Complete the slide with the information found in the **Metrics** and **Charts** sections below.

#### **Completing Metrics**

**Using LinearB:**

1. For the Cycle Time and its related metrics, use the values found for each indicator found in the 
[LinearB Home Page](https://app.linearb.io/dashboard).
    1. Select the Project that is been analyzed.
    1. Adjust the date and criterias to match the one found in the [**Indicators**](#11-indicators) section.
1. For the Planning Accuracy, use the values found in the 
[LinearB Project Delivery](https://app.linearb.io/pdt/projectsapi/all).(**)
    1. Select your project from the list.
    1. Select the date to match the criteria found in the **Considerations** section.
    1. Ensure that `Delivery` metric is by `Issue count` and not by `Story Points`.
    1. For Planning Accuracy, use the value found in the **Planning Accuracy** section.

(**) ***If the project is a Kanban project, skip this step.***

**Using Jira:**
1. For the Throughput, use the values found in the Jira report

#### **Completing Charts**

**Preparing Data for Graphs:**
1. Use the data template for the [SteerCo Metrics](https://applaudostudios.sharepoint.com/:x:/r/sites/applaudo-technology/agile-facilitator-team/Shared%20Documents/Templates/SteerCo%20Graphs.xlsx?d=w063158daef564ce09cfdfd58991de02b&csf=1&web=1&e=Fs8dJ3&nav=MTVfezY0NzdERDM3LUU4MUYtNDY5MC05M0M3LUM1QUQ0NTJERkI5MX0)
available in SharePoint to create the charts.
1. If the sheet for the project does not exist, create a new sheet with the name of the project using the `Template`
sheet.
    1. Right-click on the `Template` sheet tab and select `Move or Copy...`.
    1. In the dialog, check the `Create a copy` option and select the `Template` sheet.

**Using LinearB:**

1. For the Cycle Time Graphs and Work Breakdown, use the values found for each graph in the LinearB report
[Agile Engineering Weekly](https://app.linearb.io/performance/1954736607?filterType=People&isCopyLink=true&selectedGranularity=week).
    1. Select the Project that is been analyzed.
    1. Adjust the date and criterias to match the ones found in the [**Considerations**](#12-graphs) section.
    1. For Cycle Time and its related metrics, adjust the values on Excel sheet and copy the resulting chart.
    1. For Work Breakdown, copy the 3 charts that represent the New Code, Rework, and Refactor metrics and paste them 
    into the slide.
1. For the Planning Accuracy let's use the values found in the LinearB report 
[Project Delivery](https://app.linearb.io/pdt/projectsapi/all). (**)
    1. Select your project from the list.
    1. Select the date to match the criteria found in the [**Considerations**](#12-graphs) section.
    1. Ensure that `Delivery` metric is by `Issue count` and not by `Story Points`.
    1. For Planning Accuracy, use the values found in the **Accuracy Score over time** graph (right side of the screen).

**Using Jira:**
1. For the Throughput, use the values found in the Jira report
1. Match the date to match the criteria found in the [**Considerations**](#12-graphs) section. (***)
    
(**) ***If the project is a Kanban project, skip this step.***

(***) ***If the project is a Scrum project, skip this step.***

#### **Completing Highlights**

1. Review the data and metrics presented in the slide.
1. Identify any significant trends, anomalies, or areas for improvement based on the metrics.
1. Use the **Highlights** boxes to summarize key findings, and action items such as:
    - Significant changes on the metrics results.
    - Areas where the team has excelled or faced challenges.
    - Any other relevant observations that could impact project performance.
1. Ensure that the highlights are concise and actionable, providing clear insights for the SteerCo.


## **2. Bugs Performance Analysis**

Each field in this section represents where the team is spending their time based on the ticket types, also shows the
how bugs are being handled by the team.

### **2.1. Metrics**

<table>
  <thead>
    <tr>
      <th>Metric</th>
      <th>Considerations</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>% Stories</td>          
      <td rowspan="4">
        <ul>
          <li>Must show the percentage of the last iteration.</li>
          <li>Must include all the teams of a client.</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>% Non Stories</td>
    </tr>
    <tr>
      <td>%Bugs</td>
    </tr>
    <tr>
      <td>Defect Ratio</td>
    </tr>
  </tbody>
</table>

### 2.2. Charts

<table>
  <thead>
    <tr>
      <th>Chart</th>
      <th>Considerations</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Bugs Status</td>
      <td>
        <ul>
          <li>For <strong>Scrum Projects</strong>, shows the last 6 sprints.</li>
          <li>For <strong>Kanban Projects</strong>, shows the last 8 weeks of activity.</li>
          <li>This should open, closed and quality ratio trending</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Bugs Breakdown</td>
      <td>
        <ul>
          <li>For <strong>Scrum Projects</strong>, shows the last 6 sprints.</li>
          <li>For <strong>Kanban Projects</strong>, shows the last 8 weeks of activity.</li>
          <li>This should all the issues created regardless the current status</li>
        </ul>
      </td>
    </tr>
  </tbody>
</table>

### **2.3 How to proceed**

#### **Prepare the Slide**

1. Lookup for the PPT that needs to be updated. It's not found request the latest version from the Project Manager.
1. Create a new slide using the layout called `Bugs Performance Analysis`.

#### **Completing Metrics**

1. For `Investment Profile`, extract the how many tickets were worked that matches the criteria found in the 
[**Considerations**](#21-metrics) section. The sum of all the metrics must be 100%.  
1. For `Defect Ratio`, extract the data that matches the criteria found in the [**Considerations**](#21-metrics).

#### **Completing Charts**

**Preparing Data for Graphs:**
1. Use the data template for the [SteerCo Metrics](https://applaudostudios.sharepoint.com/:x:/r/sites/applaudo-technology/agile-facilitator-team/Shared%20Documents/Templates/SteerCo%20Graphs.xlsx?d=w063158daef564ce09cfdfd58991de02b&csf=1&web=1&e=Fs8dJ3&nav=MTVfezY0NzdERDM3LUU4MUYtNDY5MC05M0M3LUM1QUQ0NTJERkI5MX0)
available in SharePoint to create the charts.
1. If the sheet for the project does not exist, create a new sheet with the name of the project using the `Template`
sheet.
    1. Right-click on the `Template` sheet tab and select `Move or Copy...`.
    1. In the dialog, check the `Create a copy` option and select the `Template` sheet.
1. For the `Bug Status`
    1. Adjust the date and criteria to match the ones found in the [**Considerations**](#22-graphs) section.
1. For the `Bugs Breakdown`
    1. Adjust the date and criteria to match the ones found in the [**Considerations**](#22-graphs) section.

#### **Completing Highlights**
1. Follow the instruction from this [Completing Highlights](#completing-highlights).
1. Consider to add extra details if team is inventing time on issues different from stories or bugs.

## **3. Team Performance Metrics​**

Each table in this section represents the performance of the team based on the last iteration.

**Sort them by the lowest performers first.**

### **3.1. Metrics**
<table>
  <thead>
    <tr>
      <th>Metric</th>
      <th>Considerations</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Coding Time</td>
      <td rowspan="6">
        <ul>
          <li>For <strong>Scrum Projects</strong>, shows the last open sprints.</li>
          <li>For <strong>Kanban Projects</strong>, shows the last week of activity.</li>
          <li>For Planned, Unplanned and Closed Tickets exclude `Bugs and Defects`.</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Review Time</td>
    </tr>
    <tr>
      <td>Defect Ratio</td>
    </tr>
    <tr>
      <td>Planned Tickets</td>
    </tr>
    <tr>
      <td>Unplanned Tickets</td>
    </tr>
    <tr>
      <td>Closed Tickets</td>
    </tr>
    <tr>
      <td>Root Cause</td>
      <td rowspan="2">
        <ul>
          <li>
            Use the 
            <a href="{{< relref "../agile-handbook/agile-metrics.md#thresholds" >}}">
              Individual Thresholds
            </a>
            to determine, if a team member is underperforming.
          </li>
          <li>
            If there are team members underperforming, include the Root Cause and Mitigation Plan.
          </li>
        </ul>
      </td>
    </tr>
    <tr>
      <td>Mitigation Plan</td>
    </tr>
  </tbody>
</table>

### **3.2 How to proceed**

#### **Prepare the Slide**

1. Lookup for the PPT that needs to be updated. It's not found request the latest version from the Project Manager.
1. Create a new slide using the layout called `Team Performance Metrics` for each team that is working for the client/project.

#### **Completing Table**
1. Complete the name of the team in the slide title.
1. Complete the Iteration Dates based on the last open iteration for the project using the format `dd/mm/yyyy - dd/mm/yyyy`.
1. For the `Coding Time`, `Review Time`, `Defect Ratio`, `Planned Tickets`, `Unplanned Tickets` and `Closed Tickets` metrics, extract the data that for each team member within the team.
1. Complete `Root Cause` and `Mitigation Plan` based on the analysis of the team performance.

#### **Completing Highlights**
1. Follow the instruction from this [Completing Highlights](#completing-highlights).
1. Consider to add extra details that concern the team performance
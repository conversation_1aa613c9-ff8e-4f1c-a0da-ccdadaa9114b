---
title: "Jira Playbook"
categories:
  - Agile
  - Architecture
  - Playbooks
menu:
  main:
    name: Jira Playbook
    parent: Playbooks
---

# Jira Playbook

## **Introduction**

This **Jira Playbook** serves as a guide for effectively managing tickets within the team's workflow. It establishes standardized processes to prevent common issues such as unresolved dependencies, lack of documentation on blocked tickets, and ineffective follow-ups. 

By following the best practices outlined in this document, the team can:
- Improve visibility into ticket statuses and blockers.
- Reduce unnecessary delays caused by untracked dependencies.
- Ensure clear accountability and ownership of tasks.
- Enhance collaboration and communication across teams.
- Ensure that every user story is estimated and tracked.

The following sections outline specific scenarios, common causes, and step-by-step procedures to help the team maintain a smooth and efficient workflow.

---


## **1. Managing Blocked Tickets**

### **1.1. Tickets Blocked Due to Dependencies Without Follow-Up**

#### **Common Causes:**
- The dependency was not identified before starting the sprint.
- The dependency does not have an assigned responsible person or a resolution date.

#### **How to Proceed:**
1. **During Sprint Planning:**
    - Before accepting a ticket, the team must verify if it has critical dependencies.
    - If there are dependencies, there must be an associated ticket with a designated owner, and its status must be clearly documented in the backlog.

2. **If the ticket enters the Sprint and later gets blocked:**
    - **Procedural Rules:**
        - If the dependency cannot be resolved within the current sprint, **move it back to the backlog**.
        - If the dependency can be resolved within the current sprint, the ticket remains blocked **but with a clearly assigned responsible person and a follow-up date**.
    - A responsible person must be assigned to follow up every 48 hours.

3. **Documenting the Blocker:**
    - In the ticket description, add a **Blocker Section** with:
        - Reason for the blockage.
        - Assigned responsible person for follow-up.
        - Estimated resolution date.
    - Create a "Blocker Epic" where all blocked tickets are added for later analysis.

#### **Responsible Roles:**
- **Product Owner (PO) and Agile Facilitator (AF):** Ensure proper tracking of blockers.
- **Developers:** Update the reason for the blocker in the ticket.

---

### **1.2. Tickets Blocked Without Documenting the Reason**

#### **Common Causes:**
- There is no clear policy on documenting blockers.
- Lack of communication between developers and the AF/PO.

#### **How to Proceed:**
1. **Procedural Rules:**
    - Every blocked ticket must include:
        - Reason for the blockage.
        - Date when the blockage was identified.
        - Assigned responsible person for follow-up.
    - It is not allowed to mark a ticket as blocked without documenting the reason in the tracking tool (Jira, Trello, ClickUp, etc.).

2. **Follow-Up Strategy:**
    - The AF must review blocked tickets during the **Daily** meeting.
    - If a ticket has been blocked for more than three days without an update, the PO or AF must intervene.

#### **Responsible Roles:**
- **Developer who marks the ticket as blocked:** Must document the reason for the blockage.
- **AF/PO:** Review blocked tickets and ensure follow-up.

## **2. User Story Estimation**

### **2.1. User Story without Estimation**

#### **Common Causes:**

- The team has not conducted the required discussion to estimate the user story.
- The team does not have enough information to estimate the user story.

#### **How to Proceed:**

1. **Procedural Rules:**
    - **During Sprint Refinement** an estimated time must be set on every user story using the field `Original estimate`.    
    - The value must be agreed upon by the team and should reflect the effort required to complete the user story, from the moment it will be move to `In Progress` until is set to `Done`, meaning.
    - The value for `Original estimate` can be entered in minutes (`Xm`), hours (`Xh`), or days (`Xd`). The system will automatically display the value in days for consistency.    
    - If the team cannot estimate the story, it must be broken down into smaller stories.
2. **Follow-Up Strategy:**
    - The Agile Facilitator must review the `Original estimate`.
    - If a user story is missing the `Original estimate` Agile Facilitator must intervene so the team can provide the details.
    - The Agile Facilitator must coordinate any space needed for the team to discuss and agree on the estimation.
    - The Agile Facilitator must set the `Original estimate` field in the user story.

#### **Responsible Roles:**

- **Developers:** 
    - Must estimate the user stories.
- **Business Architect:** 
    - Ensure the estimation is aligned with the user story's scope and objectives.
    - Provide insights to the team to facilitate the estimation process.
- **Agile Facilitator (AF):** 
    - Review the `Original estimate` field and ensure it is filled.
    - Keep track of the user stories without estimation.
    - Check that developers and business architects know the meaning and importance of the `Original estimate` field.
    - Provide the space for the team to discuss and agree on the estimation.
    - Set the `Original estimate` field in the user story.
- **Product Owner (PO):** 
    - Ensure the team has the necessary details to discuss and agree on the estimation.

## **3. Epic Estimation**

### **3.1. Epic without Estimation**

#### **Common Causes:**

- The team has not conducted the required discussion to estimate the Epic.
- The team does not have enough information to estimate the Epic.


1. **Procedural Rules:**
    - **During Refinement** an estimated time must be set on every epic ticket using the field `Original estimate`.    
    - The value must be agreed upon by the Business and Solution (if applies) Architect and should reflect the effort required to complete all the stories within the epic, from the moment it will be move to `In Progress` until is set to `Done`, meaning.
    - The value for `Original estimate` can be entered in minutes (`Xm`), hours (`Xh`), or days (`Xd`). The system will automatically display the value in days for consistency.    
2. **Follow-Up Strategy:**
    - The Product Owner must review the `Original estimate`.
    - If an epic is missing the `Original estimate` Product Owner must intervene so Business and Solution (if applies) Architects can provide the details.
    - The Product Owner must coordinate any space needed for the team to discuss and agree on the estimation.
    - The Product Owner must set the `Original estimate` field in the user story.

#### **Responsible Roles:**

- **Business Architect and Solution Architect:** 
    - Must estimate the epic.
    - Ensure the estimation encompasses the full scope and objectives of the epic, including the time required for each area to complete their respective tasks (Quality, Development, Design, DevOpt, etc.).
    - Provide insights to the team to facilitate the estimation process.
- **Product Owner (PO):** 
    - Review the `Original estimate` field and ensure it is filled.
    - Keep track of the epics without estimation.
    - Provide the space for the team to discuss and agree on the estimation.
    - Set the `Original estimate` field in the epic ticket.
    - Ensure the team has the necessary details to discuss and agree on the estimation.
---
title: LinearB Playbook
date: 2025-03-21
categories:
  - Agile
  - Playbooks
menu:
  main:
    name: LinearB Playbook
    parent: Playbooks
---

# LinearB Playbook

## Introduction

LinearB is a powerful tool designed to help teams improve their software delivery performance by providing actionable insights and metrics. This playbook serves as a guide for effectively utilizing LinearB to monitor team progress, identify areas for improvement, and foster a culture of continuous delivery excellence. By following the steps outlined here, teams can ensure they are leveraging LinearB to its fullest potential to drive success in their projects.

## 1. Weekly Report
The objective is to analyze **LinearB metrics** and derive actionable insights regarding your project's performance and your development team's efficiency. Focus on identifying trends, bottlenecks, and opportunities for improvement to drive measurable outcomes.

### 1.1. Generate report

#### How to Proceed:

1. Use the [LinearB Weekly Report Template](https://applaudostudios.sharepoint.com/:w:/r/sites/applaudo-technology/agile-facilitator-team/Shared%20Documents/Templates/AF%20-%20Weekly%20Report%20Template%20-%20LinearB.docx?d=w19ce12f401e94b118a61969dd73fca9d&csf=1&web=1&e=jdkOkV) available on SharePoint as a guide to create the report. You can populate the data in Confluence, Word, or even directly in the body of the email—there are no restrictions on the format.
2. Ensure the report covers data from **last Friday to this Thursday** to reflect the team's performance over the past week.
3. Schedule a meeting with your Project Manager (PM) and Technical Lead (typically the Business Architect) to review the report. Use this opportunity to discuss the results and align on any necessary action items.
4. Once the report and action items are finalized:
    - Share the report via email every **Friday morning before 10am CTS** to provide valuable insights.
    - Ensure the Project Manager can upload the report to BaseCamp.
    - Include the Engineering Delivery Manager in the email for visibility.

#### Responsible Roles:
- **Agile Facilitator (AF):** 
    - Analyze the current state of the project and team performance.
    - Create the weekly report and share it with the relevant parties.
    - Schedule a meeting with the PM to discuss the report.
- **Project Manager (PM):**
    - Review the report and discuss the results with the AF and Technical Lead.
    - Upload the report to BaseCamp for broader visibility.

## 2. Report Inconsistencies


### 2.1. Submit a Ticket for Data Inconsistencies

#### Common Causes:

- The client project portfolio and team configurations are outdated and do not accurately represent the current state of the projects.
- LinearB data updates are inconsistent or inaccurate, leading to unreliable metrics and insights.
- Changes in the team structure or project scope require updates to the configuration to ensure accurate and reliable metrics.

#### How to Proceed:

1. Submit a ticket in the Service Desk to request updates to the client project portfolio and team configurations. Use the [IT Ticket Template](https://sdpondemand.manageengine.com/app/itdesk/ui/requests/add?reqTemplate=137355000030570750).
2. Track the ticket's progress and ensure the necessary updates are implemented promptly.

#### Responsible Roles:
- **Agile Facilitator (AF):** 
    - Ensure LinearB reflects the project and team structure.
    - Submit a ticket for any necessary updates.
- **IT:** 
    - Keep the tool updated with the latest configurations and data.
    - Address any inconsistencies promptly to maintain data accuracy.
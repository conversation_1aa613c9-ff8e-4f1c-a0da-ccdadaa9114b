

## AI prompt for estimating effort


``````markdown

System: You are a technical and product expert (business architect, solution architect, product owner) with many years of experience building software applications of all types. Provide clear, concise, and accurate explanations.

User:
### Instruction:
Create a detailed project Gantt chart in MermaidJS syntax using the data provided as input and take into consideration the following:

- The format of the output should be as specified in the Output Format section.
- The **section name of the Gantt chart must be the name of the feature**.
- The start date for the project is {2025-01-01}.
- The project has a max duration of {9} months.
- All tasks (features) must be completed within the project duration.
- **Tasks should be scheduled to maximize parallel execution as much as possible.**
- **Backend tasks should start immediately and be parallelized.**
- **Frontend tasks should start exactly at half the time of the backend task duration (not after it fully completes).**
  - For example, if a backend task takes 4 days, the frontend task should start after 2 days.
- **QA tasks should start immediately after the corresponding backend work is completed.**
- **DevOps tasks  must be summarized into 1 task and should start at the beginning of the project but should not block other tasks unless explicitly necessary.**
- **Mobile (iOS/Android) and Data-related tasks should start at half the time of the backend task duration, similar to frontend tasks.**
- Workdays are Monday to Friday with a 44-hour work week.
- Real productive hours of the team are 40 hours per week.

### **Dependency Rules**
1. **Backend tasks should start at the beginning of the project and be parallelized.**
2. **Frontend tasks should start at exactly half the time of the backend task duration.**
   - ✅ Example: If a backend task takes 6 days, the frontend task must start after 3 days.
   - ❌ It should **not** wait for the backend task to be completed before starting.
3. **QA tasks should start immediately after the corresponding backend task is completed.**
4. **DevOps should start at the beginning of the project and run in parallel with other tasks, but should not block other work unless explicitly necessary.**
5. **Mobile (iOS/Android) and Data-related tasks should start at exactly half the backend task duration.**

#### **MermaidJS Formatting Rules**
- **Use explicit start dates instead of "after" dependencies** to prevent MermaidJS parsing errors.
- **Avoid circular dependencies** (ensure each task has a clear flow).
- **Ensure that dependencies reference existing tasks correctly**.
- **Keep dependencies sequential and logical** (i.e., frontend should not start after QA).

Make sure to follow all considerations above and **include the headcount table in the response**.


### Input:

**Data**

{CSV DATA}


### Output Type: 
Markdown

### Output Format:


**Project Gannt Chart (MermaidJS) output format example**

```mermaid

gantt
    title Blog Platform Development Timeline
    dateFormat  YYYY-MM-DD
    axisFormat  %b %d

    %% DevOps starts at the beginning of the project
    section DevOps
    DevOps Setup      :devops_setup, 2025-01-01, 13d

    %% Backend tasks scheduled for parallel execution
    section User Authentication
    Backend          :ua_backend, 2025-01-01, 4d
    Frontend         :ua_frontend, 2025-01-03, 4d
    QA               :ua_qa, after ua_backend, 4d

    section Post Creation
    Backend          :pc_backend, 2025-01-01, 5d
    Frontend         :pc_frontend, 2025-01-03, 6d
    Android          :pc_android, after pc_backend, 2d
    iOS              :pc_ios, after pc_backend, 3d
    Data             :pc_data, after pc_backend, 3d
    QA               :pc_qa, after pc_frontend, 8d

    section Post Scheduling
    Backend          :ps_backend, 2025-01-01, 5d
    Frontend         :ps_frontend, 2025-01-03, 3d
    Android          :ps_android, after ps_backend, 2d
    iOS              :ps_ios, after ps_backend, 2d
    Data             :ps_data, after ps_backend, 2d
    QA               :ps_qa, after ps_frontend, 6d

    section Categories & Tags
    Backend          :ct_backend, 2025-01-01, 4d
    Frontend         :ct_frontend, 2025-01-03, 3d
    Android          :ct_android, after ct_backend, 2d
    iOS              :ct_ios, after ct_backend, 2d
    Data             :ct_data, after ct_backend, 2d
    QA               :ct_qa, after ct_frontend, 6d

    section Comment Management
    Backend          :cm_backend, 2025-01-01, 6d
    Frontend         :cm_frontend, 2025-01-04, 5d
    Android          :cm_android, after cm_backend, 2d
    iOS              :cm_ios, after cm_backend, 3d
    Data             :cm_data, after cm_backend, 2d
    QA               :cm_qa, after cm_frontend, 8d

    section SEO Tools
    Backend          :seo_backend, 2025-01-01, 4d
    Frontend         :seo_frontend, 2025-01-03, 4d
    Android          :seo_android, after seo_backend, 1d
    iOS              :seo_ios, after seo_backend, 2d
    Data             :seo_data, after seo_backend, 2d
    QA               :seo_qa, after seo_frontend, 6d

    section Media Library
    Backend          :ml_backend, 2025-01-01, 6d
    Frontend         :ml_frontend, 2025-01-04, 5d
    Android          :ml_android, after ml_backend, 2d
    iOS              :ml_ios, after ml_backend, 3d
    Data             :ml_data, after ml_backend, 3d
    QA               :ml_qa, after ml_frontend, 9d

    section Analytics Dashboard
    Backend          :ad_backend, 2025-01-01, 7d
    Frontend         :ad_frontend, 2025-01-04, 5d
    Android          :ad_android, after ad_backend, 2d
    iOS              :ad_ios, after ad_backend, 3d
    Data             :ad_data, after ad_backend, 5d
    QA               :ad_qa, after ad_frontend, 10d

    section User Roles & Permissions
    Backend          :urp_backend, 2025-01-01, 6d
    Frontend         :urp_frontend, 2025-01-04, 4d
    Android          :urp_android, after urp_backend, 2d
    iOS              :urp_ios, after urp_backend, 2d
    Data             :urp_data, after urp_backend, 3d
    QA               :urp_qa, after urp_frontend, 8d

    section Backup & Restore
    Backend          :br_backend, 2025-01-01, 5d
    Frontend         :br_frontend, 2025-01-03, 2d
    Android          :br_android, after br_backend, 1d
    iOS              :br_ios, after br_backend, 2d
    Data             :br_data, after br_backend, 3d
    QA               :br_qa, after br_frontend, 7d

```

**Project Headcount table output format example**

| Role           | Required Time (Days) | Equivalent FTEs |
|----------------|----------------------|-----------------|
| Backend Devs   | 52 days              | ~2-3 FTEs       |
| Frontend Devs  | 41 days              | ~2 FTEs         |
| DevOps         | 13 days              | ~1 FTE          |
| Android Devs   | 16 days              | ~1 FTE          |
| iOS Devs       | 22 days              | ~1-2 FTEs       |
| Data Engineers | 25 days              | ~1-2 FTEs       |
| QA Engineers   | 72 days              | ~3-4 FTEs       |



### Examples: 

**Input data**


```csv

"Feature","Description","Frontend (Days)","Backend (Days)","DevOps (Days)","Android (Days)","iOS (Days)","Data (Days)","QA (Days)","Total Days (Calculated by PERT)"
"User Authentication","Allows users to sign up, log in, and manage profiles, with different role levels.
","4","4","0","0","0","0","4","15"
"Post Creation
","Enables users to write, format, and publish blog posts with media attachments.
","6","5","1","2","3","3","8","35"
"Post Scheduling
","Lets users schedule blog posts to be published at a future date and time.
","3","5","1","2","2","2","6","27"
"Categories & Tags
","Provides organization for posts using categories and tags for easier navigation.
","3","4","1","2","2","2","6","25"
"Comment Management
","Allows users to moderate and manage comments left on blog posts.
","5","6","1","2","3","2","8","34"
"SEO Tools
","Includes features for optimizing content (meta tags, keywords) for search engines.
","4","4","1","1","2","2","6","25"
"Media Library
","Centralized repository to upload and manage images, videos, and files.
","5","6","2","2","3","3","9","38"
"Analytics Dashboard
","Displays metrics such as views, popular posts, and user engagement.
","5","7","2","2","3","5","10","43"
"User Roles & Permissions
","Defines different roles (admin, editor, author) with specific access and control.
","4","6","1","2","2","3","8","33"
"Backup & Restore
","Provides functionality to back up the blog content and restore it if necessary.
","2","5","3","1","2","3","7","29"
"","","41","52","13","16","22","25","72","304"

```





### Additional Instruction:
If unsure or if the input is unclear, respond with:
{
  "error": "Unable to generate response due to insufficient information."
}

Assistant:

``````

---
title: "Code Quality Metrics"
categories:
  - Position
  - Architecture
  - Role
menu:
  main:
    name: Code Quality Metrics
    parent: Architecture Handbook
---


# 📊 Code Quality Metrics

## 📝 **Introduction**

Code quality metrics are essential for evaluating the health and maintainability of software projects. They provide objective measures that help developers and teams make informed decisions about their codebase.


## 🎯 **Objectives**

- Ensure code quality across ApplaudoStudios projects making easy to provide an proof of quality for our clients.
- Standardize code quality practices in ApplaudoStudios, providing the tools and the process for developers and projects to stick to one of the levels of quality defined in ApplaudoStudios.
- Create a framework that would allow us to propagate code quality changes thru the organization projects.

## 🔍 **Scope**

The framework would consider all project repositories where ApplaudoStudios **developers are part of the 65% of coding power**. Or where a project have ApplaudoStudios software architects that ensures a level quality is followed. For external code repositories, static code analysis will be performed by software architect or by team technical leader, submitting the result directly to Papirus.

## 📋 Project assumptions

- Application Architects will ensure the creation and tracking of Jira Issues from SonarQube issues.


## 📈 Metrics

We defined a set of metrics that will give us a good understanding of the code quality state for a project, and by using different consideration for each metric we can create code quality levels. This quality levels will help us to ensure a project/client is receiving the quality they/we expect from our developers.

Notice that we are using Sonarqube as our source of truth for static code analysis and based on this report, we define what we as ApplaudoStudios considered important. You can review the whole list of Sonarqube metrics in this [link](https://docs.sonarqube.org/latest/user-guide/metric-definitions/ "https://docs.sonarqube.org/latest/user-guide/metric-definitions/").


## 📏 Metrics definition

The following metrics had been considered as important factor for code quality in a project.

### New code metrics

These metrics are defined against new code between Sonarqube analysis, and can be used to verify if that the new code entering the code base is following a set of rules.

In our case, and in order to favor the project capacity to react when needed, the new code rules are very flexible and allow the team to introduce code smells but only if overall code metrics are kept in healthy levels after this new code introduction.

#### Metric: Critical issues count

This metric is the count of issues generated by Sonarqube that categorized as _Critical Issues._ The quality gate would consider it in the following way: _“Critical issues count for new code should be less than N”._

#### Metric: Major issues count

This metric is the count of issues generated by Sonarqube that categorized as _Major Issues._ The quality gate would consider it in the following way: _“Major issues count for new code should be less than N”._

### Overall code metrics

These metrics are defined for all repository code base, this will include the new code added and the already existing code. These kind of metrics are to ensure that overall project quality is maintained.

#### Metric: Blocker issues count

This metric is the count of issues generated by Sonarqube that categorized as _Blocker Issues._ The quality gate would consider it in the following way: _“Blocker issues count for overall code should be less than N”._

#### Metric: Critical Issues count

This metric is the count of issues generated by Sonarqube that categorized as _Critical Issues._ The quality gate would consider it in the following way: _“Critical issues count for overall code should be less than N”._

#### Metric: Major issues count

This metric is the count of issues generated by Sonarqube that categorized as _Major Issues._ The quality gate would consider it in the following way: _“Major issues count for overall code should be less than N”._

#### Metric: Reopen issues count

This metric is the count of issues generated by Sonarqube that categorized as _Reopened Issues._ These reopened issues are based on issues that reappears in the project, and this metric is intended to guide the team to improve code quality overtime and reduce tech debt over time. The quality gate would consider it in the following way: _“Reopened issues count for overall code should be less than N”._

#### Metric: Comment density

This metric is the relation between lines of code and lines of comments in the repository, as a good ratio based on multiple studies, notice [this](https://www.researchgate.net/publication/221555173_The_Comment_Density_of_Open_Source_Software_Code "https://www.researchgate.net/publication/221555173_The_Comment_Density_of_Open_Source_Software_Code") study, that suggest possible correlations between a comment density of 20% approx with project maintainability increase ( notice how as project team members increases it also increase comments density ). Based on this we are suggesting a minimum of 20% for comment density, also take into consideration that this will include source code documentation. The quality gate would consider it in the following way: “_Comment density should be more or equal to %_“

#### Metric: Duplicated blocks count

This metric count the number of duplicated code blocks in the source code. It will consider a block from 10+ lines of code that are very similar. The quality gate would consider it in the following way: _“Duplicated blocks should be less than N”_

#### Metric: Code coverage

Sonarqube code coverage metric is a combination of _branch coverage + statement coverage,_ the following formula is used:


{{< asset-img src="/assets/images/coverage-formula.png" alt="Code coverage formula" >}}


With the combination of code statements + branch code lines we can ensure coverage of almost 100% of the code. Quality gate would consider it in the following way: _“Code coverage should be more than %”_

#### Metric: Maintainability Rating

This rating is a value that is based on project Technical debt ratio, for this it consider code smells issues. Possible values are:

**A**=0-0.05, meaning _“<=5% of the time that has already gone into the application, the rating is A”_

**B**=0.06-0.1, meaning _“between 6 to 10% the rating is a B”_

**C**=0.11-0.20, meaning _“between 11 to 20% the rating is a C”_

**D**=0.21-0.5, meaning _“between 21 to 50% the rating is a D”_

**E**=0.51-1, meaning _“anything over 50% is an E”_

Quality gate would consider it in the following way: _“Maintainability rating should be better than RATE”_

#### Metric: Reliability Rating

Is a value to qualify the project reliability based on the worst kind of bug issue currently open in the project.

**A** = 0 Bugs  
**B** = at least 1 Minor Bug  
**C** = at least 1 Major Bug  
**D** = at least 1 Critical Bug  
**E** = at least 1 Blocker Bug

Quality gate would consider it in the following way: _“Reliability rating should be better than RATE”_

#### Metric: Security Rating

Is a value to qualify the project security rate based on the worst kind of security issue currently open in the project. _Notice that is is not OWASP vulnerabilities, only code vulnerabilities._

**A** = 0 Vulnerabilities  
**B** = at least 1 Minor Vulnerability  
**C** = at least 1 Major Vulnerability  
**D** = at least 1 Critical Vulnerability  
**E** = at least 1 Blocker Vulnerability

Quality gate would consider it in the following way: _“Security rating should be better than RATE”_

### OWASP

OWASP is  Open Web Application Security Project and provides free reports about dependencies that are vulnerable to attacks. This dependencies vulnerabilities are classified from LOW, MEDIUM to HIGH, and a cross table is used, to get the most important to fix dependencies in a relation of **Project Impact * Likelihood to happen**.

{{< asset-img src="/assets/images/overall-risk-severity.png" alt="Overall risk severity" >}}


##### Metric: OWASP Critical severity vulnerabilities

This metric is a count of OWASP critical vulnerabilities, for ISO requirements we are going to keep this always to 0.

##### Metric: OWASP High severity vulnerabilities

This metric is a count of OWASP high vulnerabilities, for ISO requirements we are going to keep this always to 0.

##### Metric: OWASP Medium severity vulnerabilities

This metric is a count of OWASP medium vulnerabilities, for ISO requirements we are going to keep this always less or equal to 10.

##### Metric: OWASP Low severity vulnerabilities

This metric is a count of OWASP low vulnerabilities, for ISO requirements we are going to keep this always to 20.

#### Cognitive complexity special consideration

Cognitive complexity measures how hard is to understand the code’s control flow in a method. A complete explanation of the math behind the metric can be found in [sonarqube official documentation](https://www.sonarsource.com/resources/white-papers/cognitive-complexity/ "https://www.sonarsource.com/resources/white-papers/cognitive-complexity/").

For us in ApplaudoStudios is very important that developers are capable to keep methods simple, as this ensures and improves code testing capacity in the team and at the same time reduce introducing feature bugs due to complex logic. There is a limitation implementing this cognitive complexity metric in sonarqube quality gates, as this metrics is aggregated for overall project, meaning that the bigger the project gets, the more complex it will be in the quality gate. To overcome this, we changed the cognitive complexity rule for the main languages ( c#, java, javascript, typescript, ruby, swift, kotlin ) to create _Blocker issues_ for methods/functions that are over N value of cognitive complexity, this way we can implicitly ensure cognitive complexity is always reviewed.

## 🏆 Metrics tier

As we are using Sonarqube as our main static code analysis tool, we are using the concept of _Quality Gates,_ this allow us to use the same list of metric that we already defined but with different settings to weight the metrics and be less or more strict as we need.

Based on ApplaudoStudios needs, we defined a 4 tier structure of metrics:

### ApplaudoStudios - Bronze

Most basic code quality standard, **is mainly intended for playground projects and POCs**. Is very permissive with code quality, but will always ensure ISO requirements for dependencies vulnerabilities but medium/low vulnerabilities are ignored.

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|
|Critical Issues count|New Code|60|
|Major Issues count|New code|60|
|Blocker Issues count|Overall code|250|
|Critical Issues count|Overall code|300|
|Major Issues count|Overall code|350|
|Reopened Issues count|Overall code|IGNORED|
|Comment density|Overall code|IGNORED|
|Duplicated blocks count|Overall code|IGNORED|
|Code coverage|Overall code|5%|
|Maintainability Rating|Overall code|D|
|Reliability Rating|Overall code|E|
|Security Rating|Overall code|E|
|OWASP critical vulnerabilities|Overall code|0|
|OWASP high vulnerabilities|Overall code|0|
|OWASP medium vulnerabilities|Overall code|IGNORED|
|OWASP low vulnerabilities|Overall code|IGNORED|

### ApplaudoStudios - Silver

Basic code quality standard, **is mainly intended for projects that are in MVP stage, or transitioning to this state**. Code quality is increased but still very flexible. OWASP ISO complain is always ensured and medium/low issues are still ignored.

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|
|Critical Issues count|New Code|50|
|Major Issues count|New code|50|
|Blocker Issues count|Overall code|0|
|Critical Issues count|Overall code|150|
|Major Issues count|Overall code|150|
|Reopened Issues count|Overall code|100|
|Comment density|Overall code|7%|
|Duplicated blocks count|Overall code|20|
|Code coverage|Overall code|30%|
|Maintainability Rating|Overall code|C|
|Reliability Rating|Overall code|D|
|Security Rating|Overall code|D|
|OWASP critical vulnerabilities|Overall code|0|
|OWASP high vulnerabilities|Overall code|0|
|OWASP medium vulnerabilities|Overall code|10|
|OWASP low vulnerabilities|Overall code|20|

### ApplaudoStudios - Gold

Top code quality standard, **is mainly intended for projects that are in production stage, or transitioning to this state**. Code quality is now a bit more rigorously in all its metrics and OWASP ISO complain is always ensured with medium/low issues conditions.

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|
|Critical Issues count|New Code|0|
|Major Issues count|New code|25|
|Blocker Issues count|Overall code|0|
|Critical Issues count|Overall code|0|
|Major Issues count|Overall code|100|
|Reopened Issues count|Overall code|100|
|Comment density|Overall code|15%|
|Duplicated blocks count|Overall code|10|
|Code coverage|Overall code|60%|
|Maintainability Rating|Overall code|B|
|Reliability Rating|Overall code|C|
|Security Rating|Overall code|C|
|OWASP critical vulnerabilities|Overall code|0|
|OWASP high vulnerabilities|Overall code|0|
|OWASP medium vulnerabilities|Overall code|5|
|OWASP low vulnerabilities|Overall code|10|

### ApplaudoStudios - Diamond

Top code quality standard, **is mainly intended for projects that are in production stage and requires top quality**. Code quality is now very rigorously in all its metrics and OWASP ISO complain is always ensured with medium/low issues conditions.

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|

|   |   |   |
|---|---|---|
|**Metric**|**Code type**|**Quality Gate Condition**|
|Critical Issues count|New Code|0|
|Major Issues count|New code|0|
|Blocker Issues count|Overall code|0|
|Critical Issues count|Overall code|0|
|Major Issues count|Overall code|0|
|Reopened Issues count|Overall code|50|
|Comment density|Overall code|20%|
|Duplicated blocks count|Overall code|5|
|Code coverage|Overall code|80%|
|Maintainability Rating|Overall code|A|
|Reliability Rating|Overall code|A|
|Security Rating|Overall code|A|
|OWASP critical vulnerabilities|Overall code|0|
|OWASP high vulnerabilities|Overall code|0|
|OWASP medium vulnerabilities|Overall code|0|
|OWASP low vulnerabilities|Overall code|5|


## ⭐ Code Quality Score

The Code Quality Score provides a quantitative measure of a project's overall code quality based on SonarQube analysis. This scoring system helps teams track their code quality progress and identify areas for improvement.

### Scoring Formula

Every project starts with a perfect score of 100 points. The final score is calculated by subtracting penalty points for each metric that fails to meet the quality gate conditions. The formula is:

```
Final Score = 100 - ∑(Failed Metric Penalties)
```

### Scoring Process

1. **Initial Score**: Project starts with 100 points
2. **Metric Evaluation**: Each metric is evaluated against its corresponding quality gate condition
3. **Penalty Application**: For each failed metric, subtract its normalized penalty value from the total score
4. **Final Calculation**: The remaining points represent the project's final quality score

### Example Calculation

Consider a project with the following failed metrics:
- New Security Rating (D) = -7.63 points
- Code Coverage (45% when required 60%) = -4.59 points
- Major Violations (120 when limit is 100) = -3.06 points

Final Score = 100 - (7.63 + 4.59 + 3.06) = 84.72 points

### Score Interpretation

| Score Range | Quality Level | Recommendation |
|-------------|---------------|----------------|
| 90-100 | Excellent | Maintain current practices |
| 80-89 | Good | Minor improvements needed |
| 70-79 | Fair | Attention required |
| < 70 | Poor | Immediate action needed |
| < 40 | Critical | Urgent action needed |

### Metric Penalties

The penalty values for each metric are carefully weighted based on their impact on code quality, security, and maintainability. The complete penalty table is provided below, with values normalized to total 100 points.

####  **Normalized Metric Penaltys (Sorted by Penalty - Total = 100)**

| Metric Key                     | Description                                                                                  | Normalized Penalty | Rationale                                                              |
| ------------------------------ | -------------------------------------------------------------------------------------------- | ------------------- | ---------------------------------------------------------------------- |
| `new_security_rating`          | Rating of newly added code based on the number and severity of security vulnerabilities.     | **7.63**            | New vulnerabilities are critical and often a red flag for regressions. |
| `new_reliability_rating`       | Rating of newly added code based on the number and severity of bugs.                         | **6.87**            | Shows whether new changes introduce high-risk instability.             |
| `security_rating`              | Rating based on the number and severity of security vulnerabilities in the overall codebase. | **6.87**            | Legacy security issues can still be exploitable.                       |
| `critical_severity_vulns`      | Number of dependencies with known vulnerabilities of critical severity.                      | **6.87**            | Enterprise standards (e.g. PCI-DSS, NIST) treat these as showstoppers. |
| `new_coverage`                 | Code coverage by unit tests on newly added code.                                             | **6.11**            | Developers should write tests for new features or fixes.               |
| `reliability_rating`           | Rating based on the number and severity of bugs in the overall codebase.                     | **6.11**            | Important but usually a legacy concern.                                |
| `new_critical_violations`      | Number of Critical severity issues introduced in the new code.                               | **6.11**            | Good proxy for high-impact violations introduced in PRs.               |
| `critical_violations`          | Number of issues with Critical severity – bugs or vulnerabilities with high impact.          | **5.35**            | Still important, but less urgent than new ones.                        |
| `blocker_violations`           | Number of issues with Blocker severity – issues that must be fixed immediately.              | **5.35**            | Some may not be security-critical but still block builds or releases.  |
| `high_severity_vulns`          | Number of dependencies with known vulnerabilities of high severity.                          | **5.35**            | Often fixed in regular patch cycles, but still important.              |
| `new_maintainability_rating`   | Rating of newly added code based on the presence of code smells.                             | **4.59**            | Affects future scalability. Critical in growing teams.                 |
| `sqale_rating`                 | Rating based on the maintainability of the overall codebase.                                 | **4.59**            | Legacy tech debt. Important for refactoring strategies.                |
| `coverage`                     | Code coverage by unit tests on the entire codebase.                                          | **4.59**            | A must for core areas, less so for stale legacy files.                 |
| `new_duplicated_lines_density` | Percentage of duplicated lines in newly added code.                                          | **3.82**            | Signals copy-paste or lack of reuse practices.                         |
| `medium_severity_vulns`        | Number of dependencies with known vulnerabilities of medium severity.                        | **3.82**            | Still CVEs, but often not exploitable or lower risk.                   |
| `new_major_violations`         | Number of Major severity issues introduced in the new code.                                  | **3.82**            | Typically non-critical, but shows sloppy dev hygiene.                  |
| `major_violations`             | Number of issues with Major severity – issues that can cause problems but not critical ones. | **3.06**            | Usually not a blocker for most teams unless accumulating fast.         |
| `duplicated_blocks`            | Number of duplicated code blocks found in the codebase.                                      | **3.06**            | Increases maintenance cost.                                            |
| `comment_lines_density`        | Density of comment lines in the code.                                                        | **2.29**            | Important for readability, but not a showstopper unless extremely low. |
| `reopened_issues`              | Number of issues that were closed/resolved and then reopened.                                | **2.29**            | Might show QA or triage flaws, but rarely critical.                    |
| `low_severity_vulns`           | Number of dependencies with known vulnerabilities of low severity.                           | **1.53**            | Often accepted or fixed passively.                                     |



## 💸 Technical Debt

Also known as code debt or design debt, is a term used in software development to describe the cost of additional rework caused by initially choosing a quicker delivery over clean, efficient code that will take longer.

### Manage Technical Debt

In order to administrate and have visibility of the technical debt, it is recommended to use tools like Code Analyzers and Jira to track the issues.

#### Identify and track technical debt

1. Integrate a code analyzer tool (e.g., SonarQube, Codacy, ESLint, etc.) into the CI/CD pipelines in the projects.
    
2. It is recommended to run an analysis every time a pipeline runs, for example when a PR is merged and a deployment is made to an environment like development, staging, production, etc. depending on the project, in case that is needed also the development team must be able to run an on-demand analysis.
    
3. The analysis results will report issues like:
    
    - **Code Smells**: duplicated code, long methods, magic numbers, large classes, etc.
        
    - **Bugs**: potential errors, vulnerabilities or defects in a computer program, to produce incorrect or unexpected results, or to behave in unintended ways.
        
    - **Maintainability**: issues of any characteristic of a software system that make it difficult to understand, modify or test, e.g. tight coupling, insufficient documentation, etc.

4. The results of the analysis must be tracked in a tool like Jira, so that the team can monitor the technical debt and prioritize the issues to fix.

**Tools to track technical debt:**


If your project's CI is using the company's [SonarQube](https://www.sonarqube.org/) instance, you can access the Jira and SonarQube project's Issues dashboard by clicking on the following link:

- [EA SonarQube Projects Dashboard](https://ea.applaudo.com/cqm/sonarqube/projects)

Inside the dashboard, you can search for the project you want to access to see the issues. Just type the project name in the search bar and click on the **"show" button** to access the issues for that project.

{{< asset-img src="/assets/images/ea-sonar-projects.png" alt="Overall risk severity" >}}

After that you will be able to see the SonarQube issues for that specific project.

{{< asset-img src="/assets/images/ea-sonar-project-issues.png" alt="Overall risk severity" >}}

and you can also create a new issue in Jira issue for that SonarQube issue by clicking on the **"Create"** button. After created a button with the issue ID will be displayed and you can click on it to access the issue in Jira.


For a **deep dive** into how the "EA SonarQube Projects Dashboard" works,  you can check the following links:

- [Sonarqube Issues Tracking - Video Recording - Spanish](https://applaudostudios.sharepoint.com/:v:/r/sites/SoftwareArchitectureTeam/Shared%20Documents/Huddles/Recordings/Pragmatic%20Tech%20Talks/Sonarqube%20Issues%20Tracking-20250421_160402-Meeting%20Recording.mp4?csf=1&web=1&e=6cn8NV&nav=eyJyZWZlcnJhbEluZm8iOnsicmVmZXJyYWxBcHAiOiJTdHJlYW1XZWJBcHAiLCJyZWZlcnJhbFZpZXciOiJTaGFyZURpYWxvZy1MaW5rIiwicmVmZXJyYWxBcHBQbGF0Zm9ybSI6IldlYiIsInJlZmVycmFsTW9kZSI6InZpZXcifX0%3D)

### Tools to improve code quality and reduce technical debt

- IDE extensions: 
  - VSCode:
    - https://docs.sonarsource.com/sonarqube-for-ide/vs-code/
  - JetBrains
    - https://docs.sonarsource.com/sonarqube-for-ide/intellij/



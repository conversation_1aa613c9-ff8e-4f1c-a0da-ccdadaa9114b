---
title: "Techical debt management"
---

# Techical debt management

## Objectives

- Provide a guide to A<PERSON><PERSON>ado’s Engineers, in order to adopt the best practices in technical debt management.
    
- Automate processes using Code Analyzers and projects’ boards (Jira) to manage technical debt.
    

## Technical Debt

Also known as code debt or design debt, is a term used in software development to describe the cost of additional rework caused by initially choosing a quicker delivery over clean, efficient code that will take longer.

## Manage Technical Debt

In order to administrate and have visibility of the technical debt, it is recommended to use tools like Code Analyzers and Jira to track the issues.

### Identify and track technical debt

1. Integrate a code analyzer tool (e.g., SonarQube, Codacy, ESLint, etc.) into the CI/CD pipelines in the projects.
    
2. It is recommended to run an analysis every time a pipeline runs, for example when a PR is merged and a deployment is made to an environment like development, staging, production, etc. depending on the project, in case that is needed also the development team must be able to run an on-demand analysis.
    
3. The analysis results will report issues like:
    
    - **Code Smells**: duplicated code, long methods, magic numbers, large classes, etc.
        
    - **Bugs**: potential errors, vulnerabilities or defects in a computer program, to produce incorrect or unexpected results, or to behave in unintended ways.
        
    - **Maintainability**: issues of any characteristic of a software system that make it difficult to understand, modify or test, e.g. tight coupling, insufficient documentation, etc.
        
4. Create a Jira ticket for issues identified by the analyzer tool, in Applaudo there is a specific type of ticket for these issues Technical Debt.
    
5. Use Jira’s prioritization features (priority levels, story points, labels, etc.) to rank the technical debt issues based on:
    
    - **Impact:** How severely the issue affects system performance, stability, or security.
        
    - **Urgency:** How quickly the issue needs to be addressed to avoid future problems.
        
    - **Business Value:** The potential benefits of resolving the issue (e.g., improved maintainability, reduced future development costs).
        

### Automatic Jira Tickets Creation

It is recommended to automate the process as much as possible, there are some options to automate this process, for example using Automation rules for Jira, and a code analyzer that provides webhook integration:

1. Create a new automation rule in Jira.
    
    1. Triger: Select **Incoming webhook trigger**, Jira will provide a unique URL for the webhook. This URL will look something like: https://Jira-instance.atlassian.net/rest/api/3/webhook/incoming/{your-webhook-id}.
        
    2. Conditions: Add a **condition** to check if the branch is main or master (to create issues analyzed only in main or master branch), use the **If/else block** and specify the condition to match the branch name from the webhook payload.
        
    3. Action: select **Create Issue** and add the necessary details like project, issue type, summary, description, other fields (priority, labels, components, etc.), information can also be taken from webhook payload.
        
2. Configure the code analyzer tool to send **webhook** notifications to Jira (use the URL generated in the rule’s trigger in Jira) when new issues are identified (Most code analyzers provide webhooks or API integration options).
    
3. Set up webhook payload, ensure the webhook payload includes relevant information like the branch name, issue details, and any other data needed to create Jira tickets.
    
4. example of an issue in payload, an analyzer generated unique id can be used to avoid duplicates.
    

```
{
  "id": "uuid-123e4567-e89b-12d3-a456-************", //analyzer unique id
  "file": "src/main/java/com/example/MyClass.java",
  "line": 123,
  "message": "Potential Null Pointer Exception on 'myObject'",
  "severity": "Major",
  "rule": "NP_NULL_ON_DEREFERENCE" 
}
```

5. To authorize the request, an API token can be generated from Jira account settings.
    

### Categorize and group issues

In case the case that the analyzer tool is reporting a high volume of issues in a project in particular, is it recommended to categorize and group the issues, the following is an example.

1. Most of the code analyzer tools provide built-in mechanism for grouping issues, categorization can be done like.
    
    1. Rules: Group issues based on the specific rules they violate (Null Pointer, Security vulnerabilities, code style, etc.).
        
    2. File types: example (java, javascript, ect.).
        
    3. Custom Tags or Labels: some analyzer tools allow to add custom tags to issues; this provides a customized level of categorization.
        
2. configure payload structure in the analyzer tool, example.
    

```
{
  "groups": [
    { 
      "name": "Null Pointer Checks", 
      "issues": [ 
        // ... individual issue objects ... 
      ]
    },
    { 
      "name": "Security Vulnerabilities", 
      "issues": [ 
        // ... individual issue objects ... 
      ]
    },
    // ... more groups
  ]
}
```

3. individual issues must contained necessary information.
    
4. This type of customization adds complexity to create the Jira tickets, so the Automation rule must be customized to create tickets for each group in the webhook payload, one option is to use plugin like **ScriptRunner** for Jira add-on.
    
5. Example of a script to create one ticket per issue group
    

```
import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.issue.Issue
import com.atlassian.jira.issue.IssueFactory
import com.atlassian.jira.project.Project
import com.atlassian.jira.project.ProjectManager

def issueFactory = ComponentAccessor.getComponent(IssueFactory)
def projectManager = ComponentAccessor.getComponent(ProjectManager)

def webhookData = context.getWebhookEvent().getBody()

// Extract groups from the webhook payload
def groups = webhookData.get("groups")

// Iterate over each group
for (group in groups) {
    def groupName = group.get("name")
    def groupDescription = group.get("description") 
    def issues = group.get("issues") 

    // Create a new issue for the group
    def project = projectManager.getProjectObjByKey("YOUR_PROJECT_KEY") 
    def issue = issueFactory.getIssue()
    issue.setProject(project)
    issue.setSummary(groupName)
    issue.setDescription(groupDescription ? groupDescription : "Issues in group: " + groupName) 
    issue.setIssueTypeObject(issueManager.getIssueTypeObject("Your Issue Type")) 

    // Create the issue
    issueManager.createIssue(issue) 

    // Optionally: Add sub-tasks for individual issues within the group
    // (Consider limitations on the number of sub-tasks)
    // for (issueData in issues) {
    //     // Create a sub-task for each issue
    //     // ...
    // }
}
```

6. The **Action** in the Automation rule has to be changed to **“Run Script “**, and then select the script previously created.

### Manually selected issues and create Jira tickets.

Sometimes it is required to manually select and send one or a group of issues.

**Using a webhook.**

1. Select the issues required and export selected issues in a specific format (most code analyzer allow to import the issues as JSON, CSV, etc.).
    
2. prepare webhook payload, with the issues needed.
    
3. In order to send the webhook a tool like curl must be used, for example:
    

```
curl -u 'your_username:your_api_token' -H "Content-Type: application/json" -d '{ 
  "issues": [ 
    // ... your issue data ... 
  ] 
}' "YOUR_JIRA_WEBHOOK_URL"
 
```

**Leverage Jira rest API.**

1. Select the issues required and export selected issues in a specific format (most code analyzers allow to import the issues as JSON, CSV, etc.).
    
2. Ensure your JSON to be sent contains the necessary fields to create an issue in Jira. Here's an example of what your JSON file (issue.json) might look like:
    

```
{
  "fields": {
    "project": {
      "key": "PROJ"
    },
    "summary": "Issue created from JSON file",
    "description": "This issue was created using a JSON file and Jira REST API.",
    "issuetype": {
      "name": "Task"
    }
  }
}
```

3. use a tool like curl or postman to send the request, or in order to automate the process the JSON can also be saved in a file to be read and sent by a script, the following is a python script example to achieve this.
    

```
import requests
import json

# Jira credentials and URL
jira_url = 'https://jira-instance.atlassian.net/rest/api/2/issue'
username = '<EMAIL>' //replace with jira credentials
api_token = 'your-api-token' //replace with jira credentials

# Headers for the request
headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

# Read the JSON file
with open('issue.json', 'r') as file:
    issue_data = json.load(file)

# Make the POST request to create the issue
response = requests.post(jira_url, headers=headers, auth=(username, api_token), json=issue_data)

# Check the response
if response.status_code == 201:
    print('Issue created successfully:', response.json())
else:
    print('Failed to create issue:', response.status_code, response.text)
```


# SonarQube Issue Grouping Process for Jira Ticket Creation

## Step 1: Retrieve SonarQube Issues
Use the SonarQube API (`/api/issues/search`) to fetch issues:
- Filter by **project key** and **status (OPEN/CONFIRMED/REOPENED)**.
- Include **severity, type, effort estimate, component (file/module), and creation date**.

---

## Step 2: Group by Issue Type & Severity
Prioritize based on severity:
- **Critical & Major Issues:** First priority, as they pose a risk.
- **Minor Issues:** Can be batched together into larger tickets.
- **Info:** Lowest priority, might be deferred.

Then, further group by **issue type**:
- **Bugs** (runtime errors, incorrect logic).
- **Vulnerabilities** (security flaws).
- **Code Smells** (bad practices, maintainability issues).

---

## Step 3: Group by Component or Module
- Keep changes localized by grouping issues affecting the same **service, package, or file**.
- Example: *"Fix code smells in `auth-service`."*

---

## Step 4: Sort by Date (Oldest First)
- Prioritize older issues first (unless recent issues are more critical).
- If a new issue is part of an ongoing refactor, batch it with related older issues.

---

## Step 5: Group by Estimated Effort
Use SonarQube’s **"Effort"** (in minutes) to estimate complexity:
- **Low complexity (≤ 5 min):** Batch **20–30** similar issues per ticket.
- **Medium complexity (5–30 min):** Batch **5–15** per ticket.
- **High complexity (> 30 min):** Limit to **5 or fewer** per ticket.

**Example Ticket:**

- 🔹 Title: Fix code smells in auth-service 
- 🔹 Subtasks: 10 minor code style issues (~5 min each)

---

## Step 6: Adjust for Sprint Scope
- Aim for **~8–16 hours of effort per ticket** (based on the sprint’s capacity).
- Ensure each ticket is **achievable in one sprint (2 weeks)**.
- If needed, break larger groups into multiple tickets.

---

## Step 7: Create Jira Tickets via API
- Create a **parent Jira ticket** for each group.
- Add SonarQube issues as **subtasks** with descriptions and file references.
- Assign tickets to relevant teams based on module ownership.

---

## Final Output Example (Jira Ticket Structure)

### **Ticket: "Fix Code Smells in Payment Service"**
#### **Subtasks:**
✅ Remove unused imports (5 issues)  
✅ Rename ambiguous variables (10 issues)  
✅ Fix deprecated API usage (3 issues)  
✅ Refactor duplicated logic (2 issues) 


---



## **1️⃣ Jira Board Setup for Tech Debt**

- **Create a new Jira board** named **"Tech Debt"** under your project.
- **Use custom filters (JQL) to show only Tech Debt issues**
    - Example: `project = YOUR_PROJECT AND labels = "tech-debt"`
- **Columns based on the workflow**, such as:
    - **Backlog** → **To Do** → **In Progress** → **Code Review** → **Done**

---

## **2️⃣ Jira Ticket Fields to Use**

### **✅ Priority Levels**

Set the priority based on **SonarQube severity**:

|SonarQube Severity|Jira Priority|
|---|---|
|**Blocker**|Highest|
|**Critical**|High|
|**Major**|Medium|
|**Minor**|Low|
|**Info**|Lowest|

### **✅ Labels**

- **`tech-debt`** → Assigned to all issues related to technical debt.
- **`security`** → Specifically for **security vulnerabilities**.
- **`code-smell`** → For maintainability issues.
- **`bug-fix`** → If the issue is classified as a bug.

**Example JQL to find security issues in the Tech Debt board:**



`project = YOUR_PROJECT AND labels IN ("tech-debt", "security") ORDER BY priority DESC`

### **✅ Components**

Group issues by their **affected service/module**:

- `authentication-service`
- `payment-service`
- `user-profile-service`
- `frontend`

### **✅ Subtasks**

Each ticket should have **subtasks for each SonarQube issue**.  
For example:  
🔹 **Main Ticket:** _Fix Code Smells in `auth-service`_

- 🔹 _Subtask:_ Remove unused imports
- 🔹 _Subtask:_ Fix deprecated API usage

---

## **3️⃣ Recommended Jira Ticket Structure**

**Title:** `[Tech Debt] Fix Code Smells in Payment Service`  
**Description:**



This task covers a batch of **code smells** found in SonarQube.   
- **Service:** `payment-service` 
- **SonarQube Severity:** Major 
- **Effort Estimate:** ~6 hours 
- **Related Security Issues:** No

**Labels:** `tech-debt`, `code-smell`  
**Priority:** Medium  
**Components:** `payment-service`  
**Subtasks:**  
✅ Remove unused imports (5 issues)  
✅ Rename ambiguous variables (10 issues)  
✅ Fix duplicated logic (3 issues)


---

### Resources



- IDE extensions: 
  - VSCode:
    - https://docs.sonarsource.com/sonarqube-for-ide/vs-code/
  - JetBrains
    - https://www.jetbrains.com/help/idea/sonarcloud.html
- Jira integration:
  - https://marketplace.atlassian.com/apps/1217471/sonarqube-connector-for-jira?tab=pricing&hosting=cloud


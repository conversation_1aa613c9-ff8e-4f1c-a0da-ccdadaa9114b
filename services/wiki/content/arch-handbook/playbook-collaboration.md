# **Playbook de Gestión de Bloqueos y Colaboración en el Equipo de Tecnología**

## **1. Gestión de Tickets Bloqueados**

### **1.1. Tickets bloqueados por dependencias sin seguimiento**

#### **Causas comunes:**
- No se identificó la dependencia antes de iniciar el sprint.
- La dependencia no tiene un responsable asignado o una fecha de resolución.
#### **Cómo proceder:**
1. **Durante la planificación del Sprint:**
    - Antes de aceptar un ticket, el equipo debe verificar si tiene dependencias críticas.
    - Si hay dependencias, debe haber un ticket asociado con su responsable y su estado debe estar claro en el backlog.
2. **Si el ticket entra al Sprint y luego se bloquea:**
    - **Reglas de Procedimiento:**
        - Si la dependencia no se puede resolver en el sprint actual, **se mueve al backlog**.
        - Si la dependencia se puede resolver dentro del sprint actual, el ticket queda bloqueado **pero con un responsable claro y una fecha de seguimiento**.
    - Se debe asignar a un responsable para hacer seguimiento cada 48 horas.
3. **Documentación del bloqueo:**
    - En la descripción del ticket, agregar una **sección de Bloqueo** con:
        - Razón del bloqueo.
        - Responsable del seguimiento.
        - Fecha estimada de resolución.
    - Crear un "Epic de Bloqueos" donde se agregan tickets bloqueados para análisis posterior.
        

#### **Responsables:**

- **Product Owner (PO) y Agile Facilitator (AF):** Asegurar seguimiento a los bloqueos.
- **Desarrolladores:** Actualizar la razón del bloqueo en el ticket.
    

---

### **1.2. Tickets bloqueados sin documentación del motivo**

#### **Causas comunes:**

- No hay una política clara sobre documentar bloqueos.
- Falta de comunicación entre desarrolladores y el AF/PO.
    

#### **Cómo proceder:**

1. **Reglas de Procedimiento:**
    - Todo ticket bloqueado debe incluir:
        - Motivo del bloqueo.
        - Fecha en la que se identificó el bloqueo.
        - Responsable del seguimiento.
    - No se permite bloquear un ticket sin documentar el motivo en la herramienta (Jira, Trello, ClickUp, etc.).
2. **Estrategia de Seguimiento:**
    - El AF debe revisar los tickets bloqueados en la **Daily**.
    - Si un ticket lleva más de 3 días bloqueado sin actualización, el PO o AF debe intervenir.
#### **Responsables:**
- **Desarrollador que marca el ticket como bloqueado:** Debe escribir la razón del bloqueo.
- **AF/PO:** Revisar bloqueos y dar seguimiento.
    

---

## **2. Colaboración entre Equipos (Android vs iOS)**

#### **Causas comunes:**

- Falta de comunicación y sesiones de alineación.
- No hay acuerdos de arquitectura de referencia para las plataformas.
- Problemas de relaciones interpersonales.
    

#### **Cómo proceder:**

1. **Definir una reunión de "Sincronización Técnica" cada 2 semanas** entre iOS y Android.
2. **Crear un documento de "Patrones Móviles"**, donde se establezca qué componentes, arquitectura y procesos deben ser comunes entre plataformas.
3. **Reglas de Procedimiento:**
    - Cualquier nueva funcionalidad debe ser presentada en la sincronización.
    - Si hay desacuerdo, el arquitecto móvil (o un facilitador técnico) debe mediar.
    - Si el equipo de iOS no quiere implementar algo, debe justificar técnicamente la razón en el documento compartido.
        

#### **Responsables:**
- **Líder técnico de mobile.**
- **Arquitecto de software.**
- **Product Owner.**

---

## **3. Conflicto entre Arquitectos (Nuevo vs. Antiguo)**

#### **Causas comunes:**
- Falta de claridad en roles y responsabilidades.
- El arquitecto antiguo no delega adecuadamente.
- El nuevo arquitecto no sabe qué tan autónomo puede ser.
    

#### **Cómo proceder:**
1. **Definir roles y responsabilidades claras** en un documento de trabajo.
2. **Reunión de alineación** donde ambos arquitectos definan cómo van a dividir sus iniciativas.
3. **Reglas de Procedimiento:**
    - No hay reporte de uno al otro.
    - Si hay conflictos, el facilitador del equipo o el Tech Lead debe mediar.
    - Cada arquitecto debe tener autonomía en su área de trabajo.
        

#### **Responsables:**
- **CTO o líder técnico del equipo.**
- **Agile Facilitator o Tech Lead.**
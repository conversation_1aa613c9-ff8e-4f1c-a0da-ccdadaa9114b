---
title: "ADR: Architectural Decision Records"
categories:
  - Architecture
---


## **Introduction**

Architectural Decision Records (ADRs) are a critical part of documenting the architectural decisions made during a project. They capture the context, decision, reasoning, and implications in a structured format, ensuring that the rationale behind decisions is transparent and accessible to both technical and non-technical stakeholders. ADRs are vital for maintaining a clear historical record of decisions, which can help with future decision-making, onboarding, maintenance and understand the “whys" behind past decisions

## **Importance of Version Control**

ADRs should be stored in a version control system to maintain a comprehensive history of decisions. This ensures that the evolution of the architecture is well-documented and that previous decisions can be referenced or revisited as needed. We recommend using Confluence for storing ADRs, as it offers built-in version control, collaborative features, and ease of use for both technical and non-technical team members.

There are other tools like git or CLI tools that may be used by technical users like [Nat Pryce ADR Tool](https://github.com/npryce/adr-tools "https://github.com/npryce/adr-tools")

## **Guiding Principles**

1. **Maintain Simplicity:** Keep ADRs concise but informative. Focus on the key elements that influence the decision.
    
2. **Collaborative Documentation:** Use a tool like Confluence that allows easy access, collaboration, and version control.
    
3. **Amend, Don’t Replace:** If a decision needs to be revisited, consider amending the existing ADR rather than creating a new one. This maintains the continuity of decisions and avoids fragmentation.

## ADR Template

1. **Date**  
    _Date & time of ADR creation_  
      
    
2. **Title**  
    _A short, descriptive title of the decision._  
      
    
3. **Status**  
    _Approved/Rejected/Proposed/Deprecated_  
      
    
4. **Decision**  
    _A brief summary of the decision made._  
      
    
5. **Evaluation Criteria**  
    _The criteria used to evaluate the different options (e.g., cost, performance, scalability, etc.)._  
      
    
6. **Candidates to Consider**  
    _A list of the options considered during the decision-making process._  
      
    
7. **Research and Analysis of Each Candidate**
    
    1. **Cost Analysis**
        
    2. _**A SWOT (Strengths, Weaknesses, Opportunities, Threats) analysis for each candidate.**_  
        _A SWOT (Strengths, Weaknesses, Opportunities, Threats) analysis for each candidate._  
          
        
8. **Implications of the Decision**  
    _The impact of the decision on the project, including any trade-offs or potential risks._  
      
    
9. **Constraints of the Decision**  
    _Any constraints or limitations that were considered or that will result from this decision._  
      
    
10. **Related Decisions**  
    _References to other ADRs or related architectural decisions._  
      
    
11. **List of People or the Person Who Signed Off the Decision**  
    _The individuals or stakeholders who approved or were involved in making the decision._  
      
    
12. **Evidences & Attachments**  
    _Any relevant document and evidence of the decision making process_


## ADR Example

### **ADR-004: Selection of Database Technology for Pharmaceutical E-commerce Platform**  
_Date published: 08/08/2024_

|   |   |
|---|---|
|**Status**|**✅ Approved**|

4. **Decision**
    

We have chosen PostgreSQL as the primary database for our pharmaceutical e-commerce platform.

5. **Evaluation Criteria**
    

- **Cost**: Total cost of ownership, including licensing, infrastructure, and operational costs.
    
- **Scalability**: Ability to handle increasing volumes of data and transactions.
    
- **Performance**: Efficiency in handling queries, particularly with complex data structures.
    
- **Security**: Support for advanced security features critical for handling sensitive medical and financial data.
    
- **Community and Support**: Availability of community support, documentation, and professional services.  
      
    

6. **Candidates to Consider**
    

- MySQL
    
- PostgreSQL
    
- MongoDB
    
- SQL Server
    

7. **Research and Analysis of Each Candidate**
    
    1. **Cost Analysis**
        

|   |   |   |
|---|---|---|
|**Database**|**Analysis**|**Licensing Anual Cost**|
|**MySQL**|Free and open-source, with an optional enterprise edition available at a cost. Infrastructure and operational costs are moderate|$0.00|
|**PostgreSQL**|Free and open-source. Infrastructure and operational costs are similar to MySQL but potentially lower due to more advanced features reducing the need for additional tools.|$0.00|
|**MongoDB**|Open-source version is free, but the enterprise version and cloud services are paid. Higher operational costs due to the need for specific infrastructure for optimal performance||
|**SQL Server**|Proprietary and licensed, with significant costs associated with licensing, especially in a high-scale environment. Infrastructure costs are higher due to the need for Windows Server environments.||

b. **SWOT**

|   |   |   |   |   |
|---|---|---|---|---|
|**Database**|**Strengths**|**Weakness**|**Opportunities**|**Threats**|
|MySQL|- Wide adoption, strong community support, easy to use, and reliable.|- Limited support for complex queries and advanced data types.|- Integrations with many open-source tools and platforms.|- Less capable in handling large-scale, complex transactions compared to other options.|
|PostgreSQL|- Advanced features (e.g., full-text search, JSON support), strong compliance with SQL standards, robust performance.|- Slightly steeper learning curve than MySQL.|- Excellent for handling complex queries and large datasets, with strong community support.|- None significant.|
|MongoDB|- Flexible schema design, excellent for unstructured data, horizontal scalability.|- Not ideal for transactional workloads, complexity in ensuring ACID compliance.|- Ideal for handling large volumes of unstructured data.|- Potentially high costs and complexity in managing data consistency.|
|SQL Server|- Strong performance in Windows environments, robust security features, extensive support and documentation.|- High licensing costs, less flexible than open-source alternatives.|- Integration with Microsoft tools and services.|- Vendor lock-in, significant ongoing costs.|

c. benchmark of features  
  

|   |   |   |   |   |
|---|---|---|---|---|
|Feature/Aspect|MySQL|PostgreSQL|SQL Server|MongoDB|
|**Database Type**|Relational (SQL)|Relational (SQL)|Relational (SQL)|NoSQL (Document-oriented)|
|**Performance**|High read speed, optimized for OLAP|High write and read performance, suitable for OLTP and OLAP|High performance for OLTP, good for complex queries|High read and write speed for unstructured data, optimized for large datasets|
|**Scalability**|Vertical and limited horizontal|Vertical and horizontal (supports sharding)|Vertical and limited horizontal|Horizontal (built-in sharding)|
|**Support for ACID Transactions**|Yes (with InnoDB engine)|Yes|Yes|Yes (for single documents)|
|**Data Storage Model**|Table-based|Table-based|Table-based|JSON-like documents|
|**Schema Flexibility**|Rigid (fixed schema)|Flexible (supports JSON)|Rigid (fixed schema)|Highly flexible (schema-less)|
|**Query Language**|SQL|SQL|T-SQL (Transact-SQL)|MQL (MongoDB Query Language)|
|**Indexing Support**|Yes|Yes|Yes|Yes|
|**Replication**|Master-slave, Group replication|Master-slave, Multi-master|Transactional replication, Always On availability groups|Master-slave, replica sets, sharding|
|**Backup and Restore**|Yes (mysqldump, mysqlhotcopy)|Yes (pg_dump, pg_basebackup)|Yes (native tools, third-party tools)|Yes (mongodump, mongorestore)|
|**Full-Text Search**|Limited|Yes|Yes (Full-Text Search feature)|Yes (text index)|
|**Stored Procedures**|Yes|Yes|Yes|No|
|**User-Defined Functions (UDFs)**|Yes|Yes|Yes|No|
|**JSON Support**|Limited|Native support|Yes (JSON functions)|Native (primary data format)|
|**Community and Support**|Large community, widely used|Strong community, actively developed|Large community, strong enterprise support|Large community, strong enterprise support|
|**Licensing**|Open-source (GPL)|Open-source (PostgreSQL License)|Proprietary (commercial license)|Open-source (SSPL, AGPL for older versions)|
|**Operating System Compatibility**|Cross-platform|Cross-platform|Windows (primary), Linux|Cross-platform|
|**Use Cases**|Web applications, e-commerce|Complex queries, data warehousing|Enterprise applications, BI|Big data, real-time analytics, content management|
|**Geospatial Data Support**|Yes (with plugins)|Yes (native support)|Yes (spatial data types)|Yes (2d and 3d geospatial indexes)|

Key Points

- **MySQL**: Popular for web applications due to its speed and ease of use. Best suited for read-heavy operations.
    
- **PostgreSQL**: Known for its robustness, extensibility, and compliance with SQL standards. Suitable for complex queries and analytics.
    
- **SQL Server**: Offers strong integration with Microsoft products and is widely used in enterprise environments. Provides advanced features like full-text search and analytical tools.
    
- **MongoDB**: A leading NoSQL database with flexible schema design, ideal for handling unstructured data and scaling horizontally.
    

8. **Implications of the Decision**
    

- **PostgreSQL** will provide a robust, cost-effective solution capable of handling the complex queries and data structures required by our pharmaceutical e-commerce platform. It also offers strong support for scalability and security, both critical for our operations.
    
- This decision enables future expansion with minimal disruption, given PostgreSQL’s flexibility and comprehensive feature set.
    

9. **Constraints of the Decision**
    

- **Learning Curve**: Team members familiar with MySQL or other simpler databases may require training to fully leverage PostgreSQL’s advanced features.
    
- **Infrastructure**: While PostgreSQL is versatile, it may require careful optimization to ensure peak performance in our specific environment.
    

10. **Related Decisions**
    

- ADR-002: **Selection of Cloud Hosting Provider**
    
- ADR-003: **Data Encryption Strategy**
    

11. **List of People or the Person Who Signed Off the Decision**
    

|   |   |
|---|---|
|**Name**|**Position**|
|Nelson Rivera ([<EMAIL>](mailto:<EMAIL> "mailto:<EMAIL>"))|Product Manager|
|Mauricio Jovel ([<EMAIL>](mailto:<EMAIL> "mailto:<EMAIL>"))|Lead Backend Developer|
|Luis Chong ([<EMAIL>](mailto:<EMAIL> "mailto:<EMAIL>"))|Backend Developer|

12. Evidences & Attachments  
      
    
13. Ammends
    

08/23/24: v1.1 Introduced the related decisions section
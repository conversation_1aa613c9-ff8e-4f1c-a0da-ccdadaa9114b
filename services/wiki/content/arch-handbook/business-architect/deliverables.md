---
title: "Business Architect Deliverables"
categories:
  - Architecture
---

# Business Architect Deliverables


## Project Discovery


**Minimal list of deliverables:**

|#|Deliverable|Description|Accountable|
|---|---|---|---|
|1|**Discovery Summary Document**|Outlines the project's background, goals, objectives, discovery timeline, and stakeholders involved.|Product Owner|
|2|**MVP Definition and Product Roadmap**|Detailed roadmap with epics and high-level user stories for the initial product release.|Product Owner|
|3|**User Story Mapping**|Visual exercise mapping MVP features and subsequent stages using user story format.|Product Owner|
|4|**Non-functional Requirements and Quality Attributes Definition**|Defines non-functional requirements and quality attributes that the product must meet.|Business Architect|
|5|**UX and UI Design Concepts**|Preliminary UX wireframes, UI design concepts, and Design System foundations ensuring alignment with user expectations.|UX/UI Designer, supported by Business Architect|
|6|**Risk Assessment and Mitigation Strategy**|Analysis of potential risks and challenges during development with corresponding mitigation strategies.|Business Architect|
|7|**Architecture and Technology Recommendations**|High-level architecture diagram with proposed technology stack and infrastructure.|Business Architect (with Technical Lead/Architect)|
|8|**Project Plan**|Initial project plan detailing milestones, timelines for the discovery phase.|Product Owner|
|8|**Resource Estimates**|Effort estimates for the discovery phase.|Business Architect|

## Project execution
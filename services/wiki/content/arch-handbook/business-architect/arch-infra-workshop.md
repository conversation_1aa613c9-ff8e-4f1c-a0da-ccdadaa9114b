---
title: "Workshop: Infrastructure Identification and Architectural Design"
categories:
  - Architecture
---


# Workshop: Infrastructure Identification and Architectural Design

**Duration:** 3 hours  

**Moderators:**  
- Lead Facilitator: Business Architect  
- Thematic Moderator: DevOps Engineer  

## When to Conduct It

This workshop should take place after completing the "big picture" and subprocess exploration workshops. Ideally, this would be after identifying process improvement needs and defining the preliminary solution requirements.

## Workshop Objectives

- Identify the existing infrastructure and current IT systems.
- Discuss the necessary integration requirements.
- Define the change authorization and deployment process.
- Establish data quality and retention policies.
- Propose a preliminary architectural scheme to support the proposed process improvements.

## Key Questions

### Existing Infrastructure and Systems

- What existing systems could be integrated with the new solution?
- What current infrastructure (hardware, software, networks) is being used?
- What limitations exist in the current infrastructure that could affect the new solution?

### Integration

- What data do we need to exchange between the existing systems and the new solution?
- How are integrations currently managed between systems (APIs, Web Services, etc.)?
- Are there specific protocols or standards we must follow for integration?

### Change Authorization Process

- What is the current process for requesting and approving IT changes?
- What roles are involved in this authorization process?
- How can we ensure that this process is agile and meets business needs?

### Deployment Process

- What is the current process for implementing new systems or making changes to existing ones?
- What tools or methodologies do we use for deployment (DevOps, CI/CD, etc.)?
- What planning and contingency measures are required during a deployment?

### Quality

- What quality standards are currently applied to IT systems and processes?
- Are there metrics or key indicators we use to measure quality?
- How is system validation and testing carried out during development?

### Data Retention Policies

- What is the current data retention policy within the organization?
- How are critical data managed and archived in existing systems?
- What regulations or standards (such as GDPR, HIPAA, etc.) must we consider in data management?

### Security Aspects

- What security policies are necessary for the new solution?
- How are access and permissions currently managed?
- Are there cybersecurity frameworks we should follow?

### Agile

- Do teams use Agile methodologies (such as Scrum, Kanban, etc.) to manage their projects? If so, how are they implemented?
- How often do they review and adapt their processes and approaches?

### DevOps

- What automation tools are being used for deployment, continuous integration (CI), and continuous delivery (CD)?
- What metrics are used to measure success in software delivery and operations?
- How are applications monitored and evaluated in production?
- What practices are in place to ensure systems are scalable and resilient?
- How is disaster planning and recovery approached?

### Risks

- What processes are in place to assess and mitigate risks in production?
- How are failures or outages handled in the production environment?

### IT Governance

- Does the organization follow a formal IT governance framework? If so, which one and how is it implemented?
- Do they have established IT policies aligned with best practices such as COBIT or ITIL?
- Is there a formal IT change management process? If so, can you describe how this process works?
- What documentation does the IT team need to perform a production release?
- What tools are used to manage and track changes and their impact on IT services?
- Are there established controls to ensure that changes follow defined policies?
- Are periodic audits or reviews conducted to ensure governance practices are being followed?

## Future Considerations

- What are the growth projections and how should the infrastructure scale accordingly?
- What additional capabilities might be needed as the business evolves?




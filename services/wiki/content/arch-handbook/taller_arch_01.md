# Taller: Identificación de Infraestructura y Diseño Arquitectónico

**Duración:** 3 horas  
**Fecha:** 11/03/2025  

**Moderadores:**  
- Facilitador Principal: Business Architect  
- Moderador <PERSON><PERSON>: Ingeniero DevOps  

## <PERSON><PERSON><PERSON> Realizarlo

Este taller debería llevarse a cabo después de que se hayan completado los talleres sobre el "big picture" y la exploración de subprocesos. Idealmente, esto sería tras haber identificado las necesidades de mejora en los procesos y después de definir los requisitos preliminares de la solución.

## Objetivos del Taller

- Identificar la infraestructura existente y los sistemas actuales de TI.
- Discutir los requisitos de integración necesarios.
- Definir el proceso de autorización de cambios y despliegue.
- Establecer políticas de calidad y retención de datos.
- Proponer un esquema arquitectónico preliminar que apoye las mejoras propuestas en los procesos.

## Preguntas Clave

### Infraestructura Existente y Sistemas

- ¿Cuáles son los sistemas existentes que podrían integrarse con la nueva solución?
- ¿Qué infraestructura actual (hardware, software, redes) se está utilizando?
- ¿Qué limitaciones existen en la infraestructura actual que podrían afectar la nueva solución?

### Integración

- ¿Qué datos necesitamos intercambiar entre los sistemas existentes y la nueva solución?
- ¿Cómo se gestionan actualmente las integraciones entre sistemas (APIs, Web Services, etc.)?
- ¿Existen protocolos o estándares específicos que debamos seguir para la integración?

### Proceso de Autorización de Cambios

- ¿Cuál es el proceso actual para solicitar y aprobar cambios en TI?
- ¿Qué roles están involucrados en este proceso de autorización?
- ¿Cómo podemos asegurar que este proceso sea ágil y responda a las necesidades del negocio?

### Proceso de Despliegue

- ¿Cuál es el proceso actual para implementar nuevos sistemas o realizar cambios en los existentes?
- ¿Qué herramientas o metodologías utilizamos para el despliegue (DevOps, CI/CD, etc.)?
- ¿Qué medidas de planificación y contingencia se requieren durante un despliegue?

### Calidad

- ¿Qué estándares de calidad se aplican actualmente a los sistemas y procesos de TI?
- ¿Existen métricas o indicadores clave que evaluamos para medir la calidad?
- ¿Cómo se realiza la validación y pruebas de los sistemas en desarrollo?

### Políticas de Retención de Datos

- ¿Cuál es la política actual de retención de datos en la organización?
- ¿Cómo se gestionan y archivan los datos críticos en los sistemas existentes?
- ¿Qué regulaciones o normativas (como GDPR, HIPAA, etc.) debemos tener en cuenta en la gestión de datos?

### Aspectos de Seguridad

- ¿Qué políticas de seguridad son necesarias para la nueva solución?
- ¿Cómo se gestionan actualmente los accesos y permisos?
- ¿Existen marcos de ciberseguridad que debamos seguir?

### Agile

- ¿Los equipos utilizan metodologías Agile (como Scrum, Kanban, etc.) para gestionar sus proyectos? Si es así, ¿cómo se implementan?
- ¿Con qué frecuencia revisan y adaptan sus procesos y enfoques?

### DevOps

- ¿Qué herramientas de automatización se están utilizando para el despliegue, la integración continua (CI) y la entrega continua (CD)?
- ¿Qué métricas utilizan para medir el éxito en la entrega de software y operaciones?
- ¿Cómo se monitorean y evalúan las aplicaciones en producción?
- ¿Qué prácticas tienen para asegurarse de que los sistemas sean escalables y resilientes?
- ¿Cómo abordan la planificación y la recuperación ante desastres?

### Riesgos

- ¿Qué procesos tienen en marcha para evaluar y mitigar riesgos en la producción?
- ¿Cómo manejan las fallas o interrupciones que ocurren en el entorno de producción?

### Gobernanza TI

- ¿La organización sigue un marco de gobernanza de TI formal? Si es así, ¿cuál y cómo se implementa?
- ¿Tienen políticas de TI establecidas que se alineen con las mejores prácticas como las de COBIT o ITIL?
- ¿Tienen un proceso formal de gestión de cambios en TI? Si es así, ¿pueden describir cómo funciona este proceso?
- ¿Qué documentación necesita el equipo de TI para realizar un paso a producción?
- ¿Qué herramientas se utilizan para gestionar y rastrear cambios y sus impactos en los servicios de TI?
- ¿Existen controles establecidos para asegurar que los cambios realizados sigan las políticas definidas?
- ¿Se realizan auditorías o revisiones periódicas para asegurar que se cumplan las prácticas de gobernanza?

## Consideraciones Futuras

- ¿Cuáles son las proyecciones de crecimiento y cómo debe escalar la infraestructura?
- ¿Qué capacidades adicionales podrían ser necesarias a medida que el negocio evoluciona?
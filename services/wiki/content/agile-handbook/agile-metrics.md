---
title: "Agile Metrics"
categories:
  - Agile
  - Architecture
  - Handbook
menu:
  main:
    name: Metrics
    parent: Agile Handbook
---

## **Introduction**

This **Metrics Handbook** serves as a guide for effectively tracking and analyzing key performance indicators (KPIs) 
within the team's workflow. It establishes standardized processes to ensure that metrics are collected, analyzed, and utilized to drive continuous improvement.

## Thresholds

| Metric Name                                   | Threshold Project/Client                                                                                                                         | Individual Threshold                 |
| --------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------ |
| [Cycle Time](#cycle-time-hours)               | - Goal `CT <= 86 hours` <br />- Warning: `86 hours < CT <= 105 hours` <br />- Wrong: `CT > 105 hours `                                           | **Same as Project/Client threshold** |
| [Coding Time](#coding-time-hours)             | - Goal `CT <= 35 hours` <br />- Warning: `35 hours < CT <= 42 hours` <br />- Wrong: `CT > 42 hours`                                              | **Same as Project/Client threshold** |
| [Review Time](#review-time-hours)             | - Goal `RT <= 4 hours` <br />- Warning: `4 hours < RT <= 5 hours` <br />- Wrong: `CT > 5 hours`                                                  | **Same as Project/Client threshold** |
| [Deploy Time](#deploy-time-hours)             | - Goal `DT <= 50 hours` <br />- Warning: `50 hours < DT <= 60 hours` <br />- Wrong: `DT > 60 hours`                                              | `N/A`                                |
| [Defect Ratio](#defect-ratio)                 | - Goal `DR <= 25%` <br />- Warning: `25% < DR <= 50%` <br />- Wrong: `DR > 50%`                                                                  | **Same as Project/Client threshold** |
| [Planning Accuracy](#planning-accuracy)       | - Goal: `70% < PA <= 95%`   <br/> - Over Commitment: `CU <= 70%` <br /> - Under Commitment: `UA > 95%`                                           | `N/A`                                |
| [Capacity Utilization](#capacity-utilization) | - Goal: `80% <= CU <= 100%`   <br/> - Warning: `100% < CU <= 120%` <br /> - Over Utilization: `120% > UA` <br /> - Under Utilization: `80% < UA` | `N/A`                                |
| [Estimation Adherence](#estimation-adherence) | - Goal: `80% <= EA <= 100%`   <br/> - Warning: `100% < EA <= 120%` <br /> - Wrong: `120% > EA`                                                   | **Same as Project/Client threshold** |

## Definitions and Formulas

### Cycle Time (hours)

**Definition**

Measure the time taken to complete a task from the moment it is started until it is marked as done.

**Objectives**

- To evaluate the efficiency of the development process.
- To identify bottlenecks in the workflow.

**Variables**

| Variable     | Description                                                                          | Source              | Default |
| ------------ | ------------------------------------------------------------------------------------ | ------------------- | ------- |
| `start_date` | Date when the issue was moved to `In Progress`.                                      | Jira Ticket History |         |
| `end_date`   | Date when the issue was marked as `Done` or when the QA team has approved the issue. | Jira Ticket History |         |

**Formula**

```
cycle_time = percentile_75(date_diff(end_date - start_date).hours)
```
where: 
  - `hours` per day are 24 hours

### Coding Time (hours)

**Definition**

The time it takes from when first time a ticket is move to `In Progress` until a PR is issued or issue is moved to 
`PR Review`.

**Objectives**

- To measure the time spent on actual coding activities.
- To identify areas for improvement in the coding process.

**Variables**

| Variable     | Description                                                          | Source                                    | Default |
| ------------ | -------------------------------------------------------------------- | ----------------------------------------- | ------- |
| `start_date` | Date when the issue was moved to `In Progress`.                      | Jira Ticket History                       |         |
| `end_date`   | Date when the issue was moved to `PR Review` or when a PR is issued. | Jira Ticket History <br /> Git Repository |         |

**Formula**

```
coding_time = percentile_75(date_diff(end_date - start_date).hours)
```
where: 
  - `hours` per day are 24 hours

### Review Time (hours)

**Definition**

The time it takes to complete a code review and get a pull request merged or to move from `PR Review` to `Ready for QA`.

**Objectives**

- To measure the efficiency of the code review process.
- To identify areas for improvement in the review process.

**Variables**

| Variable     | Description                                                               | Source                                    | Default |
| ------------ | ------------------------------------------------------------------------- | ----------------------------------------- | ------- |
| `start_date` | Date when a PR is issue or when the issue was moved to `PR Review`.       | Jira Ticket History <br /> Git Repository |         |
| `end_date`   | Date when the PR is merged or when the issue was moved to `Ready for QA`. | Jira Ticket History <br /> Git Repository |         |

**Formula**

```
review_time = percentile_75(date_diff(end_date - start_date).hours)
```
where: 
  - `hours` per day are 24 hours

### Deploy Time (hours)

**Definition**

The time it takes to merge the code to the QA or UAT branch.

**Objectives**

- To measure the efficiency of the deployment process.
- To identify areas for improvement in the deployment process.

**Variables**

| Variable     | Description                                                               | Source                                    | Default |
| ------------ | ------------------------------------------------------------------------- | ----------------------------------------- | ------- |
| `start_date` | Date when the issue was moved to `Ready for QA`.                          | Jira Ticket History <br /> Git Repository |         |
| `end_date`   | Date when the issue was moved to `Done` or when the QA team has approved. | Jira Ticket History <br /> Git Repository |         |

**Formula**

```
deploy_time = percentile_75(date_diff(end_date - start_date).hours)
```
where: 
  - `hours` per day are 24 hours
  

### Defect Ratio

**Definition**

Evaluates the quality of development by measuring how many defects are generated per delivered ticket. 
A high number may indicate rushed code or insufficient code reviews. This metric helps teams identify areas for 
improvement in code quality and review processes.

**Objectives**
- To assess the quality of code delivered by the team.
- To identify areas for improvement in the development process.
- To ensure that the team is delivering high-quality code with minimal defects.

**Variables**

| Variable                 | Description                                                                                             | Source              | Default |
| ------------------------ | ------------------------------------------------------------------------------------------------------- | ------------------- | ------- |
| `total_delivered_issues` | Total number of issues delivered in a period of time (usually per sprint).                              | Jira Ticket History |         |
| `total_defects`          | Total number of defects reported in a period of time (usually per sprint) that are related to a Ticket. | Jira Ticket History |         |

**Formula**

```
defect_ratio = total_defects / total_delivered_issues × 100
```

### Planning Accuracy

**Definition**

Planning accuracy measures the ratio between planned issues and what were in fact delivered from that list.

**Objectives**

- To assess the effectiveness of sprint planning.
- To identify areas where planning can be improved.
- To ensure that the team is delivering on its commitments.

**Variables**

| Variable                 | Description                                                                                      | Source              | Default |
| ------------------------ | ------------------------------------------------------------------------------------------------ | ------------------- | ------- |
| `total_completed_issues` | Total number of completed (not including added) issues in a period of time (usually per sprint). | Jira Ticket History |         |
| `total_planned_issues`   | Total planned issue.                                                                             | Jira Ticket History |         |

**Formula**

```
planning_accuracy = (total_completed_issues / total_planned_issues) * 100
```

### Capacity Utilization

**Definition**

Measure how many hours of the developer's available time were spent on tasks.

**Objectives**

- To assess the efficiency of resource utilization.
- To identify potential areas for improvement in workload management.
- To ensure that developers are not overburdened or underutilized.
- To identify when the team is spending significant time on non-coding activities, enabling better focus on core 
development tasks.

**Variables**

| Variable      | Description                                                                      | Source              | Default                                      |
| ------------- | -------------------------------------------------------------------------------- | ------------------- | -------------------------------------------- |
| `total_hours` | Total available hours of the developer in a period of time (usually per sprint). | We Plant It         | 8.8 per day.                                 |
| `spent_hours` | Total time that a ticket spend in the stages of `In Progress` and `Code Review`  | Jira Ticket History | Total hours spent on tasks by the developer. |

**Formula**

```
capacity_utilization = (total_hours_available * 0.20) / spent_hours * 100
```

*Note: The `0.20` factor is used to account for the 20% of the time that developers should spend on non-coding 
activities, such as meetings, documentation, and other tasks.*

### Estimation Adherence

**Definition**

Measure how well the team adheres to their time estimations for tasks.

**Objectives**

- To evaluate the accuracy of time estimations made by the team.
- To identify discrepancies between estimated and actual time spent on tasks.
- To improve future estimation practices based on historical data.

**Variables**

| Variable                        | Description                                                                                            | Source              | Default |
| ------------------------------- | ------------------------------------------------------------------------------------------------------ | ------------------- | ------- |
| `total_original_estimate_hours` | Total estimated hours (`Original Estimate`) for all tasks in a period of time (usually per sprint).    | Jira Tickets        |         |
| `total_spent_hours`             | Total actual hours spent on tasks in a period of time in the stage of `In Progress` and `Code Review`. | Jira Ticket History |         |

**Formula**

```
estimation_adherence = (total_spent_hours / total_original_estimate_hours) * 100
```
<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.0.16 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36" version="26.0.16">
  <diagram name="Org Chart" id="DgCRBBlRC16bhSZ3kaTh">
    <mxGraphModel dx="7383" dy="1713" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Uk7nD96oottU1k68nqmY-1" value="Orgchart" style="swimlane;html=1;startSize=20;horizontal=1;containerType=tree;glass=0;" vertex="1" parent="1">
          <mxGeometry x="-4928" y="-54" width="3272" height="1387" as="geometry" />
        </mxCell>
        <UserObject label="Applaudo" treeRoot="1" id="Uk7nD96oottU1k68nqmY-37">
          <mxCell style="whiteSpace=wrap;html=1;align=center;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
            <mxGeometry x="1576" y="65" width="120" height="60" as="geometry" />
          </mxCell>
        </UserObject>
        <mxCell id="Uk7nD96oottU1k68nqmY-38" value="Cesar Bendeck&lt;br&gt;(CEO)" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1571" y="176" width="132.75" height="59" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-39" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-38" parent="Uk7nD96oottU1k68nqmY-1" source="Uk7nD96oottU1k68nqmY-37">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1640.75" y="136" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-44" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-45" source="Uk7nD96oottU1k68nqmY-38" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1249.75" y="-14" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-45" value="Jaime García&lt;br&gt;Chief Operating Officer" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1549" y="286" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-46" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-47" source="Uk7nD96oottU1k68nqmY-45" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1246.75" y="85" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-47" value="Raúl Chacón&lt;br&gt;Head of technology" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1549" y="390" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-48" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-49" source="Uk7nD96oottU1k68nqmY-47" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1238.75" y="188" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-49" value="Technology" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#cce5ff;strokeColor=#36393d;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1549" y="494" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-58" value="Emilio Forrer&lt;br&gt;Architecture Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2317" y="1055" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-61" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-62" source="Uk7nD96oottU1k68nqmY-58" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2343" y="779" as="sourcePoint" />
            <mxPoint x="2519" y="888" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-62" value="Business Architect" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2317" y="1159" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-63" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-64" source="Uk7nD96oottU1k68nqmY-58" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2343" y="779" as="sourcePoint" />
            <mxPoint x="2519" y="888" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-64" value="Application Architect" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2553" y="1161" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-65" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-66" source="Uk7nD96oottU1k68nqmY-58" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1962" y="781" as="sourcePoint" />
            <mxPoint x="2138" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-66" value="Enterprise Architect" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#6a00ff;fontColor=#ffffff;strokeColor=#3700CC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2102" y="1163" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-53" value="Teams" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#cce5ff;strokeColor=#36393d;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2633" y="729" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-52" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-53" source="Uk7nD96oottU1k68nqmY-49" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2970" y="630" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-60" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="Uk7nD96oottU1k68nqmY-1" source="Uk7nD96oottU1k68nqmY-53" target="Uk7nD96oottU1k68nqmY-58">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="3765" y="1021" as="sourcePoint" />
            <mxPoint x="3941" y="1130" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-55" value="Luis Chong&lt;br&gt;Engineering Delivery Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#0050ef;fontColor=#ffffff;strokeColor=#001DBC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2896" y="1052" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-54" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-55" source="Uk7nD96oottU1k68nqmY-53" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2970" y="734" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-33" value="Agile Facilitator" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#0050ef;fontColor=#ffffff;strokeColor=#001DBC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="3010" y="1164.5" width="157" height="55.5" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-34" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;hachureGap=4;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-33" parent="Uk7nD96oottU1k68nqmY-1" source="Uk7nD96oottU1k68nqmY-55">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2340" y="956" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-35" value="Engineering Delivery Leads&lt;br&gt;(New Position)" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#0050ef;fontColor=#ffffff;strokeColor=#001DBC;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="2787" y="1163" width="180" height="62" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-36" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;hachureGap=4;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.506;exitY=0.917;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" target="Uk7nD96oottU1k68nqmY-35" parent="Uk7nD96oottU1k68nqmY-1" source="Uk7nD96oottU1k68nqmY-55">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2486" y="1081" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-78" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-79" source="Uk7nD96oottU1k68nqmY-49" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-148" y="498" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-79" value="Areas" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#cce5ff;strokeColor=#36393d;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1058.7500000000005" y="727" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-80" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-81" source="Uk7nD96oottU1k68nqmY-79" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-165" y="548" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-81" value="Jairo Urbinaa&lt;br&gt;Data Engineering Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#008a00;fontColor=#ffffff;strokeColor=#005700;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1058.75" y="1069" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-82" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-83" source="Uk7nD96oottU1k68nqmY-81" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1508.25" y="575" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-83" value="Data Engineer" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="877.0000000000001" y="1173" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-84" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-85" source="Uk7nD96oottU1k68nqmY-81" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1508.25" y="575" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-85" value="Data Analysts" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1087.75" y="1173" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-87" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-88" source="Uk7nD96oottU1k68nqmY-81" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1508.25" y="575" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-88" value="Data Architects" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#60a917;fontColor=#ffffff;strokeColor=#2D7600;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1301.75" y="1170" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-89" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-90" source="Uk7nD96oottU1k68nqmY-79" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1287" y="689" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-90" value="Rodrigo Morales&lt;br&gt;Cloud Engineering Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#1ba1e2;fontColor=#ffffff;strokeColor=#006EAF;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1607.0000000000005" y="1062" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-91" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-92" source="Uk7nD96oottU1k68nqmY-79" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1287" y="689" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-92" value="Fer Martir" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#647687;fontColor=#ffffff;strokeColor=#314354;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1908.0000000000005" y="1062" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-95" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-96" source="Uk7nD96oottU1k68nqmY-92" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-1921" y="877" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-96" value="UI/UX Designers" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#647687;fontColor=#ffffff;strokeColor=#314354;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1908.0000000000005" y="1166" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-97" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-98" source="Uk7nD96oottU1k68nqmY-90" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-2085" y="1093" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-98" value="Cloud Engineers" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#1ba1e2;fontColor=#ffffff;strokeColor=#006EAF;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1697.0000000000005" y="1166" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-99" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-100" source="Uk7nD96oottU1k68nqmY-90" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-2085" y="1093" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-100" value="DevOps" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#1ba1e2;fontColor=#ffffff;strokeColor=#006EAF;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="1497.7500000000005" y="1166" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-72" value="Diego Rivas&lt;br&gt;Quality Engineering Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#a20025;fontColor=#ffffff;strokeColor=#6F0000;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="562" y="1075" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-73" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-74" source="Uk7nD96oottU1k68nqmY-72" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-727.8699999999999" y="486" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-74" value="Quality Engineer" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#a20025;fontColor=#ffffff;strokeColor=#6F0000;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="459" y="1180" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-76" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-77" source="Uk7nD96oottU1k68nqmY-72" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-727.8699999999999" y="486" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-77" value="Quality Automation" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#a20025;fontColor=#ffffff;strokeColor=#6F0000;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="674" y="1176" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-71" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-72" source="Uk7nD96oottU1k68nqmY-79" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-86.86999999999989" y="627" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-51" value="Elsy Villalobos&lt;br&gt;Software Engineering Manager" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#d80073;fontColor=#ffffff;strokeColor=#A50040;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="144" y="1065" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-50" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-51" source="Uk7nD96oottU1k68nqmY-79" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1544.13" y="687" as="sourcePoint" />
            <mxPoint x="1134.005" y="1056" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-67" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-68" source="Uk7nD96oottU1k68nqmY-51" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1134.005" y="1120.0000000000002" as="sourcePoint" />
            <mxPoint x="1073.005" y="1181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-69" value="" style="edgeStyle=elbowEdgeStyle;elbow=vertical;sourcePerimeterSpacing=0;targetPerimeterSpacing=0;startArrow=none;endArrow=none;rounded=0;curved=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fontSize=12;startSize=8;endSize=8;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" target="Uk7nD96oottU1k68nqmY-70" source="Uk7nD96oottU1k68nqmY-51" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1134.005" y="1120.0000000000002" as="sourcePoint" />
            <mxPoint x="1264.755" y="1181" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-68" value="Software Engineers&lt;br&gt;(Mobile/Web)" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#d80073;fontColor=#ffffff;strokeColor=#A50040;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="27" y="1176" width="181.75" height="64" as="geometry" />
        </mxCell>
        <mxCell id="Uk7nD96oottU1k68nqmY-70" value="Salesforce Engineers" style="whiteSpace=wrap;html=1;align=center;verticalAlign=middle;treeFolding=1;treeMoving=1;newEdgeStyle={&quot;edgeStyle&quot;:&quot;elbowEdgeStyle&quot;,&quot;startArrow&quot;:&quot;none&quot;,&quot;endArrow&quot;:&quot;none&quot;};sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#d80073;fontColor=#ffffff;strokeColor=#A50040;" vertex="1" parent="Uk7nD96oottU1k68nqmY-1">
          <mxGeometry x="226" y="1178" width="181.75" height="64" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

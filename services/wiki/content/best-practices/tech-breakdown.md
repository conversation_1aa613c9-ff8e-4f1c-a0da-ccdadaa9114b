---
title: "Technical Breakdown"
categories:
  - Position
  - Architecture
  - Role
menu:
  main:
    name: "Technical Breakdown"
    parent: Best Practices
---

# Technical Breakdown

## 📝 Introduction

The **Technical Breakdown** is a process that involves breaking down complex technical requirements into smaller, more manageable tasks. This process helps ensure that the development team has a clear understanding of the work required and can estimate the effort needed to complete each task.

## 🎯 Objectives
- Break down complex technical requirements into smaller, more manageable tasks.
- Ensure that the development team has a clear understanding of the work required.
- Estimate the effort needed to complete each task.
- Identify any potential risks or challenges associated with each task.


## Example of a Technical Breakdown "Forgot Password"


### 🎯 User Story: Forgot Password Flow - With Draft PRs

> **As a user**, I want to request a password reset via email, so I can regain access to my account if I forget my password.

---

### 🧩 1. Technical Breakdown (Sub-tasks)

Break the story into **atomic**, testable components:

|Sub-task|Description|
|---|---|
|1. Create reset token model|DB model + migration|
|2. Add API endpoint|`POST /forgot-password` that creates a reset token|
|3. Email service|Generate and send email with reset link|
|4. Frontend form|UI for user to enter email|
|5. Confirmation page|“Email sent” message|
|6. Tests|Unit + integration tests|

---

### 🌿 2. Branch Strategy

Use feature branches for each **sub-task**:

### 🏁 Base Story Branch:

- `feature/forgot-password` ← You will open a **draft PR** here

#### 🧱 Sub-task Branches (from `feature/forgot-password`):

- `feature/forgot-password-model`
- `feature/forgot-password-api`
- `feature/forgot-password-email`
- `feature/forgot-password-ui`

Each sub-task branch will:

1. Be branched off `feature/forgot-password`
2. Have a **small, atomic PR**
3. Be merged **into `feature/forgot-password`**, not `main` yet


### 🔁 3. Pull Request Flow

#### 🟡 Step 1: Open a Draft PR

```
git checkout -b feature/forgot-password
git push origin feature/forgot-password

```

- Open a **draft PR** titled:> `[Draft] Forgot Password Feature – Tracking PR`
- Add a checklist:

```
- [ ] Add model & migration
- [ ] Add API endpoint
- [ ] Send email
- [ ] Frontend form
- [ ] Tests

```

This gives the team early visibility and context.

---

#### ✅ Step 2: Start with the First Small PR

```
# Sub-task 1: model
git checkout -b feature/forgot-password-model feature/forgot-password
# Make changes: add model, write migration
git commit -m "Add reset token model and migration"
git push origin feature/forgot-password-model

```

Open PR:

- Title: `feat: Add reset token model and migration`
- Base: `feature/forgot-password`
- Target: `feature/forgot-password`

After review and approval:

```
git checkout feature/forgot-password
git merge feature/forgot-password-model

```

Repeat this pattern for each sub-task (API, email, UI...).

#### 🔁 Step 3: Merge Final Draft PR

Once all sub-branches are merged and the full story is complete:

1. Mark the `feature/forgot-password` draft PR as **“Ready for Review”**
2. Get a final approval
3. Merge into `main` or `develop`:

```
git checkout main
git merge feature/forgot-password

```

### ✅ Benefits of This Approach

- Keeps PRs small and reviewable (85 - 139 changes ideally or less )
- Allows parallel work if needed
- Keeps the team in sync via the tracking draft PR
- Easy to revert a small PR if needed



### 🚫 No Draft PR: "Forgot Password" Flow

We'll still break the work into small tasks and PRs, but we use **a feature branch and sub-feature PRs** without a central tracking PR.

---

### 🧩 1. Break Down the Story (Same as before)

Same technical sub-tasks:

1. DB model and migration
2. API endpoint
3. Email sender
4. Frontend form
5. Confirmation page
6. Tests

### 🌿 2. Git Branching Strategy

Instead of a draft PR on a parent branch, everything happens in **independent branches**, and all are eventually merged into `main` (or `develop`).

#### 🔹 Base branch: `main` or `develop`

#### 🔸 Sub-feature branches:

- `feature/forgot-password-model`
- `feature/forgot-password-api`
- `feature/forgot-password-email`
- `feature/forgot-password-ui`
- `feature/forgot-password-tests`

Each sub-branch is based on `main` (or your dev branch), developed and tested independently.

---

### 🔁 3. PR Flow

Each sub-task is managed as a **regular PR** targeting `main`:

```
# For the model task
git checkout -b feature/forgot-password-model main
# add files, commit
git push origin feature/forgot-password-model

```

Create PR:

- Title: `feat: Add reset token model and migration`
- Base: `main`

Repeat for each small sub-task.

Each PR is:

- Independent
- Merged into `main` when ready

> 🔒 **Important**: You must **ensure compatibility** between sub-PRs. If API logic depends on the model, you either:
> 
> - Make the model PR first and wait for it to merge
 
> - Or **temporarily duplicate/shared logic** for development, then clean up in a follow-up PR
 

---

### 📋 4. Use Issue or Task to Track Progress

Since there’s no draft PR, use your project management tool (e.g., Jira, Linear, GitHub Projects) to track the sub-tasks.

Example Jira issue:

```
[FORGOT-PASSWORD]
  ├─ [BACKEND] Reset Token Model – DONE ✅
  ├─ [BACKEND] API Endpoint – IN PROGRESS 🛠️
  ├─ [EMAIL] Mailer – NOT STARTED
  ├─ [FRONTEND] UI Form – NOT STARTED
  └─ [TESTS] Integration – NOT STARTED
```

Or add a markdown checklist in the main story ticket.

---

### ✅ Pros and Cons of Not Using Draft PRs

#### ✅ Pros:

- Cleaner Git history on `main`
- No need for coordination across dependent branches

#### ❌ Cons:

- Harder to track the full feature as one unit
- No "early preview" or feedback loop
- CI/CD may break if incomplete parts get merged
- Sub-PRs might block each other if tightly coupled

---

### 👇 Recommended if You Skip Draft PRs

- Use a **label** like `forgot-password` in all related PRs
- Keep PRs **strictly atomic**
- **Communicate** clearly with your team (e.g., in Slack, ticket comments)
- Consider using **feature toggles** to hide incomplete features in production




## Resources

#### Draft PRs
- [Draft pull requests | Bitbucket Cloud | Atlassian Support](https://support.atlassian.com/bitbucket-cloud/docs/draft-pull-requests/)
- [Introducing draft pull requests - The GitHub Blog](https://github.blog/news-insights/product-news/introducing-draft-pull-requests/)
- [Draft merge requests | GitLab Docs](https://docs.gitlab.com/user/project/merge_requests/drafts/)
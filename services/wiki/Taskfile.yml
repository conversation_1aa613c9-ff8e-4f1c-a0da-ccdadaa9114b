# https://taskfile.dev

version: '3'

tasks:
  setup:
    desc: Install dependencies
    cmds:
      - cmd: which hugo || (go install github.com/gohugoio/hugo@latest)
  serve:
    desc: Start Hugo server with live reload
    cmds:
      - hugo server -D -F --disableFastRender --forceSyncStatic --printI18nWarnings --ignoreCache --noHTTPCache
  build:
    desc: Build the Hugo static site
    cmds:
      - npm install
      - hugo --minify --baseURL /wiki --destination dist

  new:
    desc: Create a new Hugo post
    cmds:
      - hugo new content/posts/{{.CLI_ARGS}}.md

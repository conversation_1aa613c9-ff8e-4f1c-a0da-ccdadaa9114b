@tailwind base;
@tailwind components;
@tailwind utilities;

.content-container {
  display: flex;
  flex-direction: row;
}

.main-content {
  flex: 3;
  padding-right: 20px;
}

.toc-container {
  flex: 1;
  position: sticky;
  top: 20px; /* Keeps the TOC visible while scrolling */
  max-height: 90vh; /* Prevents overflow */
  overflow-y: auto;
  padding: 70px 10px 10px 10px;
}


.toc-content ul {
  list-style-type: none;
  padding: 10px;
}

.toc-content li {
  margin-bottom: 5px;
}

.toc-content a {
  text-decoration: none;
  color: #121213;
}

.toc-content strong {
  text-decoration: none;
  font-weight: normal;
}

.toc-content a:hover {
  text-decoration: underline;
}
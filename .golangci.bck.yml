# Enable linters that are useful in most Go projects
linters:
  disable:
    - depguard

  enable:
    - goimports        # Updates your Go import lines, adding missing ones and removing unreferenced ones
    - govet            # Reports suspicious constructs (e.g., format string mismatches)
    - errcheck         # Ensures all errors are checked
    - staticcheck      # Catches common bugs and issues
    # - deadcode         # Finds unused code (deprecated)
    - ineffassign      # Detects ineffectual assignments
    - unused           # Finds unused variables, constants, etc.
    # - structcheck      # Checks for unused struct fields (deprecated)
    - typecheck        # Like 'go type' command, checks for issues in types
    # - varcheck         # Checks for unused global variables (deprecated)
    - gosimple         # Suggests simpler constructs when possible
    - stylecheck       # Enforces Go style rules
    # - nilness          # Detects redundant or missing nil checks
    # - depguard         # Ensures certain dependencies are not used
    - gofmt            # Ensures code is properly formatted
    # - gocyclo          # Detects functions with high cyclomatic complexity (use cyclop instead)
    - dupl             # Detects code duplication
    - whitespace       # Detects unnecessary whitespace issues
    - gosec            # Security checks
    - prealloc         # Preallocate slices
    - unconvert        # Remove unnecessary type conversions
    # - maligned         # Struct alignment for better memory usage (deprecated)
    - govet
    - rowserrcheck     # SQL rows error handling
    # - scopelint        # Narrow variable scopes (deprecated)
    - forbidigo        # Forbid fmt/log in production code
    - wsl              # Consistent whitespace
    - mnd            # Magic number detection
    - goconst          # Detect duplicate string constants
    - goconst          # Detect duplicate string constants
    - gocritic         # Enforce idiomatic Go and optimizations
    - exhaustive       # Ensure all enum values are handled in switches
    - gomodguard       # Enforce dependency management rules
    - goprintffuncname # Enforce correct Printf-style function arguments
    - paralleltest     # Enforce parallel testing
    - revive           # Enforce Go style and performance
    - forcetypeassert  # Check for unchecked type assertions
    - makezero         # Ensure idiomatic slice creation
    - unparam          # Detect unused function parameters
    - noctx            # Detect missing context in function calls
    - godox            # Detect TODO comments
    - funlen           # Enforce max function length
    # - exportloopref    # Catches loop variables captured by reference in closures (deprecated)
    - err113           # Enforces consistent error wrapping practices
    - godox            # Detects `TODO` and `FIXME` comments
    # - interfacer       # Suggests using narrower interfaces (e.g., io.Reader instead of *os.File) (deprecated)
    - thelper          # Ensures test helper functions are marked with `t.Helper()`
    - dogsled        # Warns when named return values are unused in a function
    # - gofumpt        # Enforces stricter formatting rules, like gofmt but stricter (at the moment, it's has a bug)
    - errname        # Enforces consistent naming of error variables (`err`)
    - nolintlint     # Ensures proper use of `nolint` directives
    - revive         # Extensible linter with similar rules to golint but faster and more configurable
    - gochecknoinits # Warns about usage of `init` functions, which are discouraged
    - errorlint      # Enforces proper error unwrapping using `errors.Is` and `errors.As`
    - funlen         # Limits the maximum number of lines in a function
    - gocognit       # Detects high cognitive complexity in functions
    - cyclop         # Detects high cyclomatic complexity, more configurable than gocyclo
    - predeclared    # Detects shadowing of predeclared identifiers (e.g., len, new)
    - forcetypeassert # Detects forceful type assertions that can cause panics
    - nakedret       # Discourages naked returns in long functions
    - lll            # Checks for lines that are too long
    # - ifshort        # Detects `if` statements that could use short variable declarations (deprecated)

# Exclude some linters for specific files or patterns if necessary
linters-settings:
  wsl:
    allow-cuddle-declarations: true
  revive:
    rules:
      - name: package-comments
        disabled: false
  govet:
    enable-all: true
    # checks:
    #   - shadow=true
    # disable:
    #   - fieldalignment  # Enable this if you want to check field alignment
  lll:
    line-length: 160  # Set the maximum allowed line length
  # funlen:
  #   max-func-lines: 60  # Set maximum allowed lines for a function
  gocognit:
    min-complexity: 25  # Set threshold for cognitive complexity
  cyclop:
    max-complexity: 15  # Set the maximum allowed cyclomatic complexity
  # errcheck:
    # Ignore the blank identifier `_ = myfunc()` where necessary
    # ignore-blank: true
  # depguard:
    # Prevent the use of certain deprecated packages (example: discourage use of `log`)
    # list-type: blacklist
    # packages:
    #   - "log"
    # packages-with-error-message:
    #   "fmt": "Please use a custom logger instead of fmt for production logging."
  gofmt:
    # Use simplified code formatting (`goimports` can be used instead of `gofmt`)
    simplify: true
  
  # gocyclo: # Disbled since cyclop seems better option
  #   min-complexity: 10 # Set maximum cyclomatic complexity threshold (lower is better, but 10 is a common value)
  dupl:
    # Set the threshold of duplicate lines (default 100, can be lowered for strictness)
    threshold: 100
  staticcheck:
    checks: ["all"]
  forbidigo:
    forbid:
      - "fmt.Print"
      - "fmt.Println"
      - "fmt.Printf"

# Disable certain linters for specific files or directories (optional)
# For example, third-party generated code directories can be excluded from checks
# exclude:
#   - '.*_test\.go'      # Optionally exclude test files from some checks
#   - 'vendor/.*'        # Ignore vendor directory
#   - 'third_party/.*'   # Ignore third-party directories

# Customize severity of issues
# default-severity:
#   default: error       # Treat all findings as errors (warnings will block CI)
#   govet: warning       # Example of lowering the severity of specific linters

# Timeouts and performance-related settings
run:
  # max-issues-per-linter: 50
  # max-same-issues: 5
  timeout: 5m          # Linting must finish within 5 minutes (avoid hanging)
  tests: true          # Also run linters on _test.go files
  concurrency: 4       # Number of concurrent linters to run (set based on available CPUs)

issues:
  exclude-use-default: false
  exclude-dirs:
    - vendor           # Skip vendor directory to improve performance
  exclude:
    - 'Error return value.*is not checked'   # Example: Exclude specific errcheck patterns
    - "cyclomatic complexity"                # Example: Exclude cyclomatic complexity warnings

# Caching to improve performance (enabled by default)
# cache:
#   enabled: true
#   dir: "./.cache/golangci-lint"

  
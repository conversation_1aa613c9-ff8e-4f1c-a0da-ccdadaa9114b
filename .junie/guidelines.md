# AI Code Agent Guidelines for SA Intranet Project

> **For JetBrains AI, Augment Code, GitHub Copilot, and other AI coding assistants**

## Project Overview

This is a **Go workspace monorepo** using **Clean Architecture** and **Domain-Driven Design (DDD)** principles that hosts:
- Backend service (`services/backend`) - REST API
- Frontend service (`services/frontend`) - Go + Inertia.js with React/TypeScript
- Wiki service (`services/wiki`) - Hugo static documentation
- Domain libraries (`libs/intranet`, `libs/hexa`) - Core business logic

**Technology Stack:**
- **Backend**: Go 1.24.4, PostgreSQL, OPA policies, Clean Architecture
- **Frontend**: Go server + React/TypeScript, Vite, Tailwind CSS + DaisyUI, Inertia.js
- **Build**: Taskfile.yml automation, Docker/Docker Compose
- **Quality**: golangci-lint, SonarQube, Trivy, 80%+ test coverage requirement

## 🏗️ Architecture & Code Structure Guidelines

### Clean Architecture Pattern
**ALWAYS follow this structure when creating new features:**

```
usecase/
    feature_name/
        ├── feature.go              # Main domain logic
        ├── config/
        │   └── config.go          # Viper configuration
        ├── model/
        │   └── model.go           # Domain models/entities
        ├── repository/
        │   ├── repository.go      # Repository interfaces
        │   └── impl_repository.go # Repository implementations
        └── service/
            ├── common/            # Shared logic
            ├── in/               # Input ports (interactors)
            │   ├── *_interactor.go
            │   └── in_test.go
            ├── out/              # Output ports (presenters)
            │   └── *_presenter.go
            ├── service.go        # Service orchestrator
            └── service_test.go
```

### Project Structure
```
├── libs/
│   ├── hexa/               # Hexagonal architecture framework
│   └── intranet/           # Domain logic (Clean Architecture)
├── services/
│   ├── backend/            # REST API service
│   ├── frontend/           # GoNertia + React/TypeScript
│   └── wiki/               # Hugo documentation
├── import_service/         # Data import utilities
├── devops/                 # Docker, CI/CD
└── doc/                    # Diagrams, Grafana dashboards
```

### Key Dependencies (ALWAYS use these)
- **DI Container**: `github.com/samber/do`
- **Configuration**: `viper`
- **Database**: `bun` ORM
- **Policy Engine**: `opa`
- **Validation**: `validator`
- **Frontend Bridge**: `github.com/romsar/gonertia`

## 🚀 Development Workflow

### Essential Commands (ALWAYS use Taskfile)
```bash
# Project setup
task setup

# Development environment
task docker-compose-up                    # Start all services
task docker-compose-exec-dev-frontend     # Access frontend container

# Development servers
cd services/frontend && task run-dev      # Frontend with hot reload
cd services/frontend && task run-prod-ssr # Production SSR mode

# Build commands
task build                                # Build production frontend
task frontend:build-prod                  # Frontend only
task docker-build                         # Docker containers
```

### Environment Setup
- **Database**: PostgreSQL (postgres:5432, sa_intranet/sa_password)
- **Admin**: pgAdmin (localhost:5050, <EMAIL>/admin)
- **Frontend**: localhost:8000 (Go server), localhost:13714 (Vite dev)
- **Configuration**: Copy `.env.example` to `.env` and configure

## 🧪 Testing Requirements (MANDATORY)

### Test Commands
```bash
# ALWAYS run before submitting code
task run-test                              # All tests with coverage
task run-check                             # Linting all modules

# Individual testing
task hexa:test                             # Specific library
cd libs/intranet && task run-sonar-scanner-prepare  # With coverage
```

### Test Patterns (FOLLOW EXACTLY)

**1. Table-Driven Tests (PREFERRED)**
```go
func TestServiceMethod(t *testing.T) {
    tests := []struct {
        name     string
        input    Input
        expected Output
        wantErr  bool
    }{
        {"valid case", validInput, expectedOutput, false},
        {"error case", invalidInput, Output{}, true},
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := service.Method(tt.input)
            if (err != nil) != tt.wantErr {
                t.Errorf("Method() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(result, tt.expected) {
                t.Errorf("Method() = %v, want %v", result, tt.expected)
            }
        })
    }
}
```

**2. Integration Tests with TestContainers**
```go
func TestMain(m *testing.M) {
    ctx := context.Background()
    injector := do.New()

    bootstrap := testhelpers.Bootstrap(
        ctx,
        injector,
        testhelpers.WithPostgres(ctx),
        testhelpers.WithMigrations(ctx),
    )

    defer bootstrap.Shutdown(ctx)
    m.Run()
}
```

**3. Test Requirements**
- **Coverage**: Minimum 80% for `libs/intranet`
- **Files**: Use `*_test.go` with package `packagename_test`
- **Setup**: Use `TestMain()` for complex initialization
- **Mocking**: Mock external dependencies appropriately

## 📊 Code Quality Standards (NON-NEGOTIABLE)

### Quality Gates
```bash
# MUST pass before any code submission
task run-ci                                # Full CI pipeline
task run-check                             # All linting
task run-security-scan                     # Security scanning
task run-sonar-scan                        # SonarQube analysis
```

### Quality Tools Configuration
- **golangci-lint**: Follow `.golangci.yml` rules strictly
- **SonarQube**: 80%+ coverage, no code smells
- **Trivy**: Zero high/critical vulnerabilities
- **govulncheck**: No known vulnerabilities

### Coverage Requirements
- **libs/intranet**: Minimum 80% coverage (ENFORCED)
- **Combined report**: `devops/coverage.out`
- **HTML reports**: `{module}/coverage/coverage-pretty.html`

## 💻 Code Generation Guidelines

### Go Code Patterns (ALWAYS FOLLOW)

**1. Clean Architecture Structure**
```go
// Use context.Context for request lifecycle
func (s *Service) ProcessRequest(ctx context.Context, input InputDTO) (OutputDTO, error) {
    // Validate input
    if err := s.validator.Validate(input); err != nil {
        return OutputDTO{}, fmt.Errorf("validation failed: %w", err)
    }

    // Call interactor (business logic)
    result, err := s.interactor.Execute(ctx, input)
    if err != nil {
        return OutputDTO{}, fmt.Errorf("interactor failed: %w", err)
    }

    // Use presenter to format output
    return s.presenter.Present(result), nil
}
```

**2. Dependency Injection with samber/do**
```go
// Register dependencies
func RegisterServices(injector *do.Injector, config Config) error {
    do.Provide(injector, NewRepository)
    do.Provide(injector, NewInteractor)
    do.Provide(injector, NewPresenter)
    do.Provide(injector, NewService)
    return nil
}

// Inject dependencies
func NewService(injector *do.Injector) (*Service, error) {
    repo := do.MustInvoke[Repository](injector)
    interactor := do.MustInvoke[Interactor](injector)
    presenter := do.MustInvoke[Presenter](injector)

    return &Service{
        repository: repo,
        interactor: interactor,
        presenter:  presenter,
    }, nil
}
```

**3. Error Handling (IDIOMATIC)**
```go
// Wrap errors with context
if err != nil {
    return fmt.Errorf("failed to process user %s: %w", userID, err)
}

// Use custom error types when needed
type ValidationError struct {
    Field   string
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("validation error on field %s: %s", e.Field, e.Message)
}
```

### Frontend Code Patterns (React + GoNertia)

**1. Functional Components (ONLY)**
```tsx
// Use TypeScript with proper typing
interface PageProps {
    users: User[];
    pagination: PaginationData;
}

export default function UsersPage({ users, pagination }: PageProps) {
    // Use hooks for state management
    const [selectedUser, setSelectedUser] = useState<User | null>(null);

    // Keep business logic minimal - data comes from backend
    return (
        <div className="container mx-auto p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {users.map(user => (
                    <UserCard
                        key={user.id}
                        user={user}
                        onSelect={setSelectedUser}
                    />
                ))}
            </div>
        </div>
    );
}
```

**2. Tailwind CSS + DaisyUI (PREFERRED)**
```tsx
// Use DaisyUI components with Tailwind utilities
<div className="card bg-base-100 shadow-xl">
    <div className="card-body">
        <h2 className="card-title">{user.name}</h2>
        <p className="text-base-content/70">{user.email}</p>
        <div className="card-actions justify-end">
            <button className="btn btn-primary">Edit</button>
            <button className="btn btn-ghost">Delete</button>
        </div>
    </div>
</div>
```

**3. Inertia.js Integration**
```tsx
// Frontend receives data from Go backend via Inertia
// NO business logic in frontend - all in libs/intranet
import { router } from '@inertiajs/react';

const handleSubmit = (data: FormData) => {
    router.post('/users', data, {
        onSuccess: () => {
            // Handle success
        },
        onError: (errors) => {
            // Handle validation errors from backend
        }
    });
};
```

## 🔧 Dependency Management

### Go Dependencies (MODULE-SPECIFIC)
```bash
# Add to specific modules (NEVER to root)
cd libs/intranet && go get github.com/example/package
cd services/frontend && go get github.com/example/package

# Update and clean
go get -u ./...
go mod tidy
```

### Frontend Dependencies
```bash
cd services/frontend
npm install package-name                   # Production dependency
npm install --save-dev dev-package-name    # Development dependency
```

## 🚀 CI/CD & Deployment

### Pipeline Commands
```bash
task run-ci                                # Full CI: check, test, security, sonar
task run-security-scan                     # Trivy + govulncheck
task docker-login                          # AWS ECR authentication
task docker-build                          # Production container
task docker-publish                        # Build and push to registry
```

## 📝 Git & Commit Guidelines

### Commit Conventions (MANDATORY)
```bash
# Use Conventional Commits (max 100 characters)
feat(auth): add user provisioning interactor
fix(db): handle nil config in migration
docs(api): update authentication endpoints
test(user): add integration tests for user service
refactor(core): simplify error handling logic
```

### Git Workflow
- **Trunk-based development** with semver tags
- **Changelog**: Auto-generated using `git-chglog`
- **Versioning**: `git tag -a v1.5.0 -m "Release version 1.5.0"`

## 🎯 AI Agent Instructions

### When Generating Code

**🟢 DO:**
- Follow Clean Architecture patterns exactly
- Use dependency injection with `samber/do`
- Write comprehensive tests (80%+ coverage)
- Use table-driven tests for multiple scenarios
- Follow golangci-lint rules strictly
- Keep frontend thin - business logic in backend
- Use TypeScript with proper typing
- Apply Tailwind CSS + DaisyUI patterns
- Wrap errors with context
- Use `context.Context` for request lifecycle

**🔴 DON'T:**
- Put business logic in frontend components
- Skip test coverage requirements
- Ignore linting rules
- Use direct tool commands (use Taskfile)
- Create monolithic functions
- Skip error wrapping
- Use `interface{}` instead of `any`
- Violate Clean Architecture boundaries

### Reference Examples
- **Clean Architecture**: `libs/intranet/usecase/auth/`
- **GoNertia + React**: `services/frontend/`
- **CI/DevOps**: `devops/` and `Taskfile.yml`
- **Testing**: `libs/intranet/testing/`

### Quality Checklist
- [ ] Tests written and passing (`task run-test`)
- [ ] Linting clean (`task run-check`)
- [ ] Security scan clean (`task run-security-scan`)
- [ ] 80%+ coverage for business logic
- [ ] Clean Architecture followed
- [ ] Conventional commits used
- [ ] Documentation updated if needed

**Remember**: This project prioritizes **security**, **maintainability**, and **clean architecture** over quick solutions.

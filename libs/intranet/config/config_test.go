package config_test

import (
	"os"
	"testing"

	"sa-intranet/config"
)

func TestNewConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func()
		cleanup func()
		wantErr bool
		verify  func(*testing.T, *config.Config)
	}{
		{
			name: "successful config creation with default values",
			setup: func() {
				// Clear any existing environment variables
				os.Unsetenv("APP_SONARQUBE_URL")
				os.Unsetenv("APP_SONARQUBE_TOKEN")
				os.Unsetenv("APP_CYPHER_KEY")
				os.Unsetenv("APP_DATABASE_URL")
			},
			cleanup: func() {},
			wantErr: false,
			verify: func(t *testing.T, cfg *config.Config) {
				if cfg == nil {
					t.Error("Config is nil")
					return
				}
				// Verify default values are set
				if cfg.Cypher.Key == "" {
					t.Error("Cypher.Key should have a default value")
				}
				if cfg.Database.DatabaseURL == "" {
					t.Error("Database.DatabaseURL should have a default value")
				}
			},
		},
		{
			name: "config creation with environment variables",
			setup: func() {
				os.Setenv("APP_SONARQUBE_URL", "http://test-sonar:9000")
				os.Setenv("APP_SONARQUBE_TOKEN", "test-token")
				os.Setenv("APP_CYPHER_KEY", "dGVzdC1rZXktMTIzNDU2Nzg5MA==") // base64 encoded test key
				os.Setenv("APP_DATABASE_URL", "postgres://user:pass@localhost/testdb")
			},
			cleanup: func() {
				os.Unsetenv("APP_SONARQUBE_URL")
				os.Unsetenv("APP_SONARQUBE_TOKEN")
				os.Unsetenv("APP_CYPHER_KEY")
				os.Unsetenv("APP_DATABASE_URL")
			},
			wantErr: false,
			verify: func(t *testing.T, cfg *config.Config) {
				if cfg == nil {
					t.Error("Config is nil")
					return
				}
				if cfg.SonarQube.URL != "http://test-sonar:9000" {
					t.Errorf("SonarQube.URL = %v, want %v", cfg.SonarQube.URL, "http://test-sonar:9000")
				}
				if cfg.SonarQube.Token != "test-token" {
					t.Errorf("SonarQube.Token = %v, want %v", cfg.SonarQube.Token, "test-token")
				}
				if cfg.Cypher.Key != "dGVzdC1rZXktMTIzNDU2Nzg5MA==" {
					t.Errorf("Cypher.Key = %v, want %v", cfg.Cypher.Key, "dGVzdC1rZXktMTIzNDU2Nzg5MA==")
				}
				if cfg.Database.DatabaseURL != "postgres://user:pass@localhost/testdb" {
					t.Errorf("Database.DatabaseURL = %v, want %v", cfg.Database.DatabaseURL, "postgres://user:pass@localhost/testdb")
				}
			},
		},
		{
			name: "config creation with partial environment variables",
			setup: func() {
				os.Unsetenv("APP_SONARQUBE_URL")
				os.Unsetenv("APP_SONARQUBE_TOKEN")
				os.Setenv("APP_CYPHER_KEY", "partial-key")
				os.Unsetenv("APP_DATABASE_URL")
			},
			cleanup: func() {
				os.Unsetenv("APP_CYPHER_KEY")
			},
			wantErr: false,
			verify: func(t *testing.T, cfg *config.Config) {
				if cfg == nil {
					t.Error("Config is nil")
					return
				}
				if cfg.Cypher.Key != "partial-key" {
					t.Errorf("Cypher.Key = %v, want %v", cfg.Cypher.Key, "partial-key")
				}
				// Other values should have defaults
				if cfg.SonarQube.URL == "" {
					t.Error("SonarQube.URL should have a default value")
				}
				if cfg.Database.DatabaseURL == "" {
					t.Error("Database.DatabaseURL should have a default value")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.setup()
			defer tt.cleanup()

			// Test NewConfig
			cfg, err := config.NewConfig()

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewConfig() expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("NewConfig() unexpected error = %v", err)
				return
			}

			// Verify the config
			if tt.verify != nil {
				tt.verify(t, cfg)
			}
		})
	}
}

func TestConfigStructureValidation(t *testing.T) {
	// Test that the config structure is properly defined
	cfg, err := config.NewConfig()
	if err != nil {
		t.Fatalf("NewConfig() failed: %v", err)
	}

	// Test that all expected fields exist and are accessible
	tests := []struct {
		name     string
		testFunc func(*testing.T, *config.Config)
	}{
		{
			name: "SonarQube config structure",
			testFunc: func(t *testing.T, cfg *config.Config) {
				// Test that SonarQube field exists and has expected subfields
				_ = cfg.SonarQube.URL
				_ = cfg.SonarQube.Token
			},
		},
		{
			name: "Database config structure",
			testFunc: func(t *testing.T, cfg *config.Config) {
				// Test that Database field exists and has expected subfields
				_ = cfg.Database.DatabaseURL
				_ = cfg.Database.Debug
			},
		},
		{
			name: "Cypher config structure",
			testFunc: func(t *testing.T, cfg *config.Config) {
				// Test that Cypher field exists and has expected subfields
				_ = cfg.Cypher.Key
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// This test will panic if the structure is not as expected
			tt.testFunc(t, cfg)
		})
	}
}

func TestConfigDefaultValues(t *testing.T) {
	// Clear all environment variables to test defaults
	envVars := []string{
		"APP_SONARQUBE_URL",
		"APP_SONARQUBE_TOKEN",
		"APP_CYPHER_KEY",
		"APP_DATABASE_URL",
	}

	// Store original values
	originalValues := make(map[string]string)
	for _, envVar := range envVars {
		originalValues[envVar] = os.Getenv(envVar)
		os.Unsetenv(envVar)
	}

	// Restore original values after test
	defer func() {
		for _, envVar := range envVars {
			if originalValue, exists := originalValues[envVar]; exists && originalValue != "" {
				os.Setenv(envVar, originalValue)
			}
		}
	}()

	cfg, err := config.NewConfig()
	if err != nil {
		t.Fatalf("NewConfig() failed: %v", err)
	}

	// Test that reasonable defaults are set
	if cfg.SonarQube.URL == "" {
		t.Error("SonarQube.URL should have a default value when not set in environment")
	}

	if cfg.Cypher.Key == "" {
		t.Error("Cypher.Key should have a default value when not set in environment")
	}

	// Log the default values for verification
	t.Logf("Default SonarQube.URL: %s", cfg.SonarQube.URL)
	t.Logf("Default SonarQube.Token: %s", cfg.SonarQube.Token)
	t.Logf("Default Cypher.Key: %s", cfg.Cypher.Key)
	t.Logf("Default Database.DatabaseURL: %s", cfg.Database.DatabaseURL)
}

func TestConfigEnvironmentOverrides(t *testing.T) {
	// Test that environment variables properly override defaults
	testCases := []struct {
		envVar   string
		envValue string
		getField func(*config.Config) string
	}{
		{
			envVar:   "APP_SONARQUBE_URL",
			envValue: "http://production-sonar:9000",
			getField: func(cfg *config.Config) string { return cfg.SonarQube.URL },
		},
		{
			envVar:   "APP_SONARQUBE_TOKEN",
			envValue: "production-token",
			getField: func(cfg *config.Config) string { return cfg.SonarQube.Token },
		},
		{
			envVar:   "APP_CYPHER_KEY",
			envValue: "custom-cypher-key",
			getField: func(cfg *config.Config) string { return cfg.Cypher.Key },
		},
		{
			envVar:   "APP_DATABASE_URL",
			envValue: "postgres://custom:pass@localhost/customdb",
			getField: func(cfg *config.Config) string { return cfg.Database.DatabaseURL },
		},
	}

	for _, tc := range testCases {
		t.Run(tc.envVar, func(t *testing.T) {
			// Set the environment variable
			originalValue := os.Getenv(tc.envVar)
			os.Setenv(tc.envVar, tc.envValue)
			defer func() {
				if originalValue != "" {
					os.Setenv(tc.envVar, originalValue)
				} else {
					os.Unsetenv(tc.envVar)
				}
			}()

			// Create config
			cfg, err := config.NewConfig()
			if err != nil {
				t.Fatalf("NewConfig() failed: %v", err)
			}

			// Verify the field was set correctly
			actualValue := tc.getField(cfg)
			if actualValue != tc.envValue {
				t.Errorf("Field value = %v, want %v", actualValue, tc.envValue)
			}
		})
	}
}

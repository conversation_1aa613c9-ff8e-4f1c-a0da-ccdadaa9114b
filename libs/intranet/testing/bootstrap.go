package testing

import (
	"context"

	intranet "sa-intranet"
	intranetConfig "sa-intranet/config"

	"github.com/emilioforrer/hexa/bootstrap"
	"github.com/samber/do"
)

func Bootstrap(ctx context.Context, injector *do.Injector, services ...bootstrap.ServiceProvider) bootstrap.BootStrapper {
	conf, err := intranetConfig.NewConfig()
	if err != nil {
		panic(err)
	}

	if err = intranet.Register(injector, *conf); err != nil {
		panic(err)
	}

	bootstrapper := bootstrap.NewDefaultBootStrapper(injector, services...)

	if err := bootstrapper.Register(ctx); err != nil {
		panic(err)
	}

	if err := bootstrapper.Boot(ctx); err != nil {
		panic(err)
	}

	return bootstrapper
}

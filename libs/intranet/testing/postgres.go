// Package testhelpers contains test utilities
package testing

import (
	"context"
	"embed"
	"fmt"
	"io/fs"
	"log/slog"
	"time"

	intranetConfig "sa-intranet/config"

	"sa-intranet/db/migrate/migrations"
	cqmModel "sa-intranet/usecase/cqm/model"

	"github.com/emilioforrer/hexa/bootstrap"
	"github.com/samber/do"
	testcontainers "github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	wait "github.com/testcontainers/testcontainers-go/wait"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dbfixture"
	"github.com/uptrace/bun/migrate"
)

//go:embed testdata
var FixtureFS embed.FS

type PostgresServiceProvider struct {
	bootstrap.DefaultServiceProvider
	postgresContainer testcontainers.Container
}

func (p *PostgresServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	i := container.Injector()

	postgresContainer, connectionString, err := setupPostgres(ctx)
	if err != nil {
		return fmt.Errorf("failed to start Postgres container: %w", err)
	}

	do.Override[intranetConfig.DatabaseConfig](i, func(i *do.Injector) (intranetConfig.DatabaseConfig, error) {
		return intranetConfig.DatabaseConfig{
			DatabaseURL: connectionString,
		}, nil
	})

	p.postgresContainer = postgresContainer

	return nil
}

func (p *PostgresServiceProvider) Teardown(ctx context.Context, container bootstrap.Container) error {
	if p.postgresContainer != nil {
		return p.postgresContainer.Terminate(ctx)
	}

	return nil
}

func WithPostgres(ctx context.Context) bootstrap.ServiceProvider {
	service := &PostgresServiceProvider{}

	return service
}

type MigrationServiceProvider struct {
	bootstrap.DefaultServiceProvider
}

func (m *MigrationServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	i := container.Injector()
	db := do.MustInvoke[*bun.DB](i)
	// Let the db know about the models.

	migrator := migrate.NewMigrator(db, migrations.Migrations)

	if err := migrator.Init(ctx); err != nil {
		return err
	}

	group, err := migrator.Migrate(ctx)
	if err != nil {
		return err
	}

	if group.IsZero() {
		slog.Debug("there are no new migrations to run (database is up to date)\n")
	}

	slog.Debug("migrated to %s\n", "group", group)

	return nil
}

func WithMigrations(ctx context.Context) bootstrap.ServiceProvider {
	service := &MigrationServiceProvider{}

	return service
}

type FixturesServiceProvider struct {
	bootstrap.DefaultServiceProvider
}

func (f *FixturesServiceProvider) Register(ctx context.Context, container bootstrap.Container) error {
	return nil
}

func (f *FixturesServiceProvider) Boot(ctx context.Context, container bootstrap.Container) error {
	i := container.Injector()
	db := do.MustInvoke[*bun.DB](i)
	db.RegisterModel(
		(*cqmModel.JiraProject)(nil),
		(*cqmModel.SonarqubeProject)(nil),
		(*cqmModel.SonarqubeIssue)(nil),
	)

	fixture := dbfixture.New(db, dbfixture.WithRecreateTables()) // this needs postgres to be running

	do.Provide(i, func(i *do.Injector) (*dbfixture.Fixture, error) {
		return fixture, nil
	})

	// _, currentFile, _, _ := runtime.Caller(0)
	// testdataDir := filepath.Join(filepath.Dir(currentFile), "testdata")
	// dir := os.DirFS(testdataDir)

	dir, err := fs.Sub(FixtureFS, "testdata")
	if err != nil {
		return fmt.Errorf("failed to get testdata subdirectory: %w", err)
	}

	err = fixture.Load(ctx, dir, "fixture.yml")
	if err != nil {
		return err
	}

	return nil
}

func WithFixtures(ctx context.Context) bootstrap.ServiceProvider {
	service := &FixturesServiceProvider{}

	return service
}

func setupPostgres(ctx context.Context) (testcontainers.Container, string, error) {
	c, err := postgres.Run(ctx,
		"postgres:16.3-alpine",
		postgres.WithDatabase("users-db"),
		postgres.WithUsername("postgres"),
		postgres.WithPassword("postgres"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).WithStartupTimeout(5*time.Second)),
	)
	if err != nil {
		return nil, "", err
	}

	connStr, err := c.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		return nil, "", err
	}

	return c, connStr, nil
}

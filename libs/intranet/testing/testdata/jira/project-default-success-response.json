{"expand": "description,lead,issueTypes,url,projectKeys,permissions,insight", "self": "https://my-company.atlassian.net/rest/api/3/project/14533", "id": "14533", "key": "BPL", "description": "", "lead": {"self": "https://my-company.atlassian.net/rest/api/3/user?accountId=5e84c9839b6a000c1213523490", "accountId": "5e84c9839b6a000c1213523490", "avatarUrls": {"48x48": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/5e84c9839b6a000c1213523490/c4ff09b4-81f8-4119-965b-5bdd7be9e3c9/48", "24x24": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/5e84c9839b6a000c1213523490/c4ff09b4-81f8-4119-965b-5bdd7be9e3c9/24", "16x16": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/5e84c9839b6a000c1213523490/c4ff09b4-81f8-4119-965b-5bdd7be9e3c9/16", "32x32": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/5e84c9839b6a000c1213523490/c4ff09b4-81f8-4119-965b-5bdd7be9e3c9/32"}, "displayName": "<PERSON>", "active": true}, "components": [{"self": "https://my-company.atlassian.net/rest/api/3/component/12466", "id": "12466", "name": "Internal Initiatives", "description": "Internal Initiatives", "isAssigneeTypeValid": false}, {"self": "https://my-company.atlassian.net/rest/api/3/component/12465", "id": "12465", "name": "Objectives and Key Results", "description": "OKR", "isAssigneeTypeValid": false}], "issueTypes": [{"self": "https://my-company.atlassian.net/rest/api/3/issuetype/3", "id": "3", "description": "A task that needs to be done.", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10318?size=medium", "name": "Task", "subtask": false, "avatarId": 10318, "hierarchyLevel": 0}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/5", "id": "5", "description": "The sub-task of the issue", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10316?size=medium", "name": "Sub-task", "subtask": true, "avatarId": 10316, "hierarchyLevel": -1}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/10001", "id": "10001", "description": "A user story. Created by JIRA Software - do not edit or delete.", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10315?size=medium", "name": "Story", "subtask": false, "avatarId": 10315, "hierarchyLevel": 0}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/1", "id": "1", "description": "A problem which impairs or prevents the functions of the product.", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10303?size=medium", "name": "Bug", "subtask": false, "avatarId": 10303, "hierarchyLevel": 0}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/10000", "id": "10000", "description": "A big user story that needs to be broken down. Created by JIRA Software - do not edit or delete.", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10307?size=medium", "name": "Epic", "subtask": false, "avatarId": 10307, "hierarchyLevel": 1}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/11602", "id": "11602", "description": "", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10314?size=medium", "name": "Technical Debt", "subtask": false, "avatarId": 10314, "hierarchyLevel": 0}, {"self": "https://my-company.atlassian.net/rest/api/3/issuetype/11564", "id": "11564", "description": "", "iconUrl": "https://my-company.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/11101?size=medium", "name": "<PERSON><PERSON>", "subtask": false, "avatarId": 11101, "hierarchyLevel": 2}], "assigneeType": "UNASSIGNED", "versions": [], "name": "Backend Leads", "roles": {}, "avatarUrls": {"48x48": "https://my-company.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/13563", "24x24": "https://my-company.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/13563?size=small", "16x16": "https://my-company.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/13563?size=xsmall", "32x32": "https://my-company.atlassian.net/rest/api/3/universal_avatar/view/type/project/avatar/13563?size=medium"}, "projectTypeKey": "software", "simplified": false, "style": "classic", "isPrivate": false, "properties": {}}
{"serverUrl": "https://sonarqube.example.com", "taskId": "AZZB7ssdmwfuibSArSUF", "status": "SUCCESS", "analysedAt": "2025-04-15T17:35:23+0000", "revision": "66a81d00143c3210459bc804ad1354a267b093c4", "changedAt": "2025-04-15T17:35:23+0000", "project": {"key": "example.project-backend", "name": "Example Project Backend", "url": "https://sonarqube.example.com/dashboard?id=example.project-backend"}, "branch": {"name": "develop", "type": "BRANCH", "isMain": true, "url": "https://sonarqube.example.com/dashboard?id=example.project-backend"}, "qualityGate": {"name": "Gold", "status": "ERROR", "conditions": [{"metric": "new_reliability_rating", "operator": "GREATER_THAN", "value": "2", "status": "ERROR", "errorThreshold": "1"}, {"metric": "reliability_rating", "operator": "GREATER_THAN", "value": "2", "status": "OK", "errorThreshold": "3"}, {"metric": "new_security_rating", "operator": "GREATER_THAN", "value": "5", "status": "ERROR", "errorThreshold": "1"}, {"metric": "security_rating", "operator": "GREATER_THAN", "value": "5", "status": "ERROR", "errorThreshold": "3"}, {"metric": "new_maintainability_rating", "operator": "GREATER_THAN", "value": "1", "status": "OK", "errorThreshold": "1"}, {"metric": "sqale_rating", "operator": "GREATER_THAN", "value": "1", "status": "OK", "errorThreshold": "2"}, {"metric": "new_coverage", "operator": "LESS_THAN", "value": "89.6", "status": "OK", "errorThreshold": "80"}, {"metric": "coverage", "operator": "LESS_THAN", "value": "78.6", "status": "OK", "errorThreshold": "60"}, {"metric": "new_duplicated_lines_density", "operator": "GREATER_THAN", "value": "0.13425", "status": "OK", "errorThreshold": "3"}, {"metric": "blocker_violations", "operator": "GREATER_THAN", "value": "2", "status": "ERROR", "errorThreshold": "0"}, {"metric": "comment_lines_density", "operator": "LESS_THAN", "value": "14.0", "status": "ERROR", "errorThreshold": "15"}, {"metric": "critical_violations", "operator": "GREATER_THAN", "value": "25", "status": "ERROR", "errorThreshold": "0"}, {"metric": "duplicated_blocks", "operator": "GREATER_THAN", "value": "2", "status": "OK", "errorThreshold": "10"}, {"metric": "major_violations", "operator": "GREATER_THAN", "value": "99", "status": "OK", "errorThreshold": "100"}, {"metric": "new_critical_violations", "operator": "GREATER_THAN", "value": "25", "status": "ERROR", "errorThreshold": "0"}, {"metric": "new_major_violations", "operator": "GREATER_THAN", "value": "31", "status": "ERROR", "errorThreshold": "25"}, {"metric": "new_security_hotspots_reviewed", "operator": "LESS_THAN", "status": "NO_VALUE", "errorThreshold": "100"}, {"metric": "reopened_issues", "operator": "GREATER_THAN", "value": "0", "status": "OK", "errorThreshold": "100"}]}, "properties": {"sonar.analysis.detectedscm": "git", "sonar.analysis.detectedci": "<PERSON>"}}
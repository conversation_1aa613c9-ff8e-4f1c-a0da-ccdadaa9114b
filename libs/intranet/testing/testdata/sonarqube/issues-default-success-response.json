{"total": 27, "p": 1, "ps": 100, "paging": {"pageIndex": 1, "pageSize": 100, "total": 27}, "effortTotal": 249, "issues": [{"key": "AZaKK-g47J7LX6RjDcqI", "rule": "yaml:Line<PERSON>engthCheck", "severity": "INFO", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 11, "hash": "b6ed7a6ef39b8e5da0a8e08e682c4b75", "textRange": {"startLine": 11, "endLine": 11, "startOffset": 0, "endOffset": 145}, "flows": [], "status": "OPEN", "message": "line too long (145 > 80 characters) (line-length)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T04:47:24+0000", "updateDate": "2025-05-01T04:47:24+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaKIaFV7J7LX6RjDbfm", "rule": "yaml:TrailingSpacesCheck", "severity": "MINOR", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 9, "textRange": {"startLine": 9, "endLine": 9, "startOffset": 0, "endOffset": 6}, "flows": [], "status": "OPEN", "message": "trailing spaces (trailing-spaces)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T04:36:12+0000", "updateDate": "2025-05-01T04:36:12+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaKIaFV7J7LX6RjDbfl", "rule": "yaml:Line<PERSON>engthCheck", "severity": "INFO", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 17, "hash": "ea1be11be0f3083bf1a6381481f49ac2", "textRange": {"startLine": 17, "endLine": 17, "startOffset": 0, "endOffset": 87}, "flows": [], "status": "OPEN", "message": "line too long (87 > 80 characters) (line-length)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T04:36:12+0000", "updateDate": "2025-05-01T04:36:12+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaJ7rj87J7LX6RjCnlN", "rule": "yaml:Line<PERSON>engthCheck", "severity": "INFO", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 15, "hash": "a05930982a06322f6a13ff9c38042db4", "textRange": {"startLine": 15, "endLine": 15, "startOffset": 0, "endOffset": 107}, "flows": [], "status": "OPEN", "message": "line too long (107 > 80 characters) (line-length)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T03:40:34+0000", "updateDate": "2025-05-01T03:40:34+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaJ7rj87J7LX6RjCnlO", "rule": "yaml:Line<PERSON>engthCheck", "severity": "INFO", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 16, "hash": "cc8ef7227a125ccbdfb8e89f727cd2b2", "textRange": {"startLine": 16, "endLine": 16, "startOffset": 0, "endOffset": 82}, "flows": [], "status": "OPEN", "message": "line too long (82 > 80 characters) (line-length)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T03:40:34+0000", "updateDate": "2025-05-01T03:40:34+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaJ7rj87J7LX6RjCnlP", "rule": "yaml:Line<PERSON>engthCheck", "severity": "INFO", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 18, "hash": "0fdd2006b0b1059733e797201dbe47b0", "textRange": {"startLine": 18, "endLine": 18, "startOffset": 0, "endOffset": 163}, "flows": [], "status": "OPEN", "message": "line too long (163 > 80 characters) (line-length)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-05-01T03:40:34+0000", "updateDate": "2025-05-01T03:40:34+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHt_9X7J7LX6Rj5HGS", "rule": "yaml:DocumentStartCheck", "severity": "MAJOR", "component": "sa-intranet-libs-intranet:Taskfile.yml", "project": "sa-intranet-libs-intranet", "line": 3, "hash": "1f0129db49ffca79622c978692d306cd", "textRange": {"startLine": 3, "endLine": 3, "startOffset": 0, "endOffset": 12}, "flows": [], "status": "OPEN", "message": "missing document start \"---\" (document-start)", "effort": "2min", "debt": "2min", "author": "", "tags": ["convention"], "creationDate": "2025-04-30T17:21:35+0000", "updateDate": "2025-04-30T17:21:35+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Dh7J7LX6Rj5GLE", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:core/repository/save.go", "project": "sa-intranet-libs-intranet", "line": 41, "hash": "57b61d1065627a99e9553f88e8a2f0b9", "textRange": {"startLine": 41, "endLine": 41, "startOffset": 5, "endOffset": 9}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 49, "endLine": 49, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 54, "endLine": 54, "startOffset": 1, "endOffset": 4}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 58, "endLine": 58, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 82, "endLine": 82, "startOffset": 3, "endOffset": 7}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 58, "endLine": 58, "startOffset": 18, "endOffset": 20}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 61, "endLine": 61, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 63, "endLine": 63, "startOffset": 3, "endOffset": 9}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 78, "endLine": 78, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 89, "endLine": 89, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 94, "endLine": 94, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:core/repository/save.go", "textRange": {"startLine": 98, "endLine": 98, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 18 to the 15 allowed.", "effort": "8min", "debt": "8min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-25T07:37:16+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7G47J7LX6Rj5GLX", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:policies/rbac.go", "project": "sa-intranet-libs-intranet", "line": 53, "hash": "99577654b37f62dccc58c3c007292b3d", "textRange": {"startLine": 53, "endLine": 53, "startOffset": 21, "endOffset": 29}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 59, "endLine": 59, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 65, "endLine": 65, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 80, "endLine": 80, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 86, "endLine": 86, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 94, "endLine": 94, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 94, "endLine": 94, "startOffset": 31, "endOffset": 33}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 99, "endLine": 99, "startOffset": 1, "endOffset": 4}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 101, "endLine": 101, "startOffset": 2, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 102, "endLine": 102, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 108, "endLine": 108, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 114, "endLine": 114, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 115, "endLine": 115, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 120, "endLine": 120, "startOffset": 2, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 122, "endLine": 122, "startOffset": 3, "endOffset": 6}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 123, "endLine": 123, "startOffset": 4, "endOffset": 6}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 128, "endLine": 128, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 138, "endLine": 138, "startOffset": 3, "endOffset": 6}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 140, "endLine": 140, "startOffset": 4, "endOffset": 7}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 141, "endLine": 141, "startOffset": 5, "endOffset": 7}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:policies/rbac.go", "textRange": {"startLine": 146, "endLine": 146, "startOffset": 4, "endOffset": 6}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 49 to the 15 allowed.", "effort": "39min", "debt": "39min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-23T19:46:48+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Gf7J7LX6Rj5GLS", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "project": "sa-intranet-libs-intranet", "line": 13, "hash": "86b03a9a4145df748b6d428c29022b17", "textRange": {"startLine": 13, "endLine": 13, "startOffset": 5, "endOffset": 9}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 21, "endLine": 21, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 29, "endLine": 29, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 35, "endLine": 35, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 41, "endLine": 41, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 47, "endLine": 47, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 53, "endLine": 53, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 59, "endLine": 59, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "textRange": {"startLine": 73, "endLine": 73, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 24 to the 15 allowed.", "effort": "14min", "debt": "14min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-17T02:51:15+0000", "updateDate": "2025-05-01T02:54:58+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7GM7J7LX6Rj5GLQ", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "project": "sa-intranet-libs-intranet", "line": 13, "hash": "86b03a9a4145df748b6d428c29022b17", "textRange": {"startLine": 13, "endLine": 13, "startOffset": 5, "endOffset": 9}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 28, "endLine": 28, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 33, "endLine": 33, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 39, "endLine": 39, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 45, "endLine": 45, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "textRange": {"startLine": 60, "endLine": 60, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 18 to the 15 allowed.", "effort": "8min", "debt": "8min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-17T02:51:15+0000", "updateDate": "2025-05-01T02:54:58+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7GD7J7LX6Rj5GLP", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "project": "sa-intranet-libs-intranet", "line": 12, "hash": "86b03a9a4145df748b6d428c29022b17", "textRange": {"startLine": 12, "endLine": 12, "startOffset": 5, "endOffset": 9}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 21, "endLine": 21, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 33, "endLine": 33, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 42, "endLine": 42, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 50, "endLine": 50, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 58, "endLine": 58, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 66, "endLine": 66, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 75, "endLine": 75, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 83, "endLine": 83, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 91, "endLine": 91, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 99, "endLine": 99, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "textRange": {"startLine": 107, "endLine": 107, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 33 to the 15 allowed.", "effort": "23min", "debt": "23min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-17T02:51:15+0000", "updateDate": "2025-05-01T02:54:58+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7GX7J7LX6Rj5GLR", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "project": "sa-intranet-libs-intranet", "line": 12, "hash": "86b03a9a4145df748b6d428c29022b17", "textRange": {"startLine": 12, "endLine": 12, "startOffset": 5, "endOffset": 9}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 21, "endLine": 21, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 31, "endLine": 31, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 40, "endLine": 40, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 49, "endLine": 49, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 58, "endLine": 58, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 67, "endLine": 67, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 77, "endLine": 77, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 97, "endLine": 97, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 107, "endLine": 107, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 116, "endLine": 116, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "textRange": {"startLine": 129, "endLine": 129, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 36 to the 15 allowed.", "effort": "26min", "debt": "26min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-17T02:51:15+0000", "updateDate": "2025-05-01T02:54:58+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FC7J7LX6Rj5GLI", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "project": "sa-intranet-libs-intranet", "line": 212, "hash": "24ed0a8a8181d91ac59d1030f68e0999", "textRange": {"startLine": 212, "endLine": 212, "startOffset": 32, "endOffset": 47}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 217, "endLine": 217, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 222, "endLine": 222, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 230, "endLine": 230, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 234, "endLine": 234, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 247, "endLine": 247, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 278, "endLine": 278, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 282, "endLine": 282, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 288, "endLine": 288, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 292, "endLine": 292, "startOffset": 1, "endOffset": 4}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 293, "endLine": 293, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 306, "endLine": 306, "startOffset": 1, "endOffset": 4}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 307, "endLine": 307, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 320, "endLine": 320, "startOffset": 3, "endOffset": 6}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 326, "endLine": 326, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 337, "endLine": 337, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 361, "endLine": 361, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 373, "endLine": 373, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 23 to the 15 allowed.", "effort": "13min", "debt": "13min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-04-16T04:45:48+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Ea7J7LX6Rj5GLF", "rule": "go:S1135", "severity": "INFO", "component": "sa-intranet-libs-intranet:usecase/cqm/repository/sonarqube_analysis_repository.go", "project": "sa-intranet-libs-intranet", "line": 64, "hash": "f5def281b3d382a6b664de8017635eb7", "textRange": {"startLine": 64, "endLine": 64, "startOffset": 5, "endOffset": 9}, "flows": [], "status": "OPEN", "message": "Complete the task associated to this TODO comment.", "effort": "0min", "debt": "0min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-04-16T00:58:04+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Fg7J7LX6Rj5GLO", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/get_measures.go", "project": "sa-intranet-libs-intranet", "line": 38, "hash": "dbcd48051a1012aa53466ae3c9954b2c", "textRange": {"startLine": 38, "endLine": 38, "startOffset": 30, "endOffset": 38}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/get_measures.go", "textRange": {"startLine": 59, "endLine": 59, "startOffset": 20, "endOffset": 28}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/get_measures.go", "textRange": {"startLine": 63, "endLine": 63, "startOffset": 20, "endOffset": 28}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"%s: %w\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-04-14T21:08:44+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FX7J7LX6Rj5GLK", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "project": "sa-intranet-libs-intranet", "line": 282, "hash": "cc4640a96360ba78fb613d13f8ea3ff3", "textRange": {"startLine": 282, "endLine": 282, "startOffset": 38, "endOffset": 77}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 363, "endLine": 363, "startOffset": 34, "endOffset": 73}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 416, "endLine": 416, "startOffset": 30, "endOffset": 69}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"error making request to SonarQube: %w\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-04-14T21:08:44+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FX7J7LX6Rj5GLM", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "project": "sa-intranet-libs-intranet", "line": 288, "hash": "1e2e91cb919c769c192e4c92a1362dbf", "textRange": {"startLine": 288, "endLine": 288, "startOffset": 38, "endOffset": 75}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 369, "endLine": 369, "startOffset": 34, "endOffset": 71}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 422, "endLine": 422, "startOffset": 30, "endOffset": 67}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"SonarQube API error (status %d): %s\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-04-14T21:08:44+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FX7J7LX6Rj5GLJ", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "project": "sa-intranet-libs-intranet", "line": 293, "hash": "44c3163ceaf44f144a8364407a91f087", "textRange": {"startLine": 293, "endLine": 293, "startOffset": 38, "endOffset": 71}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 374, "endLine": 374, "startOffset": 34, "endOffset": 67}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 427, "endLine": 427, "startOffset": 30, "endOffset": 63}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"error reading response body: %w\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-04-14T21:08:44+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FX7J7LX6Rj5GLL", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "project": "sa-intranet-libs-intranet", "line": 298, "hash": "2f4c7866b9fa428b3910283bf7567926", "textRange": {"startLine": 298, "endLine": 298, "startOffset": 38, "endOffset": 71}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 379, "endLine": 379, "startOffset": 34, "endOffset": 67}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 431, "endLine": 431, "startOffset": 30, "endOffset": 63}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"error unmarshaling response: %w\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-04-14T21:08:44+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FX7J7LX6Rj5GLN", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "project": "sa-intranet-libs-intranet", "line": 179, "hash": "8be12e5b405f0f4e1f46222b00a9fe4d", "textRange": {"startLine": 179, "endLine": 179, "startOffset": 17, "endOffset": 33}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 181, "endLine": 181, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 196, "endLine": 196, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 197, "endLine": 197, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 198, "endLine": 198, "startOffset": 3, "endOffset": 5}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 200, "endLine": 200, "startOffset": 5, "endOffset": 9}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 205, "endLine": 205, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 216, "endLine": 216, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 217, "endLine": 217, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 221, "endLine": 221, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 225, "endLine": 225, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 229, "endLine": 229, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 233, "endLine": 233, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 237, "endLine": 237, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 241, "endLine": 241, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 245, "endLine": 245, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 249, "endLine": 249, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 253, "endLine": 253, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 257, "endLine": 257, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 261, "endLine": 261, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 265, "endLine": 265, "startOffset": 2, "endOffset": 4}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 273, "endLine": 273, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 281, "endLine": 281, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 286, "endLine": 286, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 292, "endLine": 292, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "textRange": {"startLine": 297, "endLine": 297, "startOffset": 1, "endOffset": 3}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 42 to the 15 allowed.", "effort": "32min", "debt": "32min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-03-29T08:13:53+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7FC7J7LX6Rj5GLH", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "project": "sa-intranet-libs-intranet", "line": 318, "hash": "88d0b7993be3cbbac79505c293bbf483", "textRange": {"startLine": 318, "endLine": 318, "startOffset": 49, "endOffset": 68}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 321, "endLine": 321, "startOffset": 50, "endOffset": 69}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "textRange": {"startLine": 324, "endLine": 324, "startOffset": 33, "endOffset": 52}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"technical-debt-%s\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-03-29T03:56:49+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7El7J7LX6Rj5GLG", "rule": "go:S1135", "severity": "INFO", "component": "sa-intranet-libs-intranet:usecase/cqm/repository/sonarqube_issue_repository.go", "project": "sa-intranet-libs-intranet", "line": 109, "hash": "f5def281b3d382a6b664de8017635eb7", "textRange": {"startLine": 109, "endLine": 109, "startOffset": 4, "endOffset": 8}, "flows": [], "status": "OPEN", "message": "Complete the task associated to this TODO comment.", "effort": "0min", "debt": "0min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-03-26T23:03:03+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Gl7J7LX6Rj5GLT", "rule": "go:S3776", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:cli/cli.go", "project": "sa-intranet-libs-intranet", "line": 15, "hash": "98c33b37cc24735233b2996db1332304", "textRange": {"startLine": 15, "endLine": 15, "startOffset": 5, "endOffset": 17}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 40, "endLine": 40, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 49, "endLine": 49, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 63, "endLine": 63, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 69, "endLine": 69, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 72, "endLine": 72, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 97, "endLine": 97, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 111, "endLine": 111, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 115, "endLine": 115, "startOffset": 3, "endOffset": 6}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 129, "endLine": 129, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 133, "endLine": 133, "startOffset": 3, "endOffset": 6}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 146, "endLine": 146, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 161, "endLine": 161, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:cli/cli.go", "textRange": {"startLine": 164, "endLine": 164, "startOffset": 3, "endOffset": 5}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this method to reduce its Cognitive Complexity from 28 to the 15 allowed.", "effort": "18min", "debt": "18min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-03-26T02:57:23+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Gs7J7LX6Rj5GLU", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:http/middlewares.go", "project": "sa-intranet-libs-intranet", "line": 99, "hash": "821d7e330628c4f187498f1938878e9d", "textRange": {"startLine": 99, "endLine": 99, "startOffset": 35, "endOffset": 53}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 153, "endLine": 153, "startOffset": 35, "endOffset": 53}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 211, "endLine": 211, "startOffset": 35, "endOffset": 53}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"application/json\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-02-04T16:13:48+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Gs7J7LX6Rj5GLW", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:http/middlewares.go", "project": "sa-intranet-libs-intranet", "line": 99, "hash": "821d7e330628c4f187498f1938878e9d", "textRange": {"startLine": 99, "endLine": 99, "startOffset": 19, "endOffset": 33}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 153, "endLine": 153, "startOffset": 19, "endOffset": 33}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 211, "endLine": 211, "startOffset": 19, "endOffset": 33}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"Content-Type\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-02-04T16:13:48+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZaHs7Gs7J7LX6Rj5GLV", "rule": "go:S1192", "severity": "CRITICAL", "component": "sa-intranet-libs-intranet:http/middlewares.go", "project": "sa-intranet-libs-intranet", "line": 116, "hash": "1dd92bec0c1653f754ec1fb05279150f", "textRange": {"startLine": 116, "endLine": 116, "startOffset": 16, "endOffset": 53}, "flows": [{"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 170, "endLine": 170, "startOffset": 16, "endOffset": 53}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "sa-intranet-libs-intranet:http/middlewares.go", "textRange": {"startLine": 228, "endLine": 228, "startOffset": 16, "endOffset": 53}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal \"failed to encode error response: %v\" 3 times.", "effort": "6min", "debt": "6min", "assignee": "eboni60579", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-02-04T16:13:48+0000", "updateDate": "2025-04-30T17:16:54+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}], "components": [{"key": "sa-intranet-libs-intranet:core/repository/save.go", "enabled": true, "qualifier": "FIL", "name": "save.go", "longName": "core/repository/save.go", "path": "core/repository/save.go"}, {"key": "sa-intranet-libs-intranet:db/migrate/migrations/20250326220941_sonarqube_projects.go", "enabled": true, "qualifier": "FIL", "name": "20250326220941_sonarqube_projects.go", "longName": "db/migrate/migrations/20250326220941_sonarqube_projects.go", "path": "db/migrate/migrations/20250326220941_sonarqube_projects.go"}, {"key": "sa-intranet-libs-intranet:Taskfile.yml", "enabled": true, "qualifier": "FIL", "name": "Taskfile.yml", "longName": "Taskfile.yml", "path": "Taskfile.yml"}, {"key": "sa-intranet-libs-intranet:cli/cli.go", "enabled": true, "qualifier": "FIL", "name": "cli.go", "longName": "cli/cli.go", "path": "cli/cli.go"}, {"key": "sa-intranet-libs-intranet:http/middlewares.go", "enabled": true, "qualifier": "FIL", "name": "middlewares.go", "longName": "http/middlewares.go", "path": "http/middlewares.go"}, {"key": "sa-intranet-libs-intranet:db/migrate/migrations/20250326221134_sonarqube_issues.go", "enabled": true, "qualifier": "FIL", "name": "20250326221134_sonarqube_issues.go", "longName": "db/migrate/migrations/20250326221134_sonarqube_issues.go", "path": "db/migrate/migrations/20250326221134_sonarqube_issues.go"}, {"key": "sa-intranet-libs-intranet:usecase/cqm/interactor/issues_interactor.go", "enabled": true, "qualifier": "FIL", "name": "issues_interactor.go", "longName": "usecase/cqm/interactor/issues_interactor.go", "path": "usecase/cqm/interactor/issues_interactor.go"}, {"key": "sa-intranet-libs-intranet:policies/rbac.go", "enabled": true, "qualifier": "FIL", "name": "rbac.go", "longName": "policies/rbac.go", "path": "policies/rbac.go"}, {"key": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/sonarqube.go", "enabled": true, "qualifier": "FIL", "name": "sonarqube.go", "longName": "usecase/cqm/sonarqube/sonarqube.go", "path": "usecase/cqm/sonarqube/sonarqube.go"}, {"key": "sa-intranet-libs-intranet:usecase/cqm/sonarqube/get_measures.go", "enabled": true, "qualifier": "FIL", "name": "get_measures.go", "longName": "usecase/cqm/sonarqube/get_measures.go", "path": "usecase/cqm/sonarqube/get_measures.go"}, {"key": "sa-intranet-libs-intranet:usecase/cqm/repository/sonarqube_analysis_repository.go", "enabled": true, "qualifier": "FIL", "name": "sonarqube_analysis_repository.go", "longName": "usecase/cqm/repository/sonarqube_analysis_repository.go", "path": "usecase/cqm/repository/sonarqube_analysis_repository.go"}, {"key": "sa-intranet-libs-intranet", "enabled": true, "qualifier": "TRK", "name": "SA Intranet - Libs Intranet", "longName": "SA Intranet - Libs Intranet"}, {"key": "sa-intranet-libs-intranet:db/migrate/migrations/20250415211514_sonarqube_analyses.go", "enabled": true, "qualifier": "FIL", "name": "20250415211514_sonarqube_analyses.go", "longName": "db/migrate/migrations/20250415211514_sonarqube_analyses.go", "path": "db/migrate/migrations/20250415211514_sonarqube_analyses.go"}, {"key": "sa-intranet-libs-intranet:db/migrate/migrations/20250326013035_jira_projects.go", "enabled": true, "qualifier": "FIL", "name": "20250326013035_jira_projects.go", "longName": "db/migrate/migrations/20250326013035_jira_projects.go", "path": "db/migrate/migrations/20250326013035_jira_projects.go"}, {"key": "sa-intranet-libs-intranet:usecase/cqm/repository/sonarqube_issue_repository.go", "enabled": true, "qualifier": "FIL", "name": "sonarqube_issue_repository.go", "longName": "usecase/cqm/repository/sonarqube_issue_repository.go", "path": "usecase/cqm/repository/sonarqube_issue_repository.go"}], "facets": []}
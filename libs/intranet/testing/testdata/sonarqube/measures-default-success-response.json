{"component": {"key": "example.project-backend", "name": "Example Project Backend", "qualifier": "TRK", "measures": [{"metric": "SMELL_COUNT_SPECULATIVE_GENERALITY", "value": "0", "bestValue": true}, {"metric": "coverage", "value": "78.6", "bestValue": false}, {"metric": "comment_lines_density", "value": "14.0", "bestValue": false}, {"metric": "open_issues", "value": "282", "bestValue": false}, {"metric": "new_reliability_remediation_effort", "periods": [{"index": 1, "value": "5", "bestValue": false}], "period": {"index": 1, "value": "5", "bestValue": false}}, {"metric": "tests", "value": "415"}, {"metric": "statements", "value": "5469"}, {"metric": "vulnerable_dependencies", "value": "12", "bestValue": false}, {"metric": "new_critical_violations", "periods": [{"index": 1, "value": "25", "bestValue": false}], "period": {"index": 1, "value": "25", "bestValue": false}}, {"metric": "file_complexity", "value": "3.3"}, {"metric": "new_uncovered_lines", "periods": [{"index": 1, "value": "260", "bestValue": false}], "period": {"index": 1, "value": "260", "bestValue": false}}, {"metric": "package_count", "value": "0"}, {"metric": "duplicated_files", "value": "2", "bestValue": false}, {"metric": "new_development_cost", "periods": [{"index": 1, "value": "135810.0", "bestValue": false}], "period": {"index": 1, "value": "135810.0", "bestValue": false}}, {"metric": "SMELL_COUNT_NON_EXCEPTION", "value": "0", "bestValue": true}, {"metric": "new_security_hotspots_to_review_status", "periods": [{"index": 1, "value": "0", "bestValue": true}], "period": {"index": 1, "value": "0", "bestValue": true}}, {"metric": "security_rating", "value": "5.0", "bestValue": false}, {"metric": "new_blocker_violations", "periods": [{"index": 1, "value": "2", "bestValue": false}], "period": {"index": 1, "value": "2", "bestValue": false}}, {"metric": "files", "value": "456"}, {"metric": "false_positive_issues", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_OTHER", "value": "0", "bestValue": true}, {"metric": "new_line_coverage", "periods": [{"index": 1, "value": "89.6", "bestValue": false}], "period": {"index": 1, "value": "89.6", "bestValue": false}}, {"metric": "new_conditions_to_cover", "periods": [{"index": 1, "value": "0"}], "period": {"index": 1, "value": "0"}}, {"metric": "number-of-classes-and-interfaces", "value": "0"}, {"metric": "duplicated_lines_density", "value": "0.4", "bestValue": false}, {"metric": "new_security_remediation_effort", "periods": [{"index": 1, "value": "0", "bestValue": true}], "period": {"index": 1, "value": "0", "bestValue": true}}, {"metric": "SMELL_COUNT_NON_COMPLIANCE_WITH_STANDARDS", "value": "0", "bestValue": true}, {"metric": "new_minor_violations", "periods": [{"index": 1, "value": "123", "bestValue": false}], "period": {"index": 1, "value": "123", "bestValue": false}}, {"metric": "SMELL_COUNT_ODDBALL_SOLUTION", "value": "0", "bestValue": true}, {"metric": "afferent-couplings", "value": "0"}, {"metric": "new_lines_to_cover", "periods": [{"index": 1, "value": "2496"}], "period": {"index": 1, "value": "2496"}}, {"metric": "vulnerable_component_ratio", "value": "3.08333", "bestValue": false}, {"metric": "new_code_smells", "periods": [{"index": 1, "value": "144", "bestValue": false}], "period": {"index": 1, "value": "144", "bestValue": false}}, {"metric": "new_coverage", "periods": [{"index": 1, "value": "89.6", "bestValue": false}], "period": {"index": 1, "value": "89.6", "bestValue": false}}, {"metric": "security_hotspots_reviewed_status", "value": "0", "bestValue": true}, {"metric": "new_vulnerabilities", "periods": [{"index": 1, "value": "37", "bestValue": false}], "period": {"index": 1, "value": "37", "bestValue": false}}, {"metric": "test_errors", "value": "0", "bestValue": true}, {"metric": "new_security_review_rating", "periods": [{"index": 1, "value": "1.0", "bestValue": true}], "period": {"index": 1, "value": "1.0", "bestValue": true}}, {"metric": "quality_profiles", "value": "[{\"key\":\"AX0PZ42bFbZHQ98KqoSq\",\"language\":\"php\",\"name\":\"Sonar way\",\"rulesUpdatedAt\":\"2024-12-15T14:32:35+0000\"}]"}, {"metric": "SMELL_COUNT_WRONG_LANGUAGE", "value": "0", "bestValue": true}, {"metric": "missing_package_info_count", "value": "0"}, {"metric": "functions", "value": "1042"}, {"metric": "new_duplicated_lines", "periods": [{"index": 1, "value": "16", "bestValue": false}], "period": {"index": 1, "value": "16", "bestValue": false}}, {"metric": "classes", "value": "429"}, {"metric": "bugs", "value": "3", "bestValue": false}, {"metric": "sqale_index", "value": "742", "bestValue": false}, {"metric": "new_security_rating", "periods": [{"index": 1, "value": "5.0", "bestValue": false}], "period": {"index": 1, "value": "5.0", "bestValue": false}}, {"metric": "new_duplicated_lines_density", "periods": [{"index": 1, "value": "0.13425", "bestValue": false}], "period": {"index": 1, "value": "0.13425", "bestValue": false}}, {"metric": "SMELL_DEBT", "value": "0", "bestValue": true}, {"metric": "new_info_violations", "periods": [{"index": 1, "value": "1", "bestValue": false}], "period": {"index": 1, "value": "1", "bestValue": false}}, {"metric": "SMELL_COUNT_REFUSED_BEQUEST", "value": "0", "bestValue": true}, {"metric": "vulnerabilities", "value": "37", "bestValue": false}, {"metric": "SMELL_COUNT_USELESS_TEST", "value": "0", "bestValue": true}, {"metric": "cognitive_complexity", "value": "566", "bestValue": false}, {"metric": "medium_severity_vulns", "value": "10", "bestValue": false}, {"metric": "package-dependency-cycles", "value": "0"}, {"metric": "new_duplicated_blocks", "periods": [{"index": 1, "value": "2", "bestValue": false}], "period": {"index": 1, "value": "2", "bestValue": false}}, {"metric": "SMELL_COUNT_OVERCOMPLICATED_ALGORITHM", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_BAD_DESIGN", "value": "0", "bestValue": true}, {"metric": "low_severity_vulns", "value": "0", "bestValue": true}, {"metric": "line_coverage", "value": "78.6", "bestValue": false}, {"metric": "effort_to_reach_maintainability_rating_a", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_HOW_COMMENT", "value": "0", "bestValue": true}, {"metric": "new_reliability_rating", "periods": [{"index": 1, "value": "2.0", "bestValue": false}], "period": {"index": 1, "value": "2.0", "bestValue": false}}, {"metric": "security_remediation_effort", "value": "0", "bestValue": true}, {"metric": "new_bugs", "periods": [{"index": 1, "value": "1", "bestValue": false}], "period": {"index": 1, "value": "1", "bestValue": false}}, {"metric": "total_vulnerabilities", "value": "37", "bestValue": false}, {"metric": "minor_violations", "value": "155", "bestValue": false}, {"metric": "comment_lines", "value": "2228"}, {"metric": "new_uncovered_conditions", "periods": [{"index": 1, "value": "0", "bestValue": true}], "period": {"index": 1, "value": "0", "bestValue": true}}, {"metric": "inherited_risk_score", "value": "169", "bestValue": false}, {"metric": "SMELL_COUNT_INDECENT_EXPOSURE", "value": "0", "bestValue": true}, {"metric": "uncovered_lines", "value": "1289", "bestValue": false}, {"metric": "high_severity_vulns", "value": "25", "bestValue": false}, {"metric": "violations", "value": "282", "bestValue": false}, {"metric": "SMELL_COUNT_ABBREVIATIONS_USAGE", "value": "0", "bestValue": true}, {"metric": "sqale_debt_ratio", "value": "0.2", "bestValue": false}, {"metric": "new_violations", "periods": [{"index": 1, "value": "182", "bestValue": false}], "period": {"index": 1, "value": "182", "bestValue": false}}, {"metric": "critical_severity_vulns", "value": "2", "bestValue": false}, {"metric": "total_dependencies", "value": "148"}, {"metric": "new_lines", "periods": [{"index": 1, "value": "11918"}], "period": {"index": 1, "value": "11918"}}, {"metric": "reliability_rating", "value": "2.0", "bestValue": false}, {"metric": "duplicated_blocks", "value": "2", "bestValue": false}, {"metric": "ncloc_language_distribution", "value": "php=13687"}, {"metric": "security_hotspots", "value": "0", "bestValue": true}, {"metric": "skipped_tests", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_MISSING_DOCUMENTATION", "value": "0", "bestValue": true}, {"metric": "quality_gate_details", "value": "{\"level\":\"ERROR\",\"conditions\":[{\"metric\":\"new_reliability_rating\",\"op\":\"GT\",\"period\":1,\"error\":\"1\",\"actual\":\"2\",\"level\":\"ERROR\"},{\"metric\":\"reliability_rating\",\"op\":\"GT\",\"error\":\"3\",\"actual\":\"2\",\"level\":\"OK\"},{\"metric\":\"new_security_rating\",\"op\":\"GT\",\"period\":1,\"error\":\"1\",\"actual\":\"5\",\"level\":\"ERROR\"},{\"metric\":\"security_rating\",\"op\":\"GT\",\"error\":\"3\",\"actual\":\"5\",\"level\":\"ERROR\"},{\"metric\":\"new_maintainability_rating\",\"op\":\"GT\",\"period\":1,\"error\":\"1\",\"actual\":\"1\",\"level\":\"OK\"},{\"metric\":\"sqale_rating\",\"op\":\"GT\",\"error\":\"2\",\"actual\":\"1\",\"level\":\"OK\"},{\"metric\":\"new_coverage\",\"op\":\"LT\",\"period\":1,\"error\":\"80\",\"actual\":\"89.6\",\"level\":\"OK\"},{\"metric\":\"coverage\",\"op\":\"LT\",\"error\":\"60\",\"actual\":\"78.6\",\"level\":\"OK\"},{\"metric\":\"new_duplicated_lines_density\",\"op\":\"GT\",\"period\":1,\"error\":\"3\",\"actual\":\"0.13425\",\"level\":\"OK\"},{\"metric\":\"blocker_violations\",\"op\":\"GT\",\"error\":\"0\",\"actual\":\"2\",\"level\":\"ERROR\"},{\"metric\":\"comment_lines_density\",\"op\":\"LT\",\"error\":\"15\",\"actual\":\"14.0\",\"level\":\"ERROR\"},{\"metric\":\"critical_violations\",\"op\":\"GT\",\"error\":\"0\",\"actual\":\"25\",\"level\":\"ERROR\"},{\"metric\":\"duplicated_blocks\",\"op\":\"GT\",\"error\":\"10\",\"actual\":\"2\",\"level\":\"OK\"},{\"metric\":\"major_violations\",\"op\":\"GT\",\"error\":\"100\",\"actual\":\"99\",\"level\":\"OK\"},{\"metric\":\"new_critical_violations\",\"op\":\"GT\",\"period\":1,\"error\":\"0\",\"actual\":\"25\",\"level\":\"ERROR\"},{\"metric\":\"new_major_violations\",\"op\":\"GT\",\"period\":1,\"error\":\"25\",\"actual\":\"31\",\"level\":\"ERROR\"},{\"metric\":\"reopened_issues\",\"op\":\"GT\",\"error\":\"100\",\"actual\":\"0\",\"level\":\"OK\"}],\"ignoredConditions\":false}"}, {"metric": "major_violations", "value": "99", "bestValue": false}, {"metric": "critical_violations", "value": "25", "bestValue": false}, {"metric": "SMELL_COUNT_WRONG_LOGIC", "value": "0", "bestValue": true}, {"metric": "test_success_density", "value": "100.0", "bestValue": true}, {"metric": "info_violations", "value": "1", "bestValue": false}, {"metric": "SMELL_COUNT_MISSING_TEST", "value": "0", "bestValue": true}, {"metric": "wont_fix_issues", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_PRIMITIVES_OBSESSION", "value": "0", "bestValue": true}, {"metric": "package", "value": "0"}, {"metric": "new_technical_debt", "periods": [{"index": 1, "value": "345", "bestValue": false}], "period": {"index": 1, "value": "345", "bestValue": false}}, {"metric": "security_review_rating", "value": "1.0", "bestValue": true}, {"metric": "lines", "value": "22328"}, {"metric": "SMELL_COUNT_BAD_LOGGING", "value": "0", "bestValue": true}, {"metric": "blocker_violations", "value": "2", "bestValue": false}, {"metric": "lines_to_cover", "value": "6033"}, {"metric": "sqale_rating", "value": "1.0", "bestValue": true}, {"metric": "new_maintainability_rating", "periods": [{"index": 1, "value": "1.0", "bestValue": true}], "period": {"index": 1, "value": "1.0", "bestValue": true}}, {"metric": "SMELL_COUNT_ANTI_PATTERN", "value": "0", "bestValue": true}, {"metric": "new_security_hotspots_reviewed_status", "periods": [{"index": 1, "value": "0", "bestValue": true}], "period": {"index": 1, "value": "0", "bestValue": true}}, {"metric": "ncloc", "value": "13687"}, {"metric": "test_execution_time", "value": "698528"}, {"metric": "confirmed_issues", "value": "0", "bestValue": true}, {"metric": "efferent-couplings", "value": "0"}, {"metric": "alert_status", "value": "ERROR"}, {"metric": "reliability_remediation_effort", "value": "15", "bestValue": false}, {"metric": "SMELL_COUNT_REINVENTED_WHEEL", "value": "0", "bestValue": true}, {"metric": "missing_package_info", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_MISSING_IMPLEMENTATION", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_UNCOMMUNICATIVE_NAME", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_MIDDLE_MAN", "value": "0", "bestValue": true}, {"metric": "security_hotspots_to_review_status", "value": "0", "bestValue": true}, {"metric": "complexity", "value": "1497"}, {"metric": "last_commit_date", "value": "1744225158000"}, {"metric": "SMELL_COUNT_MEANINGLESS_COMMENT", "value": "0", "bestValue": true}, {"metric": "reopened_issues", "value": "0", "bestValue": true}, {"metric": "duplicated_lines", "value": "92", "bestValue": false}, {"metric": "new_sqale_debt_ratio", "periods": [{"index": 1, "value": "0.25403", "bestValue": false}], "period": {"index": 1, "value": "0.25403", "bestValue": false}}, {"metric": "SMELL_COUNT", "value": "0", "bestValue": true}, {"metric": "test_failures", "value": "0", "bestValue": true}, {"metric": "new_security_hotspots", "periods": [{"index": 1, "value": "0", "bestValue": true}], "period": {"index": 1, "value": "0", "bestValue": true}}, {"metric": "SMELL_COUNT_SOLUTION_SPRAWL", "value": "0", "bestValue": true}, {"metric": "code_smells", "value": "242", "bestValue": false}, {"metric": "SMELL_COUNT_MULTIPLE_RESPONSIBILITIES", "value": "0", "bestValue": true}, {"metric": "SMELL_COUNT_BAD_FRAMEWORK_USAGE", "value": "0", "bestValue": true}, {"metric": "new_major_violations", "periods": [{"index": 1, "value": "31", "bestValue": false}], "period": {"index": 1, "value": "31", "bestValue": false}}]}, "metrics": [{"key": "skipped_tests", "name": "Skipped Unit Tests", "description": "Number of skipped unit tests", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "lines", "name": "Lines", "description": "Lines", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "generated_lines", "name": "Generated Lines", "description": "Number of generated lines", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "ncloc", "name": "Lines of Code", "description": "Non commenting lines of code", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "new_lines", "name": "New Lines", "description": "New lines", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "ncloc_language_distribution", "name": "Lines of Code Per Language", "description": "Non Commenting Lines of Code Distributed By Language", "domain": "Size", "type": "DATA", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "generated_ncloc", "name": "Generated Lines of Code", "description": "Generated non Commenting Lines of Code", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "classes", "name": "Classes", "description": "Classes", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "files", "name": "Files", "description": "Number of files", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "directories", "name": "Directories", "description": "Directories", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "functions", "name": "Functions", "description": "Functions", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "statements", "name": "Statements", "description": "Number of statements", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "public_api", "name": "Public API", "description": "Public API", "domain": "Documentation", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true}, {"key": "projects", "name": "Project branches", "description": "Number of project branches", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "comment_lines", "name": "Comment Lines", "description": "Number of comment lines", "domain": "Size", "type": "INT", "higherValuesAreBetter": true, "qualitative": false, "hidden": false}, {"key": "comment_lines_density", "name": "Comments (%)", "description": "Comments balanced by ncloc + comment lines", "domain": "Size", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "public_documented_api_density", "name": "Public Documented API (%)", "description": "Public documented classes and functions balanced by ncloc", "domain": "Documentation", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": true, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "public_undocumented_api", "name": "Public Undocumented API", "description": "Public undocumented classes, functions and variables", "domain": "Documentation", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": true, "bestValue": "0"}, {"key": "complexity", "name": "Cyclomatic Complexity", "description": "Cyclomatic complexity", "domain": "Complexity", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "file_complexity", "name": "Complexity / File", "description": "Complexity average by file", "domain": "Complexity", "type": "FLOAT", "higherValuesAreBetter": false, "qualitative": true, "hidden": true, "decimalScale": 1}, {"key": "complexity_in_classes", "name": "Complexity in Classes", "description": "Cyclomatic complexity in classes", "domain": "Complexity", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true}, {"key": "class_complexity", "name": "Complexity / Class", "description": "Complexity average by class", "domain": "Complexity", "type": "FLOAT", "higherValuesAreBetter": false, "qualitative": true, "hidden": true, "decimalScale": 1}, {"key": "complexity_in_functions", "name": "Complexity in Functions", "description": "Cyclomatic complexity in functions", "domain": "Complexity", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true}, {"key": "function_complexity", "name": "Complexity / Function", "description": "Complexity average by function", "domain": "Complexity", "type": "FLOAT", "higherValuesAreBetter": false, "qualitative": true, "hidden": true, "decimalScale": 1}, {"key": "function_complexity_distribution", "name": "Function Distribution / Complexity", "description": "Functions distribution /complexity", "domain": "Complexity", "type": "DISTRIB", "qualitative": true, "hidden": true}, {"key": "file_complexity_distribution", "name": "File Distribution / Complexity", "description": "Files distribution /complexity", "domain": "Complexity", "type": "DISTRIB", "qualitative": true, "hidden": true}, {"key": "cognitive_complexity", "name": "Cognitive Complexity", "description": "Cognitive complexity", "domain": "Complexity", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "tests", "name": "Unit Tests", "description": "Number of unit tests", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": true, "qualitative": false, "hidden": false}, {"key": "test_execution_time", "name": "Unit Test Duration", "description": "Execution duration of unit tests", "domain": "Coverage", "type": "MILLISEC", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "test_errors", "name": "Unit Test Errors", "description": "Number of unit test errors", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "test_failures", "name": "Unit Test Failures", "description": "Number of unit test failures", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "test_success_density", "name": "Unit Test Success (%)", "description": "Density of successful unit tests", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "coverage", "name": "Coverage", "description": "Coverage by tests", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "new_coverage", "name": "Coverage on New Code", "description": "Coverage of new/changed code", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "lines_to_cover", "name": "Lines to Cover", "description": "Lines to cover", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "new_lines_to_cover", "name": "Lines to Cover on New Code", "description": "Lines to cover on new code", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "uncovered_lines", "name": "Uncovered Lines", "description": "Uncovered lines", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_uncovered_lines", "name": "Uncovered Lines on New Code", "description": "Uncovered lines on new code", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "line_coverage", "name": "Line Coverage", "description": "Line coverage", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "new_line_coverage", "name": "Line Coverage on New Code", "description": "Line coverage of added/changed code", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "conditions_to_cover", "name": "Conditions to Cover", "description": "Conditions to cover", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "new_conditions_to_cover", "name": "Conditions to Cover on New Code", "description": "Conditions to cover on new code", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "uncovered_conditions", "name": "Uncovered Conditions", "description": "Uncovered conditions", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_uncovered_conditions", "name": "Uncovered Conditions on New Code", "description": "Uncovered conditions on new code", "domain": "Coverage", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "branch_coverage", "name": "Condition Coverage", "description": "Condition coverage", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "new_branch_coverage", "name": "Condition Coverage on New Code", "description": "Condition coverage of new/changed code", "domain": "Coverage", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "duplicated_lines", "name": "Duplicated Lines", "description": "Duplicated lines", "domain": "Duplications", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_duplicated_lines", "name": "Duplicated Lines on New Code", "description": "Duplicated Lines on New Code", "domain": "Duplications", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "duplicated_blocks", "name": "Duplicated Blocks", "description": "Duplicated blocks", "domain": "Duplications", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_duplicated_blocks", "name": "Duplicated Blocks on New Code", "description": "Duplicated blocks on new code", "domain": "Duplications", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "duplicated_files", "name": "Duplicated Files", "description": "Duplicated files", "domain": "Duplications", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "duplicated_lines_density", "name": "Duplicated Lines (%)", "description": "Duplicated lines balanced by statements", "domain": "Duplications", "type": "PERCENT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "0.0", "worstValue": "100.0"}, {"key": "new_duplicated_lines_density", "name": "Duplicated Lines (%) on New Code", "description": "Duplicated lines (%) on new code balanced by statements", "domain": "Duplications", "type": "PERCENT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "0.0", "worstValue": "100.0"}, {"key": "duplications_data", "name": "Duplication Details", "description": "Duplications details", "domain": "Duplications", "type": "DATA", "qualitative": false, "hidden": false}, {"key": "violations", "name": "Issues", "description": "Issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "blocker_violations", "name": "Blocker Issues", "description": "Blocker issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "critical_violations", "name": "Critical Issues", "description": "Critical issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "major_violations", "name": "Major Issues", "description": "Major issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "minor_violations", "name": "Minor Issues", "description": "Minor issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "info_violations", "name": "Info Issues", "description": "Info issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_violations", "name": "New Issues", "description": "New issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_blocker_violations", "name": "New Blocker Issues", "description": "New Blocker issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_critical_violations", "name": "New Critical Issues", "description": "New Critical issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_major_violations", "name": "New Major Issues", "description": "New Major issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_minor_violations", "name": "New Minor Issues", "description": "New Minor issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_info_violations", "name": "New Info Issues", "description": "New Info issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "false_positive_issues", "name": "False Positive Issues", "description": "False positive issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "wont_fix_issues", "name": "Won't Fix Issues", "description": "Won't fix issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "open_issues", "name": "Open Issues", "description": "Open issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "reopened_issues", "name": "Reopened Issues", "description": "Reopened issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "confirmed_issues", "name": "Confirmed Issues", "description": "Confirmed issues", "domain": "Issues", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "code_smells", "name": "Code Smells", "description": "Code Smells", "domain": "Maintainability", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_code_smells", "name": "New Code Smells", "description": "New Code Smells", "domain": "Maintainability", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "bugs", "name": "Bugs", "description": "Bugs", "domain": "Reliability", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_bugs", "name": "New Bugs", "description": "New Bugs", "domain": "Reliability", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "vulnerabilities", "name": "Vulnerabilities", "description": "Vulnerabilities", "domain": "Security", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_vulnerabilities", "name": "New Vulnerabilities", "description": "New Vulnerabilities", "domain": "Security", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "security_hotspots", "name": "Security Hotspots", "description": "Security Hotspots", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "new_security_hotspots", "name": "New Security Hotspots", "description": "New Security Hotspots", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "sqale_index", "name": "Technical Debt", "description": "Total effort (in hours) to fix all the issues on the component and therefore to comply to all the requirements.", "domain": "Maintainability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_technical_debt", "name": "Added Technical Debt", "description": "Added technical debt", "domain": "Maintainability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "sqale_rating", "name": "Maintainability Rating", "description": "A-to-E rating based on the technical debt ratio", "domain": "Maintainability", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "new_maintainability_rating", "name": "Maintainability Rating on New Code", "description": "Maintainability rating on new code", "domain": "Maintainability", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "new_development_cost", "name": "Development Cost on New Code", "description": "Development cost on new code", "domain": "Maintainability", "type": "FLOAT", "higherValuesAreBetter": false, "qualitative": true, "hidden": true, "decimalScale": 1, "bestValue": "0.0"}, {"key": "sqale_debt_ratio", "name": "Technical Debt Ratio", "description": "Ratio of the actual technical debt compared to the estimated cost to develop the whole source code from scratch", "domain": "Maintainability", "type": "PERCENT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "0.0", "worstValue": "100.0"}, {"key": "new_sqale_debt_ratio", "name": "Technical Debt Ratio on New Code", "description": "Technical Debt Ratio of new/changed code.", "domain": "Maintainability", "type": "PERCENT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "0.0", "worstValue": "100.0"}, {"key": "effort_to_reach_maintainability_rating_a", "name": "Effort to Reach Maintainability Rating A", "description": "Effort to reach maintainability rating A", "domain": "Maintainability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "reliability_remediation_effort", "name": "Reliability Remediation Effort", "description": "Reliability Remediation Effort", "domain": "Reliability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_reliability_remediation_effort", "name": "Reliability Remediation Effort on New Code", "description": "Reliability remediation effort on new code", "domain": "Reliability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "reliability_rating", "name": "Reliability Rating", "description": "Reliability rating", "domain": "Reliability", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "new_reliability_rating", "name": "Reliability Rating on New Code", "description": "Reliability rating on new code", "domain": "Reliability", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "security_remediation_effort", "name": "Security Remediation Effort", "description": "Security remediation effort", "domain": "Security", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "new_security_remediation_effort", "name": "Security Remediation Effort on New Code", "description": "Security remediation effort on new code", "domain": "Security", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "security_rating", "name": "Security Rating", "description": "Security rating", "domain": "Security", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "new_security_rating", "name": "Security Rating on New Code", "description": "Security rating on new code", "domain": "Security", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "security_review_rating", "name": "Security Review Rating", "description": "Security Review Rating", "domain": "SecurityReview", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "new_security_review_rating", "name": "Security Review Rating on New Code", "description": "Security Review Rating on New Code", "domain": "SecurityReview", "type": "RATING", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "1.0", "worstValue": "5.0"}, {"key": "security_hotspots_reviewed", "name": "Security Hotspots Reviewed", "description": "Percentage of Security Hotspots Reviewed", "domain": "SecurityReview", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "new_security_hotspots_reviewed", "name": "Security Hotspots Reviewed on New Code", "description": "Percentage of Security Hotspots Reviewed on New Code", "domain": "SecurityReview", "type": "PERCENT", "higherValuesAreBetter": true, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "100.0", "worstValue": "0.0"}, {"key": "security_hotspots_reviewed_status", "name": "Security Review Reviewed Status", "description": "Security Review Reviewed Status", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true, "bestValue": "0"}, {"key": "security_hotspots_to_review_status", "name": "Security Review To Review Status", "description": "Security Review To Review Status", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true, "bestValue": "0"}, {"key": "new_security_hotspots_reviewed_status", "name": "Security Review Reviewed Status on New Code", "description": "Security Review Reviewed Status on New Code", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true, "bestValue": "0"}, {"key": "new_security_hotspots_to_review_status", "name": "Security Review To Review Status on New Code", "description": "Security Review To Review Status on New Code", "domain": "SecurityReview", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true, "bestValue": "0"}, {"key": "ncloc_data", "name": "ncloc_data", "domain": "Size", "type": "DATA", "qualitative": false, "hidden": true}, {"key": "comment_lines_data", "name": "comment_lines_data", "domain": "Size", "type": "DATA", "qualitative": false, "hidden": true}, {"key": "executable_lines_data", "name": "executable_lines_data", "domain": "Coverage", "type": "DATA", "qualitative": false, "hidden": true}, {"key": "alert_status", "name": "Quality Gate Status", "description": "The project status with regard to its quality gate.", "domain": "Releasability", "type": "LEVEL", "higherValuesAreBetter": true, "qualitative": true, "hidden": false}, {"key": "quality_gate_details", "name": "Quality Gate Details", "description": "The project detailed status with regard to its quality gate", "domain": "General", "type": "DATA", "qualitative": false, "hidden": false}, {"key": "quality_profiles", "name": "Profiles", "description": "Details of quality profiles used during analysis", "domain": "General", "type": "DATA", "qualitative": false, "hidden": true}, {"key": "last_commit_date", "name": "Date of Last Commit", "domain": "SCM", "type": "MILLISEC", "qualitative": false, "hidden": true}, {"key": "unanalyzed_c", "name": "Number of unanalyzed c files", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true}, {"key": "unanalyzed_cpp", "name": "Number of unanalyzed c++ files", "domain": "Size", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": true}, {"key": "inherited_risk_score", "name": "Inherited Risk Score", "description": "Inherited Risk Score", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "vulnerable_component_ratio", "name": "Vulnerable Component Ratio", "description": "Vulnerable Component Ratio", "domain": "OWASP-Dependency-Check", "type": "PERCENT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "decimalScale": 1, "bestValue": "0.0", "worstValue": "100.0"}, {"key": "critical_severity_vulns", "name": "Critical Severity Vulnerabilities", "description": "Critical Severity Vulnerabilities", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "high_severity_vulns", "name": "High Severity Vulnerabilities", "description": "High Severity Vulnerabilities", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "medium_severity_vulns", "name": "Medium Severity Vulnerabilities", "description": "Medium Severity Vulnerabilities", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "low_severity_vulns", "name": "Low Severity Vulnerabilities", "description": "Low Severity Vulnerabilities", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "total_dependencies", "name": "Total Dependencies", "description": "Total Dependencies", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false}, {"key": "vulnerable_dependencies", "name": "Vulnerable Dependencies", "description": "Vulnerable Dependencies", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "total_vulnerabilities", "name": "Total Vulnerabilities", "description": "Total Vulnerabilities", "domain": "OWASP-Dependency-Check", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_DEBT", "name": "Code Smells debt (reported by manual reviews)", "description": "Technical debt reported by developers.", "domain": "Maintainability", "type": "WORK_DUR", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT", "name": "Smells count", "description": "Total number of reported code smells.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_WRONG_LOGIC", "name": "Wrong logic count", "description": "Number of wrong logics reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_WRONG_LANGUAGE", "name": "Wrong language count", "description": "Number of wrong languages reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_OVERCOMPLICATED_ALGORITHM", "name": "Overcomplicated algorithm count", "description": "Number of overcomplicated algorithms reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_ANTI_PATTERN", "name": "Anti-pattern count", "description": "Number of anti-patterns reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_BAD_DESIGN", "name": "Bad design count", "description": "Number of bad designs reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_USELESS_TEST", "name": "Useless test count", "description": "Number of useless tests reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MEANINGLESS_COMMENT", "name": "Meaningless comment count", "description": "Number of meaningless comments reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_UNCOMMUNICATIVE_NAME", "name": "Uncommunicative name count", "description": "Number of uncommunicative names reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_SPECULATIVE_GENERALITY", "name": "Speculative generality count", "description": "Number of speculative generalities reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_ODDBALL_SOLUTION", "name": "Oddball solution count", "description": "Number of oddball solutions reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_PRIMITIVES_OBSESSION", "name": "Primitives obsession count", "description": "Number of primitives obsessions reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_INDECENT_EXPOSURE", "name": "Indecent exposure count", "description": "Number of indecent exposures reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_SOLUTION_SPRAWL", "name": "Solution sprawl count", "description": "Number of solution sprawls reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MIDDLE_MAN", "name": "Middle man count", "description": "Number of middle men reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_REFUSED_BEQUEST", "name": "Refused bequest count", "description": "Number of refused bequests reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_NON_EXCEPTION", "name": "Non exception count", "description": "Number of non exceptions reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_HOW_COMMENT", "name": "How comment count", "description": "Number of how comments reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MISSING_IMPLEMENTATION", "name": "Missing implementation count", "description": "Number of missing implementations reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MULTIPLE_RESPONSIBILITIES", "name": "Multiple responsibilities count", "description": "Number of multiple responsibilities reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_ABBREVIATIONS_USAGE", "name": "Abbreviations usage count", "description": "Number of abbreviations usages reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_BAD_LOGGING", "name": "Bad logging count", "description": "Number of bad loggings reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_REINVENTED_WHEEL", "name": "Reinvented wheel count", "description": "Number of reinvented wheels reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_BAD_FRAMEWORK_USAGE", "name": "Bad framework usage count", "description": "Number of bad framework usages reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MISSING_DOCUMENTATION", "name": "Missing documentation count", "description": "Number of missing documentation reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_MISSING_TEST", "name": "Missing tests count", "description": "Number of missing tests reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_OTHER", "name": "Uncategorized smells count", "description": "Number of uncategorized smells reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "SMELL_COUNT_NON_COMPLIANCE_WITH_STANDARDS", "name": "Non compliances with standards count", "description": "Number of non compliances with standards reported by developers.", "domain": "Code Smells", "type": "INT", "higherValuesAreBetter": false, "qualitative": false, "hidden": false, "bestValue": "0"}, {"key": "afferent-couplings", "name": "Afferent couplings", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": false}, {"key": "efferent-couplings", "name": "Efferent couplings", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": false}, {"key": "number-of-classes-and-interfaces", "name": "Number of classes and interfaces", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": false}, {"key": "package-dependency-cycles", "name": "Package dependency cycles", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": false}, {"key": "missing_package_info", "name": "Missing package-info.java files", "domain": "Jdepend", "type": "INT", "higherValuesAreBetter": false, "qualitative": true, "hidden": false, "bestValue": "0"}, {"key": "package", "name": "Number of packages", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": false}, {"key": "missing_package_info_count", "name": "Missing package-info.java files", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": true}, {"key": "package_count", "name": "Number of packages", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": true}, {"key": "package_info_count", "name": "Number of package-info.java files", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": true}, {"key": "java_count", "name": "Number of java files", "domain": "Jdepend", "type": "INT", "qualitative": false, "hidden": true}], "periods": [{"index": 1, "mode": "PREVIOUS_VERSION", "date": "2024-04-25T19:10:19+0000"}], "period": {"index": 1, "mode": "PREVIOUS_VERSION", "date": "2024-04-25T19:10:19+0000"}}
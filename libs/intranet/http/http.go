// Package http implements the core app functionality and shared components
package http

import (
	"errors"

	"github.com/emilioforrer/hexa/httpsvr"
	"github.com/spf13/viper"
)

type Config struct {
	BaseURL string `mapstructure:"base_url"`
}

type App struct {
	DefaultApp httpsvr.App
	Config     Config
}

func NewApp() *App {
	v := viper.New()

	// Set default values
	v.SetDefault("base_url", "http://localhost:8000")

	// Enable reading from environment variables
	v.AutomaticEnv()

	// Optional: specify config files
	// v.SetConfigName("config")
	// v.SetConfigType("yaml")
	// v.AddConfigPath(".")
	// v.AddConfigPath("./config")

	// Load .env file
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(".")

	// Read config file
	if err := v.ReadInConfig(); err != nil {
		var configFileNotFoundError viper.ConfigFileNotFoundError

		if !errors.As(err, &configFileNotFoundError) {
			panic(err)
		}
	}

	// Unmarshal config
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		panic(err)
	}

	app := &App{
		DefaultApp: httpsvr.NewApp(),
		Config:     config,
	}

	return app
}

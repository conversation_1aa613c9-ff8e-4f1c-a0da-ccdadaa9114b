package auth

import (
	"net/http"
	"time"
)

func LogoutHandler(logOutURL string) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		logoutURL := logOutURL
		// Clear the authentication cookies (you may have multiple cookies to clear, depending on your setup)
		http.SetCookie(w, &http.Cookie{
			Name:     "AWSELBAuthSessionCookie-0",
			Value:    "",
			Path:     "/",
			Expires:  time.Unix(0, 0),
			HttpOnly: true,
		})
		http.SetCookie(w, &http.Cookie{
			Name:     "AWSELBAuthSessionCookie-1",
			Value:    "",
			Path:     "/",
			Expires:  time.Unix(0, 0),
			HttpOnly: true,
		})
		// Redirect the user to the Cognito logout URL
		http.Redirect(w, r, logoutURL, http.StatusSeeOther)
	})
}

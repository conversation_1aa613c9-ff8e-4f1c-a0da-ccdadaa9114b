// Package client implements the HTTP client for Papyrus service
package client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strings"

	"golang.org/x/exp/slog"
)

var (
	ErrClientRequired    = errors.New("client is required")
	ErrBaseURLRequired   = errors.New("baseURL is required")
	ErrInvalidHTTPMethod = errors.New("invalid HTTP method")
	ErrAPIRequest        = errors.New("API request error")
)

var methods = []string{
	http.MethodGet,
	http.MethodPost,
	http.MethodPut,
	http.MethodDelete,
	http.MethodPatch,
	http.MethodHead,
	http.MethodOptions,
	http.MethodTrace,
	http.MethodConnect,
}

type RequestApply func(*http.Request)

type DefaultClient struct {
	Client *http.Client
}

func (dc *DefaultClient) Request(
	baseURL string,
	method string,
	path string,
	body io.Reader,
	applier RequestApply,
) (*http.Response, error) {
	if strings.TrimSpace(baseURL) == "" {
		return nil, ErrBaseURLRequired
	}

	if !slices.Contains(methods, method) {
		return nil, fmt.Errorf("%w: %s", ErrInvalidHTTPMethod, method)
	}

	req, err := http.NewRequestWithContext(context.Background(), method, baseURL+path, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if applier != nil {
		applier(req)
	}

	resp, err := dc.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to request: %w", err)
	}

	if resp.StatusCode >= 400 && resp.StatusCode < 600 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("%w: status %d: %s", ErrAPIRequest, resp.StatusCode, string(bodyBytes))
	}

	return resp, nil
}

func (dc *DefaultClient) RequestJSON(
	baseURL string,
	method string,
	path string,
	body io.Reader,
	target interface{},
	applier RequestApply,
) error {
	resp, err := dc.Request(baseURL, method, path, body, applier)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			slog.Error("failed to close response body", "error", err)
		}
	}()

	if err := json.NewDecoder(resp.Body).Decode(target); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	return nil
}

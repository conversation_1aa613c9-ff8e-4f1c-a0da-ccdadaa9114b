package client_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"sa-intranet/http/client"
)

func TestDefaultClient(t *testing.T) {
	// Test creating a DefaultClient directly
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	if httpClient == nil {
		t.<PERSON><PERSON>("DefaultClient creation failed")
	}

	if httpClient.Client == nil {
		t.<PERSON>("DefaultClient.Client is nil")
	}
}

func TestDefaultClientRequest(t *testing.T) {
	// Create a simple test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	}))
	defer server.Close()

	// Create client
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	// Test successful request
	resp, err := httpClient.Request(server.URL, "GET", "", nil, nil)
	if err != nil {
		t.<PERSON><PERSON>("Request() unexpected error = %v", err)
		return
	}

	if resp == nil {
		t.<PERSON><PERSON>rf("Request() returned nil response")
		return
	}

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Request() status = %v, want %v", resp.StatusCode, http.StatusOK)
	}

	resp.Body.Close()
}

func TestDefaultClientRequestErrors(t *testing.T) {
	httpClient := &client.DefaultClient{
		Client: &http.Client{},
	}

	tests := []struct {
		name    string
		baseURL string
		method  string
		wantErr bool
	}{
		{
			name:    "empty baseURL",
			baseURL: "",
			method:  "GET",
			wantErr: true,
		},
		{
			name:    "invalid method",
			baseURL: "http://example.com",
			method:  "INVALID",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := httpClient.Request(tt.baseURL, tt.method, "", nil, nil)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Request() expected error, got nil")
				}
			} else {
				if err != nil {
					t.Errorf("Request() unexpected error = %v", err)
				}
			}
		})
	}
}

package interactor_test

import (
	"encoding/json"
	"testing"

	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/interactor"
)

func testGetSonarQubeWebhookCases(t *testing.T) []struct {
	name    string
	input   interactor.SonarQubeWebhook
	wantErr bool
} {
	t.Helper() // This marks the function as a helper

	payloadFailed, err := testhelpers.FixtureFS.ReadFile("testdata/sonarqube/webhook-default-failed.json")
	if err != nil {
		t.Fatalf("Failed to read webhook-default-failed.json: %v", err)
	}
	var webhookFailed interactor.SonarQubeWebhook

	err = json.Unmarshal(payloadFailed, &webhookFailed)
	if err != nil {
		t.Fatalf("Failed to unmarshal webhook-default-failed.json: %v", err)
	}

	payloadSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/sonarqube/webhook-default-success.json")
	if err != nil {
		t.Fatalf("Failed to read webhook-default-success.json: %v", err)
	}

	var webhookSuccess interactor.SonarQubeWebhook

	err = json.Unmarshal(payloadSuccess, &webhookSuccess)
	if err != nil {
		t.Fatalf("Failed to unmarshal webhook-default-success.json: %v", err)
	}

	return []struct {
		name    string
		input   interactor.SonarQubeWebhook
		wantErr bool
	}{
		{
			name:    "valid webhook with error status",
			input:   webhookFailed,
			wantErr: false,
		},
		{
			name:    "valid webhook with success status",
			input:   webhookSuccess,
			wantErr: false,
		},
		{
			name: "webhook with invalid date format",
			input: interactor.SonarQubeWebhook{
				ServerURL:  "https://sonarqube.example.com",
				TaskID:     "AZYbjyjomwfuibSArRzI",
				Status:     "SUCCESS",
				AnalysedAt: "invalid-date",
				ChangedAt:  "invalid-date",
				Branch: interactor.SonarQubeWebhookBranch{
					Name: "develop",
					Type: "BRANCH",
				},
			},
			wantErr: true,
		},
		{
			name: "webhook with duplicate task ID",
			input: interactor.SonarQubeWebhook{
				ServerURL: "https://sonarqube.example.com",
				TaskID:    webhookSuccess.TaskID,
				Branch: interactor.SonarQubeWebhookBranch{
					Name: "develop",
					Type: "BRANCH",
				},
			},
			wantErr: true,
		},
		{
			name: "webhook with invalid branch type",
			input: interactor.SonarQubeWebhook{
				ServerURL: "https://sonarqube.example.com",
				TaskID:    "AZYbjyjomwfuibSArRz1",
				Branch: interactor.SonarQubeWebhookBranch{
					Name: "develop",
					Type: "PULL_REQUEST",
				},
			},
			wantErr: true,
		},
		{
			name: "webhook with invalid branch name",
			input: interactor.SonarQubeWebhook{
				ServerURL: "https://sonarqube.example.com",
				TaskID:    "AZYbjyjomwfuibSArRz2",
				Branch: interactor.SonarQubeWebhookBranch{
					Name: "invalid-name",
					Type: "BRANCH",
				},
			},
			wantErr: true,
		},
	}
}

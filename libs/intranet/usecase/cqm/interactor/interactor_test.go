package interactor_test

import (
	"context"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"

	"sa-intranet/core"
	testhelpers "sa-intranet/testing"
	"sa-intranet/usecase/cqm/interactor"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/google/uuid"
	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	ctx := context.Background()
	injector = do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx), // Apply migrations, needs to be after WithPostgres
		testhelpers.WithFixtures(ctx),   // Load fixtures, needs to be after WithMigrations
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	// Run all tests
	m.Run()
}

func setupSonarQubeMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("Request path: %s", r.URL.Path)
		jsonDefaultSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/sonarqube/issues-default-success-response.json")
		if err != nil {
			panic(err)
		}
		switch r.URL.Path {
		case "/api/issues/search":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write(jsonDefaultSuccess)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func setupJiraMockServer() *httptest.Server {
	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("Request path: %s", r.URL.Path)
		jsonDefaultProjectSuccess, err := testhelpers.FixtureFS.ReadFile("testdata/jira/project-default-success-response.json")
		if err != nil {
			panic(err)
		}
		switch r.URL.Path {
		case "/rest/api/3/project/TEST":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write(jsonDefaultProjectSuccess)
		case "/rest/api/3/issue":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusCreated)
			w.Write([]byte(`{}`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
}

func TestSonarIssuesInteractor(t *testing.T) {
	t.Parallel()

	companyClientRepo := do.MustInvoke[repository.CompanyClientRepository](injector)

	jiraRepo := do.MustInvoke[repository.JiraProjectRepository](injector)

	sonarProjectRepo := do.MustInvoke[repository.SonarqubeProjectRepository](injector)

	cipher := do.MustInvoke[core.Cypher](injector)

	t.Run("CreateJiraTicket", func(t *testing.T) {
		t.Parallel()

		mockSonarServer := setupSonarQubeMockServer()
		defer mockSonarServer.Close()

		do.Override(injector, func(i *do.Injector) (sonarqube.ClientConfig, error) {
			config := sonarqube.ClientConfig{
				URL:   mockSonarServer.URL,
				Token: "test-token",
			}

			return config, nil
		})

		usecase := do.MustInvoke[*interactor.SonarIssuesInteractor](injector)

		mockJiraServer := setupJiraMockServer()
		defer mockJiraServer.Close()

		companyClient := model.CompanyClient{
			Name: "Test Company",
		}

		_, err := companyClientRepo.Save(context.TODO(), &companyClient)
		if err != nil {
			t.Fatalf("Failed to save company client: %v", err)
		}

		encrypedToken, err := cipher.Encrypt("W7JC59uDmQVgwecHIB-gQrk-mG4xJ3bycbZi2SOcFH0")
		if err != nil {
			t.Fatalf("Failed to encrypt token: %v", err)
		}

		jiraProject := model.JiraProject{
			Name:            "Test Jira Project",
			ProjectKey:      "TEST",
			CompanyClientID: companyClient.ID,
			JiraURL:         mockJiraServer.URL,
			Username:        "<EMAIL>",
			Token:           encrypedToken,
		}

		_, err = jiraRepo.Save(context.TODO(), &jiraProject)
		if err != nil {
			t.Fatalf("Failed to save Jira project: %v", err)
		}

		projectUUID, err := uuid.Parse("0195dda6-0db4-74a6-8d38-4464b32a8d85")
		if err != nil {
			t.Fatalf("Failed to parse project UUID: %v", err)
		}

		project := model.SonarqubeProject{
			ID:            projectUUID,
			ProjectKey:    "test_project",
			ProjectName:   "Test Project",
			JiraProjectID: jiraProject.ID,
		}

		_, err = sonarProjectRepo.Save(context.TODO(), &project, true)
		if err != nil {
			t.Fatalf("Failed to save SonarQube project: %v", err)
		}

		_, err = usecase.CreateJiraIssue("0195dda6-0db4-74a6-8d38-4464b32a8d85", "AZUqtlaa7J7LX6RjQIpc")
		if err != nil {
			t.Fatalf("CreateJiraTicket failed: %v", err)
		}

		t.Log("CreateJiraTicket passed")
	})
}

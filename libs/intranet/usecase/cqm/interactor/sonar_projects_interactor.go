package interactor

import (
	"context"
	"errors"
	"fmt"
	"time"

	corerepository "sa-intranet/core/repository"
	"sa-intranet/core/validatorext"
	"sa-intranet/usecase/cqm/model"
	"sa-intranet/usecase/cqm/repository"
	"sa-intranet/usecase/cqm/sonarqube"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type SonarProjectsData struct {
	SonarProjects []SonarProject `json:"sonarProjects"`
	Pagination    Pagination     `json:"pagination"`
}

type SonarProject struct {
	ID          uuid.UUID   `json:"id"`
	ProjectName string      `json:"projectName"`
	Active      bool        `json:"active"`
	JiraProject JiraProject `json:"jiraProject"`
	ProjectKey  string      `json:"projectKey"`
	Branch      string      `json:"branch"`
	CreatedAt   time.Time   `json:"createdAt"`
	UpdatedAt   time.Time   `json:"updatedAt"`
}

type SonarProjectsInteractor struct {
	sonarProjectRepo repository.SonarqubeProjectRepository
	validator        *validator.Validate
	sonarqube        *sonarqube.Client
	customValidator  *validatorext.CustomValidator
}

type SonarProjectValidator struct {
	ProjectKey    string    `json:"projectKey" validate:"required"`
	JiraProjectID uuid.UUID `json:"jiraProjectId" validate:"required,jira_project_exists"`
	Branch        string    `json:"branch" validate:"required"`
	Active        *bool     `json:"active" validate:"required"`
}

func NewSonarProjectsInteractor(i *do.Injector) (*SonarProjectsInteractor, error) {
	sonarqubeClient, err := do.Invoke[*sonarqube.Client](i)
	if err != nil {
		return nil, err
	}

	sonarProjectRepo, err := do.Invoke[repository.SonarqubeProjectRepository](i)
	if err != nil {
		return nil, err
	}

	validator, err := do.Invoke[*validator.Validate](i)
	if err != nil {
		return nil, err
	}

	customValidator := do.MustInvoke[*validatorext.CustomValidator](i)

	return &SonarProjectsInteractor{
		sonarqube:        sonarqubeClient,
		sonarProjectRepo: sonarProjectRepo,
		validator:        validator,
		customValidator:  customValidator,
	}, nil
}

func (i *SonarProjectsInteractor) InitialData(filter string, page int, pageSize int) (SonarProjectsData, error) {
	var data SonarProjectsData
	ctx := context.Background()

	params := corerepository.PaginationParams[repository.SonarqubeProjectFilter]{
		Filters: repository.SonarqubeProjectFilter{
			Name: filter,
		},
		Page:     page,
		PageSize: pageSize,
	}

	sonarProjectsResp, err := i.sonarProjectRepo.List(ctx, params)
	if err != nil {
		return data, err
	}

	sonarProjects := make([]SonarProject, 0, len(sonarProjectsResp.Items))

	for _, project := range sonarProjectsResp.Items {
		var companyClient CompanyClient
		if project.JiraProject != nil && project.JiraProject.CompanyClient != nil {
			companyClient = CompanyClient{
				ID:   project.JiraProject.CompanyClient.ID,
				Name: project.JiraProject.CompanyClient.Name,
			}
		}

		jiraProject := JiraProject{
			ID:            project.JiraProject.ID,
			Name:          project.JiraProject.Name,
			JiraURL:       project.JiraProject.JiraURL,
			Active:        project.JiraProject.Active,
			Username:      project.JiraProject.Username,
			ProjectKey:    project.JiraProject.ProjectKey,
			CompanyClient: companyClient,
		}

		sonarProjects = append(sonarProjects, SonarProject{
			ID:          project.ID,
			ProjectName: project.ProjectName,
			ProjectKey:  project.ProjectKey,
			Active:      project.Active,
			Branch:      project.Branch,
			CreatedAt:   project.CreatedAt,
			UpdatedAt:   project.UpdatedAt,
			JiraProject: jiraProject,
		})
	}

	pagination := Pagination{
		TotalCount:  int(sonarProjectsResp.TotalItems),
		CurrentPage: sonarProjectsResp.Page,
		PerPage:     sonarProjectsResp.PageSize,
	}

	return SonarProjectsData{
		SonarProjects: sonarProjects,
		Pagination:    pagination,
	}, nil
}

func (i *SonarProjectsInteractor) CreateSonarProject(jiraProject SonarProjectValidator) (SonarProject, map[string]any, error) {
	ctx := context.Background()
	sonarProjectResp := SonarProject{}

	validationErrors, err := i.validateSonarProject(jiraProject)
	if err != nil {
		return sonarProjectResp, validationErrors, err
	}

	projectResp, err := i.sonarqube.GetProject(jiraProject.ProjectKey)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	project := i.buildSonarProjectModel(jiraProject, projectResp.Name)

	sonarProjectFromDB, err := i.saveAndRetrieveProject(ctx, project)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.buildSonarProjectResponse(sonarProjectFromDB), nil, nil
}

func (i *SonarProjectsInteractor) buildSonarProjectModel(validator SonarProjectValidator, projectName string) *model.SonarqubeProject {
	return &model.SonarqubeProject{
		ProjectKey:    validator.ProjectKey,
		Branch:        validator.Branch,
		ProjectName:   projectName,
		JiraProjectID: validator.JiraProjectID,
		Active:        *validator.Active,
	}
}

func (i *SonarProjectsInteractor) saveAndRetrieveProject(ctx context.Context, project *model.SonarqubeProject) (*model.SonarqubeProject, error) {
	sonarProjectFromDB, saveErr := i.sonarProjectRepo.Save(ctx, project, project.ID == uuid.Nil)

	if saveErr != nil {
		return nil, saveErr
	}

	return i.sonarProjectRepo.Find(ctx, sonarProjectFromDB.ID)
}

func (i *SonarProjectsInteractor) UpdateSonarProject(projectID string, sonarProject SonarProjectValidator) (SonarProject, map[string]any, error) {
	ctx := context.Background()
	sonarProjectResp := SonarProject{}

	projectUUID, err := uuid.Parse(projectID)
	if err != nil {
		return sonarProjectResp, nil, fmt.Errorf("failed to parse project ID: %w", err)
	}

	sonarProjectFromDB, err := i.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	validationErrors, err := i.validateSonarProject(sonarProject)
	if err != nil {
		return sonarProjectResp, validationErrors, err
	}

	updatedProject, err := i.buildUpdatedProject(projectUUID, sonarProject, sonarProjectFromDB)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.saveAndReturnUpdatedProject(ctx, updatedProject, projectUUID)
}

func (i *SonarProjectsInteractor) validateSonarProject(sonarProject SonarProjectValidator) (map[string]any, error) {
	err := i.validator.Struct(sonarProject)
	if err != nil {
		var validationErr validator.ValidationErrors
		ok := errors.As(err, &validationErr)

		if !ok {
			return nil, fmt.Errorf("failed to validate sonar project: %w", err)
		}

		validationErrors := validatorext.FormatFormErrors(sonarProject, validationErr)

		return validationErrors, ErrValidationFailed
	}

	return nil, nil
}

func (i *SonarProjectsInteractor) buildUpdatedProject(
	projectUUID uuid.UUID,
	sonarProject SonarProjectValidator,
	existingProject *model.SonarqubeProject,
) (*model.SonarqubeProject, error) {
	project := &model.SonarqubeProject{
		ID:            projectUUID,
		Branch:        sonarProject.Branch,
		ProjectName:   existingProject.ProjectName,
		JiraProjectID: sonarProject.JiraProjectID,
		Active:        *sonarProject.Active,
		ProjectKey:    sonarProject.ProjectKey,
		CreatedAt:     existingProject.CreatedAt,
		UpdatedAt:     existingProject.UpdatedAt,
	}

	if sonarProject.ProjectKey != existingProject.ProjectKey {
		projectResponse, err := i.sonarqube.GetProject(sonarProject.ProjectKey)
		if err != nil {
			return nil, err
		}

		project.ProjectName = projectResponse.Name
	}

	return project, nil
}

func (i *SonarProjectsInteractor) saveAndReturnUpdatedProject(
	ctx context.Context,
	project *model.SonarqubeProject,
	projectUUID uuid.UUID,
) (SonarProject, map[string]any, error) {
	sonarProjectResp := SonarProject{}
	_, saveErr := i.sonarProjectRepo.Save(ctx, project, false)

	if saveErr != nil {
		return sonarProjectResp, nil, saveErr
	}

	sonarProjectFromDB, err := i.sonarProjectRepo.Find(ctx, projectUUID)
	if err != nil {
		return sonarProjectResp, nil, err
	}

	return i.buildSonarProjectResponse(sonarProjectFromDB), nil, nil
}

func (i *SonarProjectsInteractor) buildSonarProjectResponse(sonarProjectFromDB *model.SonarqubeProject) SonarProject {
	var companyClient CompanyClient
	if sonarProjectFromDB.JiraProject != nil && sonarProjectFromDB.JiraProject.CompanyClient != nil {
		companyClient = CompanyClient{
			ID:   sonarProjectFromDB.JiraProject.CompanyClient.ID,
			Name: sonarProjectFromDB.JiraProject.CompanyClient.Name,
		}
	}

	return SonarProject{
		ID:          sonarProjectFromDB.ID,
		ProjectName: sonarProjectFromDB.ProjectName,
		ProjectKey:  sonarProjectFromDB.ProjectKey,
		JiraProject: JiraProject{
			ID:            sonarProjectFromDB.JiraProject.ID,
			Active:        sonarProjectFromDB.JiraProject.Active,
			Name:          sonarProjectFromDB.JiraProject.Name,
			JiraURL:       sonarProjectFromDB.JiraProject.JiraURL,
			ProjectKey:    sonarProjectFromDB.JiraProject.ProjectKey,
			Username:      sonarProjectFromDB.JiraProject.Username,
			CompanyClient: companyClient,
		},
		Active:    sonarProjectFromDB.Active,
		Branch:    sonarProjectFromDB.Branch,
		CreatedAt: sonarProjectFromDB.CreatedAt,
		UpdatedAt: sonarProjectFromDB.UpdatedAt,
	}
}

package model

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

// JiraProject represents a project configuration in Jira for issue tracking.
// It stores connection details and project metadata.
// https://confluence.atlassian.com/adminjiraserver073/configuring-jira-application-options-861253962.html#ConfiguringJIRAOptions-Options
type JiraProject struct {
	bun.BaseModel `bun:"table:jira_projects,alias:jp"`

	ID              uuid.UUID              `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	JiraURL         string                 `bun:"jira_url,notnull,unique:unique_jira_config"`
	Username        string                 `bun:"username,notnull"`
	Token           string                 `bun:"token,notnull"`
	Name            string                 `bun:"name,notnull"`
	ProjectID       string                 `bun:"project_id,notnull"`
	ProjectKey      string                 `bun:"project_key,notnull,unique:unique_jira_config"`
	Active          bool                   `bun:"active,notnull,default:true"`
	CompanyClientID uuid.UUID              `bun:"company_client_id,type:uuid"`
	CompanyClient   *CompanyClient         `bun:"rel:belongs-to,join:company_client_id=id"`
	Meta            map[string]interface{} `bun:"meta,type:jsonb"`

	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

var _ bun.BeforeAppendModelHook = (*JiraProject)(nil)

func (u *JiraProject) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.UpdateQuery:
		u.UpdatedAt = time.Now()
	default:
	}

	return nil
}

// SonarqubeProject represents a project in SonarQube for code quality analysis.
// It is linked to a Jira project for issue tracking.
// https://community.sonarsource.com/t/key-length-causes-error-in-project-analysis-maximum-authorized-400/62878
type SonarqubeProject struct {
	bun.BaseModel `bun:"table:sonarqube_projects,alias:sp"`

	ID            uuid.UUID    `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	ProjectName   string       `bun:"project_name,notnull"`
	ProjectKey    string       `bun:"project_key,notnull"`
	Branch        string       `bun:"branch,notnull"`
	Active        bool         `bun:"active,notnull,default:true"`
	JiraProjectID uuid.UUID    `bun:"jira_project_id,type:uuid,notnull"`
	JiraProject   *JiraProject `bun:"rel:belongs-to,join:jira_project_id=id"`

	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

var _ bun.BeforeAppendModelHook = (*SonarqubeProject)(nil)

func (u *SonarqubeProject) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.UpdateQuery:
		u.UpdatedAt = time.Now()
	default:
	}

	return nil
}

type SonarqubeIssue struct {
	bun.BaseModel `bun:"table:sonarqube_issues,alias:si"`

	ID                 uuid.UUID         `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	JiraIssueID        string            `bun:"jira_issue_id"`
	JiraIssueKey       string            `bun:"jira_issue_key"`
	IssueID            string            `bun:"issue_id,notnull"`
	SonarqubeProjectID uuid.UUID         `bun:"sonarqube_project_id,type:uuid,notnull"`
	SonarqubeProject   *SonarqubeProject `bun:"rel:belongs-to,join:sonarqube_project_id=id"`
	CreatedAt          time.Time         `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt          time.Time         `bun:",nullzero,notnull,default:current_timestamp"`
}

var _ bun.BeforeAppendModelHook = (*SonarqubeIssue)(nil)

func (u *SonarqubeIssue) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.UpdateQuery:
		u.UpdatedAt = time.Now()
	default:
	}

	return nil
}

type SonarAnalysisStatus string

var ErrInvalidSonarAnalysisStatus = errors.New("invalid sonar analysis status")

const (
	SonarAnalysisProcessing SonarAnalysisStatus = "processing"
	SonarAnalysisSucceeded  SonarAnalysisStatus = "succeeded"
	SonarAnalysisFailed     SonarAnalysisStatus = "failed"
)

func (s SonarAnalysisStatus) IsValid() bool {
	switch s {
	case SonarAnalysisProcessing,
		SonarAnalysisSucceeded,
		SonarAnalysisFailed:
		return true
	}

	return false
}

func (s SonarAnalysisStatus) String() string {
	return string(s)
}

func (s SonarAnalysisStatus) Validate() error {
	if !s.IsValid() {
		return ErrInvalidSonarAnalysisStatus
	}

	return nil
}

type SonarqubeAnalysis struct {
	bun.BaseModel `bun:"table:sonarqube_analyses,alias:sa"`

	ID                 uuid.UUID              `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	SonarqubeProjectID uuid.UUID              `bun:"sonarqube_project_id,type:uuid,notnull"`
	SonarqubeProject   *SonarqubeProject      `bun:"rel:belongs-to,join:sonarqube_project_id=id"`
	AnalysisID         string                 `bun:"analysis_id,notnull"`
	WebhookPayload     map[string]interface{} `bun:"webhook_payload,type:jsonb,notnull,default:'{}'::jsonb"`
	Measures           map[string]interface{} `bun:"measures,type:jsonb,default:'{}'::jsonb"`
	Score              float64                `bun:"score,type:numeric(5,2)"`
	Status             SonarAnalysisStatus    `bun:"status"`
	AnalysedAt         time.Time              `bun:"analysed_at,type:timestamptz"`
	CreatedAt          time.Time              `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt          time.Time              `bun:",nullzero,notnull,default:current_timestamp"`
	CompletedAt        time.Time              `bun:"completed_at,nullzero,type:timestamptz"`
	QualityGateName    string                 `bun:"quality_gate_name"`
	QualityGateStatus  string                 `bun:"quality_gate_status"`
	ErrorDetails       map[string]interface{} `bun:"error_details,type:jsonb,default:'{}'::jsonb"`
	IssuesTotal        int                    `bun:"issues_total"`
	IssuesEffortTotal  int                    `bun:"issues_effort_total"`
}

var _ bun.BeforeAppendModelHook = (*SonarqubeAnalysis)(nil)

func (u *SonarqubeAnalysis) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.UpdateQuery:
		u.UpdatedAt = time.Now()
	default:
	}

	return nil
}

type CompanyClient struct {
	bun.BaseModel `bun:"table:company_clients,alias:cc"`

	ID        uuid.UUID `bun:"id,pk,type:uuid,default:uuid_generate_v4()"`
	Name      string    `bun:"name,notnull"`
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

var _ bun.BeforeAppendModelHook = (*CompanyClient)(nil)

func (u *CompanyClient) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.UpdateQuery:
		u.UpdatedAt = time.Now()
	default:
	}

	return nil
}

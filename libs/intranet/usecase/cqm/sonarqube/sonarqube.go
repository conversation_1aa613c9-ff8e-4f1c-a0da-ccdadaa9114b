package sonarqube

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/samber/do"
)

// SonarQubeTime is a custom time type that can handle SonarQube's time format
type SonarQubeTime struct {
	time.Time
}

// UnmarshalJSON implements the json.Unmarshaler interface
func (ct *SonarQubeTime) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" {
		ct.Time = time.Time{}
		return nil
	}

	// Try parsing with different formats
	formats := []string{
		"2006-01-02T15:04:05-0700",
		"2006-01-02T15:04:05+0000",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02T15:04:05+0000", // This format already handles your case

	}

	var err error

	for _, format := range formats {
		t, parseErr := time.Parse(format, s)
		if parseErr == nil {
			ct.Time = t
			return nil
		}

		err = parseErr
	}

	return fmt.Errorf("could not parse time %q: %w", s, err)
}

type ClientConfig struct {
	URL   string `mapstructure:"APP_SONARQUBE_URL"`
	Token string `mapstructure:"APP_SONARQUBE_TOKEN"`
}

type Client struct {
	config ClientConfig
	http   *http.Client
}

// Flow represents a flow in a SonarQube issue
type Flow struct {
	Locations []Location `json:"locations,omitempty"`
}

// Location represents a code location in a flow
type Location struct {
	Component string    `json:"component,omitempty"`
	TextRange TextRange `json:"textRange,omitempty"`
	Message   string    `json:"message,omitempty"`
}

// MessageFormatting represents formatting information for messages
type MessageFormatting struct {
	Start int    `json:"start"`
	End   int    `json:"end"`
	Type  string `json:"type"` // Could be "CODE", "STRONG", etc.
}

// Issue represents a SonarQube issue
type Issue struct {
	Key                string              `json:"key"`
	Rule               string              `json:"rule"`
	Severity           string              `json:"severity"`
	Component          string              `json:"component"`
	Project            string              `json:"project"`
	Line               int                 `json:"line,omitempty"`
	Hash               string              `json:"hash,omitempty"`
	Status             string              `json:"status"`
	Message            string              `json:"message"`
	Type               string              `json:"type"`
	CreationDate       SonarQubeTime       `json:"creationDate"`
	UpdateDate         SonarQubeTime       `json:"updateDate"`
	Tags               []string            `json:"tags"`
	Author             string              `json:"author,omitempty"`
	Effort             string              `json:"effort,omitempty"`
	Debt               string              `json:"debt,omitempty"`
	Resolution         string              `json:"resolution,omitempty"`
	CloseDate          SonarQubeTime       `json:"closeDate,omitempty"`
	TextRange          TextRange           `json:"textRange,omitempty"`
	Flows              []Flow              `json:"flows"`
	Scope              string              `json:"scope,omitempty"`
	QuickFixAvailable  bool                `json:"quickFixAvailable,omitempty"`
	MessageFormattings []MessageFormatting `json:"messageFormattings"`
}

type Component struct {
	Key       string `json:"key"`
	Name      string `json:"name"`
	Enabled   bool   `json:"enabled,omitempty"`
	Qualifier string `json:"qualifier,omitempty"`
	LongName  string `json:"longName,omitempty"`
	Path      string `json:"path,omitempty"`
}

type TextRange struct {
	StartLine   int `json:"startLine"`
	EndLine     int `json:"endLine"`
	StartOffset int `json:"startOffset"`
	EndOffset   int `json:"endOffset"`
}

type IssuesResponse struct {
	Total       int           `json:"total"`
	P           int           `json:"p"`
	Ps          int           `json:"ps"`
	EffortTotal int           `json:"effortTotal"`
	Issues      []Issue       `json:"issues"`
	Components  []Component   `json:"components"`
	Facets      []interface{} `json:"facets"`
	Paging      struct {
		PageIndex int `json:"pageIndex"`
		PageSize  int `json:"pageSize"`
		Total     int `json:"total"`
	} `json:"paging"`
}

type ProjectResponse struct {
	Components []Component `json:"components"`
}

// IssueFilter represents filtering options for SonarQube issues
type IssueFilter struct {
	Severities    []string // Possible values: INFO, MINOR, MAJOR, CRITICAL, BLOCKER
	Types         []string // Possible values: CODE_SMELL, BUG, VULNERABILITY, SECURITY_HOTSPOT
	Statuses      []string // Possible values: OPEN, CONFIRMED, REOPENED, RESOLVED, CLOSED
	Resolutions   []string // Possible values: FALSE-POSITIVE, WONTFIX, FIXED, REMOVED
	CreatedAfter  string   // Format: YYYY-MM-DD
	CreatedBefore string   // Format: YYYY-MM-DD
	Tags          []string
	Authors       []string
	Assignees     []string
	Languages     []string
	Rules         []string
	Issues        []string
}

// PaginationOptions represents pagination options for API requests
type PaginationOptions struct {
	PageIndex int // 1-based page index
	PageSize  int // Number of items per page (default: 100, max: 500)
}

func New(i *do.Injector) (*Client, error) {
	config, err := do.Invoke[ClientConfig](i)
	if err != nil {
		return nil, err
	}

	client := &Client{
		config: config,
		http:   &http.Client{Timeout: 30 * time.Second},
	}

	return client, nil
}

// GetProjectIssues retrieves issues for a specific project from SonarQube with pagination and filtering
func (c *Client) GetProjectIssues(projectKey string, filter *IssueFilter, pagination *PaginationOptions) (IssuesResponse, error) {
	baseURL, err := c.buildBaseURL("/api/issues/search")
	if err != nil {
		return IssuesResponse{}, fmt.Errorf("invalid SonarQube URL: %w", err)
	}

	params := c.buildQueryParams(projectKey, filter, pagination)
	baseURL.RawQuery = params.Encode()

	response, err := c.makeRequest(baseURL.String())
	if err != nil {
		return IssuesResponse{}, err
	}

	return response, nil
}

func (c *Client) buildBaseURL(path string) (*url.URL, error) {
	baseURL, err := url.Parse(c.config.URL)
	if err != nil {
		return nil, err
	}

	baseURL.Path = path

	return baseURL, nil
}

func (c *Client) buildQueryParams(projectKey string, filter *IssueFilter, pagination *PaginationOptions) url.Values {
	params := url.Values{}
	params.Add("componentKeys", projectKey)

	c.addPaginationParams(&params, pagination)
	c.addSortingParams(&params)

	if filter != nil {
		c.addFilterParams(&params, filter)
	}

	return params
}

func (c *Client) addPaginationParams(params *url.Values, pagination *PaginationOptions) {
	pageSize := 25
	pageIndex := 1

	if pagination != nil {
		if pagination.PageSize > 0 {
			pageSize = min(pagination.PageSize, 500)
		}

		if pagination.PageIndex > 0 {
			pageIndex = pagination.PageIndex
		}
	}

	params.Add("ps", fmt.Sprintf("%d", pageSize))
	params.Add("p", fmt.Sprintf("%d", pageIndex))
}

func (c *Client) addSortingParams(params *url.Values) {
	params.Add("s", "SEVERITY")
	params.Add("asc", "false")
}

func (c *Client) addFilterParams(params *url.Values, filter *IssueFilter) {
	if len(filter.Severities) > 0 {
		params.Add("severities", joinStrings(filter.Severities))
	}

	if len(filter.Types) > 0 {
		params.Add("types", joinStrings(filter.Types))
	}

	if len(filter.Statuses) > 0 {
		params.Add("statuses", joinStrings(filter.Statuses))
	}

	if len(filter.Resolutions) > 0 {
		params.Add("resolutions", joinStrings(filter.Resolutions))
	}

	if filter.CreatedAfter != "" {
		params.Add("createdAfter", filter.CreatedAfter)
	}

	if filter.CreatedBefore != "" {
		params.Add("createdBefore", filter.CreatedBefore)
	}

	if len(filter.Tags) > 0 {
		params.Add("tags", joinStrings(filter.Tags))
	}

	if len(filter.Authors) > 0 {
		params.Add("authors", joinStrings(filter.Authors))
	}

	if len(filter.Assignees) > 0 {
		params.Add("assignees", joinStrings(filter.Assignees))
	}

	if len(filter.Languages) > 0 {
		params.Add("languages", joinStrings(filter.Languages))
	}

	if len(filter.Rules) > 0 {
		params.Add("rules", joinStrings(filter.Rules))
	}

	if len(filter.Issues) > 0 {
		params.Add("issues", joinStrings(filter.Issues))
	}
}

func (c *Client) makeRequest(url string) (IssuesResponse, error) {
	req, err := http.NewRequestWithContext(context.TODO(), "GET", url, nil)
	if err != nil {
		return IssuesResponse{}, fmt.Errorf("creating request: %w", err)
	}

	req.SetBasicAuth(c.config.Token, "")

	resp, err := c.http.Do(req)
	if err != nil {
		return IssuesResponse{}, fmt.Errorf(ErrorWrapFormat, ErrMakingRequestToSonarQube, err)
	}

	defer func() {
		if bodyErr := resp.Body.Close(); bodyErr != nil {
			slog.Error(ErrFailedToCloseRespBody.Error(), "error", bodyErr)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return IssuesResponse{}, fmt.Errorf("status %d: %s: %w", resp.StatusCode, string(body), ErrSonarQubeAPIStatus)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return IssuesResponse{}, fmt.Errorf(ErrorWrapFormat, ErrReadingResponseBody, err)
	}

	var response IssuesResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return IssuesResponse{}, fmt.Errorf(ErrorWrapFormat, ErrUnmarshalingResponse, err)
	}

	return response, nil
}

// GetAllProjectIssues retrieves all issues for a project by handling pagination automatically
func (c *Client) GetAllProjectIssues(projectKey string, filter *IssueFilter) ([]Issue, error) {
	var allIssues []Issue
	pageIndex := 1
	pageSize := 500 // Maximum allowed by SonarQube API

	for {
		pagination := &PaginationOptions{
			PageIndex: pageIndex,
			PageSize:  pageSize,
		}

		issuesResp, err := c.GetProjectIssues(projectKey, filter, pagination)
		if err != nil {
			return nil, err
		}

		issues := issuesResp.Issues

		allIssues = append(allIssues, issues...)

		// Check if we've retrieved all pages
		if len(allIssues) >= issuesResp.Paging.Total {
			break
		}

		pageIndex++
	}

	return allIssues, nil
}

// GetProject retrieves all the projects that match with key and returns the first one
func (c *Client) GetProject(projectKey string) (Component, error) {
	baseURL, err := url.Parse(c.config.URL)
	var sonarProject Component

	if err != nil {
		return sonarProject, fmt.Errorf("invalid SonarQube URL: %w", err)
	}

	// Set up the API path
	baseURL.Path = "/api/components/search"

	// Add query parameters
	queryParams := url.Values{}
	queryParams.Set("q", projectKey)
	queryParams.Set("qualifiers", "TRK")
	baseURL.RawQuery = queryParams.Encode()

	req, err := http.NewRequestWithContext(context.TODO(), "GET", baseURL.String(), nil)
	if err != nil {
		return sonarProject, fmt.Errorf("error creating request: %w", err)
	}

	// Add authentication
	req.SetBasicAuth(c.config.Token, "")

	resp, err := c.http.Do(req)
	if err != nil {
		return sonarProject, fmt.Errorf(ErrorWrapFormat, ErrMakingRequestToSonarQube, err)
	}

	defer func() {
		if bodyErr := resp.Body.Close(); bodyErr != nil {
			slog.Error(ErrFailedToCloseRespBody.Error(), "error", bodyErr)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return sonarProject, fmt.Errorf("%w: status %d: %s", ErrSonarQubeAPIStatus, resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return sonarProject, fmt.Errorf(ErrorWrapFormat, ErrReadingResponseBody, err)
	}

	var response ProjectResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return sonarProject, fmt.Errorf(ErrorWrapFormat, ErrUnmarshalingResponse, err)
	}

	// Return the first project if found
	if len(response.Components) > 0 {
		return response.Components[0], nil
	}

	return sonarProject, fmt.Errorf("%w: %s", ErrProjectNotFound, projectKey)
}

// Helper function to join string slices with commas
func joinStrings(strs []string) string {
	if len(strs) == 0 {
		return ""
	}

	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += "," + strs[i]
	}

	return result
}

// Define static errors
var (
	ErrSonarQubeAPI             = errors.New("SonarQube API error")
	ErrProjectNotFound          = errors.New("no project found with key")
	ErrMakingRequestToSonarQube = errors.New("error making request to SonarQube")
	ErrFailedToCloseRespBody    = errors.New("failed to close response body")
	ErrUnmarshalingResponse     = errors.New("error unmarshaling response")
	ErrReadingResponseBody      = errors.New("error reading response body")
	ErrSonarQubeAPIStatus       = errors.New("SonarQube API status error")
)

const (
	// ErrorWrapFormat is the standard format for wrapping errors
	ErrorWrapFormat = "%w: %w"
)

func doRequest[T any](c *Client, method, url string, body io.Reader) (T, error) {
	var response T

	req, err := http.NewRequestWithContext(context.TODO(), method, url, body)
	if err != nil {
		return response, fmt.Errorf("failed to create request to SonarQube: %w", err)
	}

	req.SetBasicAuth(c.config.Token, "")

	resp, err := c.http.Do(req)
	if err != nil {
		return response, fmt.Errorf(ErrorWrapFormat, ErrMakingRequestToSonarQube, err)
	}

	defer func() {
		if bodyErr := resp.Body.Close(); bodyErr != nil {
			slog.Error(ErrFailedToCloseRespBody.Error(), "error", bodyErr)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return response, fmt.Errorf("status %d: %s: %w", resp.StatusCode, string(bodyBytes), ErrSonarQubeAPIStatus)
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return response, fmt.Errorf(ErrorWrapFormat, ErrReadingResponseBody, err)
	}

	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		return response, fmt.Errorf(ErrorWrapFormat, ErrUnmarshalingResponse, err)
	}

	return response, nil
}

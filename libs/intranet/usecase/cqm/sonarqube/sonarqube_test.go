package sonarqube_test

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	intranetConfig "sa-intranet/config"
	"sa-intranet/usecase/cqm/sonarqube"

	intranet "sa-intranet"

	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	// Set up logging for tests
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	slog.SetDefault(logger)

	injector = do.New()

	conf, err := intranetConfig.NewConfig()
	if err != nil {
		panic(err)
	}

	if err = intranet.Register(injector, *conf); err != nil {
		panic(err)
	}

	// Run all tests
	m.Run()
}

func TestClient_GetProjectIssues(t *testing.T) {
	t.Parallel()

	ts := setupTestServer(t)
	defer ts.Close()

	client := setupTestClient(ts.URL)
	response := executeTest(t, client)
	verifyResponse(t, response)
}

func setupTestServer(t *testing.T) *httptest.Server {
	t.Helper()

	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		verifyRequest(t, r)

		mockResponse := createMockResponse()

		w.Header().Set("Content-Type", "application/json")

		err := json.NewEncoder(w).Encode(mockResponse)
		if err != nil {
			t.Fatalf("Failed to encode response: %v", err)
		}
	}))
}

func verifyRequest(t *testing.T, r *http.Request) {
	t.Helper()

	if r.URL.Path != "/api/issues/search" {
		t.Errorf("Expected request to '/api/issues/search', got '%s'", r.URL.Path)
	}

	if r.Method != "GET" {
		t.Errorf("Expected GET request, got %s", r.Method)
	}

	query := r.URL.Query()

	verifyQueryParams(t, query)
}

func verifyQueryParams(t *testing.T, query map[string][]string) {
	t.Helper()

	projectKey := query["componentKeys"][0]
	if projectKey != "test-project" {
		t.Errorf("Expected componentKeys=test-project, got %s", projectKey)
	}

	pageSize := query["ps"][0]
	if pageSize != "50" {
		t.Errorf("Expected ps=50, got %s", pageSize)
	}

	pageIndex := query["p"][0]
	if pageIndex != "2" {
		t.Errorf("Expected p=2, got %s", pageIndex)
	}

	severities := query["severities"][0]
	if severities != "MAJOR,CRITICAL" {
		t.Errorf("Expected severities=MAJOR,CRITICAL, got %s", severities)
	}
}

func createMockResponse() sonarqube.IssuesResponse {
	return sonarqube.IssuesResponse{
		Total:       150,
		P:           2,
		Ps:          50,
		EffortTotal: 240,
		Issues: []sonarqube.Issue{
			{
				Key:       "issue-1",
				Rule:      "rule-1",
				Severity:  "MAJOR",
				Component: "test-project:src/main.go",
				Line:      42,
				Status:    "OPEN",
			},
			{
				Key:       "issue-2",
				Rule:      "rule-2",
				Severity:  "CRITICAL",
				Component: "test-project:src/utils.go",
				Line:      15,
				Status:    "OPEN",
			},
		},
		Components: []sonarqube.Component{
			{
				Key:  "test-project:src/main.go",
				Name: "main.go",
				Path: "src/main.go",
			},
			{
				Key:  "test-project:src/utils.go",
				Name: "utils.go",
				Path: "src/utils.go",
			},
		},
		Paging: struct {
			PageIndex int `json:"pageIndex"`
			PageSize  int `json:"pageSize"`
			Total     int `json:"total"`
		}{
			PageIndex: 2,
			PageSize:  50,
			Total:     150,
		},
	}
}

func setupTestClient(serverURL string) *sonarqube.Client {
	i := injector.Clone()
	do.Override(i, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		config := sonarqube.ClientConfig{
			URL:   serverURL,
			Token: "test-token",
		}

		return config, nil
	})

	return do.MustInvoke[*sonarqube.Client](i)
}

func executeTest(t *testing.T, client *sonarqube.Client) *sonarqube.IssuesResponse {
	t.Helper()

	filter := &sonarqube.IssueFilter{
		Severities: []string{"MAJOR", "CRITICAL"},
	}

	pagination := &sonarqube.PaginationOptions{
		PageIndex: 2,
		PageSize:  50,
	}

	response, err := client.GetProjectIssues("test-project", filter, pagination)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	return &response
}

func verifyResponse(t *testing.T, response *sonarqube.IssuesResponse) {
	t.Helper()

	if response.Total != 150 {
		t.Errorf("Expected total=150, got %d", response.Total)
	}

	if len(response.Issues) != 2 {
		t.Errorf("Expected 2 issues, got %d", len(response.Issues))
	}

	if response.Issues[0].Key != "issue-1" {
		t.Errorf("Expected first issue key to be 'issue-1', got '%s'", response.Issues[0].Key)
	}

	if response.Issues[1].Severity != "CRITICAL" {
		t.Errorf("Expected second issue severity to be 'CRITICAL', got '%s'", response.Issues[1].Severity)
	}

	if response.Paging.Total != 150 {
		t.Errorf("Expected paging total=150, got %d", response.Paging.Total)
	}
}

func setupTestGetAllServer(t *testing.T) (*httptest.Server, *int) {
	t.Helper()

	pageCount := 0
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/api/issues/search" {
			t.Errorf("Expected request to '/api/issues/search', got '%s'", r.URL.Path)
		}

		query := r.URL.Query()
		pageIndex := query.Get("p")
		pageCount++

		var mockResponse sonarqube.IssuesResponse
		totalIssues := 6
		pageSize := 2

		currentPage, _ := strconv.Atoi(pageIndex)
		startIdx := (currentPage - 1) * pageSize

		endIdx := startIdx + pageSize
		if endIdx > totalIssues {
			endIdx = totalIssues
		}

		var issues []sonarqube.Issue
		for i := startIdx; i < endIdx; i++ {
			issues = append(issues, sonarqube.Issue{
				Key:       fmt.Sprintf("issue-%d", i+1),
				Severity:  "MAJOR",
				Component: "test-project:src/file.go",
				Status:    "OPEN",
			})
		}

		mockResponse = sonarqube.IssuesResponse{
			Total:  totalIssues,
			P:      currentPage,
			Ps:     pageSize,
			Issues: issues,
			Paging: struct {
				PageIndex int `json:"pageIndex"`
				PageSize  int `json:"pageSize"`
				Total     int `json:"total"`
			}{
				PageIndex: currentPage,
				PageSize:  pageSize,
				Total:     totalIssues,
			},
		}

		w.Header().Set("Content-Type", "application/json")

		err := json.NewEncoder(w).Encode(mockResponse)
		if err != nil {
			t.Fatalf("Failed to encode response: %v", err)
		}
	}))

	return ts, &pageCount
}

func TestClient_GetAllProjectIssues(t *testing.T) {
	t.Parallel()

	ts, pageCount := setupTestGetAllServer(t)
	defer ts.Close()

	i := injector.Clone()
	do.Override(i, func(i *do.Injector) (sonarqube.ClientConfig, error) {
		config := sonarqube.ClientConfig{
			URL:   ts.URL,
			Token: "test-token",
		}

		return config, nil
	})

	client := do.MustInvoke[*sonarqube.Client](i)

	issues, err := client.GetAllProjectIssues("test-project", nil)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(issues) != 6 {
		t.Errorf("Expected 6 issues, got %d", len(issues))
	}

	if *pageCount != 3 {
		t.Errorf("Expected 3 API calls, got %d", *pageCount)
	}

	for i, issue := range issues {
		expectedKey := fmt.Sprintf("issue-%d", i+1)
		if issue.Key != expectedKey {
			t.Errorf("Expected issue key %s, got %s", expectedKey, issue.Key)
		}
	}
}

func TestSonarQubeCustomTime(t *testing.T) {
	// Test cases for different time formats
	testCases := []struct {
		name     string
		jsonTime string
		expected time.Time
		hasError bool
	}{
		{
			name:     "Format with timezone offset -0700",
			jsonTime: `"2023-01-02T15:04:05-0700"`,
			expected: time.Date(2023, 1, 2, 15, 4, 5, 0, time.FixedZone("", -7*60*60)),
			hasError: false,
		},
		{
			name:     "Format with +0000",
			jsonTime: `"2025-03-24T15:05:32+0000"`,
			expected: time.Date(2025, 3, 24, 15, 5, 32, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "Format with Z",
			jsonTime: `"2023-01-02T15:04:05Z"`,
			expected: time.Date(2023, 1, 2, 15, 4, 5, 0, time.UTC),
			hasError: false,
		},
		{
			name:     "Null value",
			jsonTime: `null`,
			expected: time.Time{},
			hasError: false,
		},
		{
			name:     "Invalid format",
			jsonTime: `"2023/01/02 15:04:05"`,
			expected: time.Time{},
			hasError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			var sqTime sonarqube.SonarQubeTime
			err := json.Unmarshal([]byte(tc.jsonTime), &sqTime)

			if tc.hasError {
				if err == nil {
					t.Errorf("Expected error but got none for %s", tc.name)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for %s: %v", tc.name, err)
				}

				if !sqTime.Time.UTC().Equal(tc.expected.UTC()) {
					t.Errorf("Expected time %v but got %v for %s",
						tc.expected.UTC(), sqTime.UTC(), tc.name)
				}
			}
		})
	}
}

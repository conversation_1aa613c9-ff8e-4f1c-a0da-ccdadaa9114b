package sonarqube

import (
	"fmt"
	"net/url"
)

var (
	ErrMissingRequiredParameter = fmt.<PERSON><PERSON><PERSON>("missing required parameter")
	ErrInvalidSonarQubeURL      = fmt.<PERSON><PERSON><PERSON>("invalid SonarQube URL")
)

const GetMeasuresEndpoint = "api/measures/component"

type GetMeasuresParams struct {
	AdditionalFields string `url:"additionalFields,omitempty"`
	Branch           string `url:"branch,omitempty"`
	Component        string `url:"component"`
	MetricKeys       string `url:"metricKeys"`
}

type GetMeasuresResponse struct {
	Component struct {
		Key       string `json:"key"`
		Name      string `json:"name"`
		Qualifier string `json:"qualifier"`
		Measures  []struct {
			Metric string `json:"metric"`
			Value  string `json:"value"`
		} `json:"measures"`
	} `json:"component"`
}

func (c *Client) GetMeasures(params GetMeasuresParams) (GetMeasuresResponse, error) {
	var response GetMeasuresResponse

	baseURL, err := url.Parse(c.config.URL)
	if err != nil {
		return response, fmt.Errorf("%w: %w", ErrInvalidSonarQubeURL, err)
	}

	// Use a different variable name to avoid shadowing
	if validationErr := c.ValidateGetMeasures(params); validationErr != nil {
		return response, validationErr
	}

	baseURL.Path = GetMeasuresEndpoint

	requestParams := url.Values{}
	requestParams.Add("component", params.Component)
	requestParams.Add("metricKeys", params.MetricKeys)
	baseURL.RawQuery = requestParams.Encode()

	response, err = doRequest[GetMeasuresResponse](c, "GET", baseURL.String(), nil)
	if err != nil {
		return response, fmt.Errorf("failed to get measures: %w", err)
	}

	return response, nil
}

func (c *Client) ValidateGetMeasures(params GetMeasuresParams) error {
	if params.Component == "" {
		return fmt.Errorf("%s: %w", "component", ErrMissingRequiredParameter)
	}

	if params.MetricKeys == "" {
		return fmt.Errorf("%s: %w", "metricKeys", ErrMissingRequiredParameter)
	}

	return nil
}

package jira

import (
	"fmt"
	"net/http"
)

var ErrJiraProjectNotFound = fmt.Errorf("project not found, please check the project key or token")

// https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-projects/#api-rest-api-3-project-projectidorkey-get

// Project represents a Jira project with selected fields
type Project struct {
	Key         string      `json:"key"`
	Name        string      `json:"name"`
	ID          string      `json:"id"`
	Description string      `json:"description"`
	IssueTypes  []IssueType `json:"issueTypes"`
}

// IssueType represents a Jira issue type
type IssueType struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Subtask     bool   `json:"subtask"`
}

// GetProject retrieves a project from Jira by its ID or key
func (c *Client) GetProject(creds Credentials, jiraURL string, projectIDOrKey string) (Project, error) {
	var resonse Project
	path := fmt.Sprintf("/rest/api/3/project/%s", projectIDOrKey)

	err := c.DefaultClient.RequestJSON(
		jiraURL,
		"GET",
		path,
		nil,
		&resonse,
		func(req *http.Request) {
			req.SetBasicAuth(creds.Username, creds.Token)
			req.Header.Set("Accept", "application/json")
			req.Header.Set("Content-Type", "application/json")
		},
	)
	if err != nil {
		return resonse, fmt.Errorf("failed to get project: %w", err)
	}

	return resonse, nil
}

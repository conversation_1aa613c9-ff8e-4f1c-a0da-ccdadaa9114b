package jira

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

// Issue represents the simplified Jira issue with only the fields we're interested in
type Issue struct {
	ID       string `json:"id"`
	Key      string `json:"key"`
	Summary  string `json:"summary"`
	Project  string `json:"project"`
	Assignee string `json:"assignee"`
}

// JiraFetchIssueResponse represents the response from Jira API
type JiraFetchIssueResponse struct {
	Expand      string      `json:"expand"`
	IssueErrors []any       `json:"issueErrors"`
	Issues      []JiraIssue `json:"issues"`
}

// JiraIssue represents the full Jira issue structure from the API
type JiraIssue struct {
	ID     string     `json:"id"`
	Key    string     `json:"key"`
	Self   string     `json:"self"`
	Fields JiraFields `json:"fields"`
}

// Jira<PERSON>ields represents the fields of a Jira issue
type JiraFields struct {
	Summary  string      `json:"summary"`
	Project  JiraProject `json:"project"`
	Assignee JiraUser    `json:"assignee"`
}

// JiraProject represents a Jira project
type JiraProject struct {
	ID   string `json:"id"`
	Key  string `json:"key"`
	Name string `json:"name"`
}

// JiraUser represents a Jira user
type JiraUser struct {
	AccountID    string `json:"accountId"`
	DisplayName  string `json:"displayName"`
	EmailAddress string `json:"emailAddress"`
}

// FetchIssues fetches issues for a specific project from Jira API
func (c *Client) FetchIssues(creds Credentials, jiraURL string, projectKey string, issueKeys []string) (JiraFetchIssueResponse, error) {
	searchResp := JiraFetchIssueResponse{}

	fields := []string{"summary", "project", "assignee"}
	// For fetching issues by project, we need to use the search API instead of bulkfetch
	path := "/rest/api/3/search"

	quoteIssueKeys := func(keys []string) []string {
		quoted := make([]string, len(keys))
		for i, key := range keys {
			quoted[i] = fmt.Sprintf("\"%s\"", key)
		}

		return quoted
	}

	// Construct JQL query to filter by project and issue keys if provided
	jql := fmt.Sprintf("project = \"%s\"", projectKey)

	if len(issueKeys) > 0 {
		// Use the IN clause for more efficient filtering
		jql += fmt.Sprintf(" AND key in (%s)", strings.Join(quoteIssueKeys(issueKeys), ", "))
	}

	// Prepare request body
	requestBody := map[string]interface{}{
		"jql":        jql,
		"maxResults": 100, // Default to 100 results, can be made configurable
		"fields":     fields,
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return searchResp, fmt.Errorf("error marshaling request body: %w", err)
	}

	err = c.DefaultClient.RequestJSON(
		jiraURL,
		"POST",
		path,
		bytes.NewBuffer(jsonBody),
		&searchResp,
		func(req *http.Request) {
			req.SetBasicAuth(creds.Username, creds.Token)
			req.Header.Set("Accept", "application/json")
			req.Header.Set("Content-Type", "application/json")
		},
	)
	if err != nil {
		return searchResp, fmt.Errorf("error making request: %w", err)
	}

	return searchResp, nil
}

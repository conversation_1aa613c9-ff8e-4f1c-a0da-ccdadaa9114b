package repository

import (
	"context"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// JiraProjectDefaultRepository is the default implementation of JiraProjectRepository
type JiraProjectDefaultRepository struct {
	*bun.DB
}

// compile time check if JiraProjectDefaultRepository implements JiraProjectRepository
var _ JiraProjectRepository = (*JiraProjectDefaultRepository)(nil)

type JiraProjectFilter struct {
	Name string `json:"name,omitempty"`
}

// NewJiraProjectDefaultRepository creates a new instance of JiraProjectDefaultRepository
func NewJiraProjectDefaultRepository(i *do.Injector) (JiraProjectRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &JiraProjectDefaultRepository{
		DB: db,
	}, nil
}

// Find retrieves a JiraProject by its ID
func (r *JiraProjectDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.JiraProject, error) {
	record, err := repository.Find[model.JiraProject](
		ctx,
		r.DB,
		id,
		repository.WithDefaultQueryBuilder(func(q *bun.SelectQuery) {
			q.Relation("CompanyClient")
		}),
		repository.WithFindIDColumnName("jp.id"),
	)

	return record, err
}

// List returns a paginated list of JiraProjects
func (r *JiraProjectDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[JiraProjectFilter],
) (*repository.PaginatedResult[model.JiraProject], error) {
	return repository.FindAll[model.JiraProject](
		ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[JiraProjectFilter]) {
				q = q.Relation("CompanyClient")
				if params.Filters.Name != "" {
					q.Where("jp.name ILIKE ?", "%"+params.Filters.Name+"%")
				}
			}))
}

// Save persists a JiraProject to the database
func (r *JiraProjectDefaultRepository) Save(ctx context.Context, project *model.JiraProject) (*model.JiraProject, error) {
	err := repository.Save(
		ctx,
		r.DB,
		project,
		repository.WithSaveNew(project.ID == uuid.Nil),
	)
	if err != nil {
		return nil, err
	}

	return project, nil
}

package repository

import (
	"context"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// CompanyClientDefaultRepository is the default implementation of CompanyClientRepository
type CompanyClientDefaultRepository struct {
	*bun.DB
}

// compile time check if CompanyClientDefaultRepository implements CompanyClientRepository
var _ CompanyClientRepository = (*CompanyClientDefaultRepository)(nil)

type CompanyClientFilter struct {
	Name string `json:"name,omitempty"`
}

// NewCompanyClientDefaultRepository creates a new instance of CompanyClientDefaultRepository
func NewCompanyClientDefaultRepository(i *do.Injector) (CompanyClientRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &CompanyClientDefaultRepository{
		DB: db,
	}, nil
}

// Find retrieves a JiraProject by its ID
func (r *CompanyClientDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.CompanyClient, error) {
	record, err := repository.Find[model.CompanyClient](
		ctx,
		r.DB,
		id,
	)

	return record, err
}

// List returns a paginated list of CompanyClients
func (r *CompanyClientDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[CompanyClientFilter],
) (*repository.PaginatedResult[model.CompanyClient], error) {
	return repository.FindAll[model.CompanyClient](
		ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[CompanyClientFilter]) {
				if params.Filters.Name != "" {
					q.Where("name ILIKE ?", "%"+params.Filters.Name+"%")
				}
			}))
}

// Save persists a CompanyClient to the database
func (r *CompanyClientDefaultRepository) Save(ctx context.Context, companyClient *model.CompanyClient) (*model.CompanyClient, error) {
	err := repository.Save(
		ctx,
		r.DB,
		companyClient,
		repository.WithSaveNew(companyClient.ID == uuid.Nil),
	)
	if err != nil {
		return nil, err
	}

	return companyClient, nil
}

package repository

import (
	"context"
	"fmt"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/cqm/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

// SonarqubeProjectDefaultRepository is the default implementation of SonarqubeProjectRepository
type SonarqubeProjectDefaultRepository struct {
	*bun.DB
}

// compile time check if SonarqubeProjectDefaultRepository implements SonarqubeProjectRepository
var _ SonarqubeProjectRepository = (*SonarqubeProjectDefaultRepository)(nil)

type SonarqubeProjectFilter struct {
	Name       string `json:"name,omitempty"`
	ProjectKey string `json:"project_key,omitempty"`
}

// NewSonarqubeProjectDefaultRepository creates a new instance of SonarqubeProjectDefaultRepository
func NewSonarqubeProjectDefaultRepository(i *do.Injector) (SonarqubeProjectRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &SonarqubeProjectDefaultRepository{
		DB: db,
	}, nil
}

// Find retrieves a SonarqubeProject by its ID
func (r *SonarqubeProjectDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.SonarqubeProject, error) {
	project := new(model.SonarqubeProject)

	tableModel := r.DB.NewSelect().Model(project)
	tableAlias := "sp"

	err := tableModel.
		Relation("JiraProject.CompanyClient").
		Where(fmt.Sprintf("%s.id = ?", tableAlias), id).
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return project, nil
}

// FindByJiraProjectID retrieves SonarqubeProjects by JiraProjectID
func (r *SonarqubeProjectDefaultRepository) FindByJiraProjectID(ctx context.Context, jiraProjectID uuid.UUID) ([]*model.SonarqubeProject, error) {
	var projects []*model.SonarqubeProject

	err := r.DB.NewSelect().
		Model(&projects).
		Where("jira_project_id = ?", jiraProjectID).
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return projects, nil
}

// List returns a paginated list of SonarqubeProjects
func (r *SonarqubeProjectDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[SonarqubeProjectFilter],
) (*repository.PaginatedResult[model.SonarqubeProject], error) {
	return repository.FindAll[model.SonarqubeProject](
		ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[SonarqubeProjectFilter]) {
				q = q.Relation("JiraProject.CompanyClient")

				if params.Filters.Name != "" {
					q.Where("sp.project_name ILIKE ? OR jira_project.name ILIKE ?", "%"+params.Filters.Name+"%", "%"+params.Filters.Name+"%")
				}

				if params.Filters.ProjectKey != "" {
					q.Where("sp.project_key = ?", params.Filters.ProjectKey)
				}
			}),
	)
}

// Save persists a SonarqubeProject to the database
func (r *SonarqubeProjectDefaultRepository) Save(ctx context.Context, project *model.SonarqubeProject, forceID bool) (*model.SonarqubeProject, error) {
	err := repository.Save(
		ctx,
		r.DB,
		project,
		repository.WithSaveNew(forceID),
	)
	if err != nil {
		return nil, err
	}

	return project, nil
}

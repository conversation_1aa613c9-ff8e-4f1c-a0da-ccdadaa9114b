package auth

import (
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service"

	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/samber/do"
)

func Register(i *do.Injector, authConfig config.AuthConfig) error {
	do.Provide(i, func(i *do.Injector) (config.AuthConfig, error) {
		return authConfig, nil
	})

	// Register auth repository
	do.Provide(i, repository.NewUserDefaultRepository)

	// Register auth interactors
	do.Provide(i, in.NewCreateUserInteractor)
	do.Provide(i, in.NewUpdateUserInteractor)
	do.Provide(i, in.NewListUserInteractor)
	do.Provide(i, in.NewUpdateUserRoleInteractor)

	// Register auth presenters
	do.Provide(i, out.NewCreateUserPresenter)
	do.Provide(i, out.NewUpdateUserPresenter)
	do.Provide(i, out.NewListUserPresenter)
	do.Provide(i, out.NewUpdateUserRolePresenter)

	// Register auth service
	do.Provide(i, service.NewUserService)

	/// Register user provisioning service
	do.Provide(i, in.NewUserProvisioningService)

	return nil
}

package repository

import (
	"context"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

type UserDefaultRepository struct {
	DB bun.IDB
}

// compile time check if UserDefaultRepository implements UserRepository
var _ UserRepository = (*UserDefaultRepository)(nil)

func NewUserDefaultRepository(i *do.Injector) (UserRepository, error) {
	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return nil, err
	}

	return &UserDefaultRepository{
		DB: db,
	}, nil
}

func (r *UserDefaultRepository) Find(ctx context.Context, id uuid.UUID) (*model.User, error) {
	record, err := repository.Find[model.User](ctx, r.DB, id)
	return record, err
}

func (r *UserDefaultRepository) List(
	ctx context.Context,
	params repository.PaginationParams[UserFilter],
) (*repository.PaginatedResult[model.User], error) {
	return repository.FindAll[model.User](ctx,
		r.DB,
		params,
		repository.WithFindAllQueryBuilder(
			func(q *bun.SelectQuery, params repository.PaginationParams[UserFilter]) {
				if params.Filters.Username != "" {
					q.Where("username = ?", params.Filters.Username)
				}

				if params.Filters.Email != "" {
					q.Where("email = ?", params.Filters.Email)
				}

				if params.Filters.EmailCont != "" {
					q.Where("email ILIKE ?", "%"+params.Filters.EmailCont+"%")
				}
			}))
}

func (r *UserDefaultRepository) Save(ctx context.Context, user *model.User, opts ...repository.SaveOptionsFunc) (*model.User, error) {
	err := repository.Save(ctx, r.DB, user, opts...)
	if err != nil {
		return nil, err
	}

	return user, nil
}

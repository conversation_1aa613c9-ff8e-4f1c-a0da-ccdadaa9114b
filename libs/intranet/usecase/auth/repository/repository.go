package repository

import (
	"context"
	"errors"

	"sa-intranet/core/repository"
	"sa-intranet/usecase/auth/model"

	"github.com/google/uuid"
)

var (
	ErrRecordNotFound = errors.New("record not found")
	ErrInvalidPage    = errors.New("invalid page parameters")
)

type UserFilter struct {
	Username  string `json:"username,omitempty"`
	Email     string `json:"email,omitempty"`
	EmailCont string `json:"email_cont,omitempty"`
	Role      string `json:"role,omitempty"`
}

type UserRepository interface {
	Find(ctx context.Context, id uuid.UUID) (*model.User, error)
	List(ctx context.Context, params repository.PaginationParams[UserFilter]) (*repository.PaginatedResult[model.User], error)
	Save(ctx context.Context, user *model.User, opts ...repository.SaveOptionsFunc) (*model.User, error)
}

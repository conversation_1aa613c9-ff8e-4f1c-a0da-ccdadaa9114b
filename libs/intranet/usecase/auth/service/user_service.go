package service

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type UserService struct {
	createUserInPort      in.CreateUserInPort
	createUserOutPort     out.CreateUserOutPort
	updateUserInPort      in.UpdateUserInPort
	updateUserOutPort     out.UpdateUserOutPort
	listUserInPort        in.ListUserInPort
	listUserOutPort       out.ListUserOutPort
	updateUserRoleInPort  in.UpdateUserRoleInPort
	updateUserRoleOutPort out.UpdateUserRoleOutPort
}

func NewUserService(i *do.Injector) (*UserService, error) {
	createUserInPort := do.MustInvoke[in.CreateUserInPort](i)
	createUserOutPort := do.MustInvoke[out.CreateUserOutPort](i)
	updateUserInPort := do.MustInvoke[in.UpdateUserInPort](i)
	updateUserOutPort := do.MustInvoke[out.UpdateUserOutPort](i)
	listUserInPort := do.MustInvoke[in.ListUserInPort](i)
	listUserOutPort := do.MustInvoke[out.ListUserOutPort](i)
	updateUserRoleInPort := do.MustInvoke[in.UpdateUserRoleInPort](i)
	updateUserRoleOutPort := do.MustInvoke[out.UpdateUserRoleOutPort](i)

	return &UserService{
		createUserInPort:      createUserInPort,
		createUserOutPort:     createUserOutPort,
		updateUserInPort:      updateUserInPort,
		updateUserOutPort:     updateUserOutPort,
		listUserInPort:        listUserInPort,
		listUserOutPort:       listUserOutPort,
		updateUserRoleInPort:  updateUserRoleInPort,
		updateUserRoleOutPort: updateUserRoleOutPort,
	}, nil
}

func (s *UserService) CreateUser(input in.CreateUserInput) viewmodel.Response[out.CreateUserViewModel] {
	var viewmodel viewmodel.Response[out.CreateUserViewModel]

	output, err := s.createUserInPort.CreateUser(input)
	if err != nil {
		return s.createUserOutPort.PresentError(err)
	}

	viewmodel = s.createUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) UpdateUser(id uuid.UUID, input in.UpdateUserInput) viewmodel.Response[out.UpdateUserViewModel] {
	var viewmodel viewmodel.Response[out.UpdateUserViewModel]

	output, err := s.updateUserInPort.UpdateUser(id, input)
	if err != nil {
		return s.updateUserOutPort.PresentError(err)
	}

	viewmodel = s.updateUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) ListUsers(input in.ListUsersInput, page int, pageSize int) viewmodel.Response[out.ListUsersViewModel] {
	var viewmodel viewmodel.Response[out.ListUsersViewModel]

	output, err := s.listUserInPort.ListUser(input, page, pageSize)
	if err != nil {
		return s.listUserOutPort.PresentError(err)
	}

	viewmodel = s.listUserOutPort.Present(output)

	return viewmodel
}

func (s *UserService) UpdateUserRole(id uuid.UUID, input in.UpdateUserRoleInput) viewmodel.Response[out.UpdateUserRoleViewModel] {
	var viewmodel viewmodel.Response[out.UpdateUserRoleViewModel]

	output, err := s.updateUserRoleInPort.UpdateUserRole(id, input)
	if err != nil {
		return s.updateUserRoleOutPort.PresentError(err)
	}

	viewmodel = s.updateUserRoleOutPort.Present(output)

	return viewmodel
}

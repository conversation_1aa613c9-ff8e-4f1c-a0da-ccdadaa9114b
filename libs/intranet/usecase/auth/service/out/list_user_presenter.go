package out

import (
	"sort"

	"sa-intranet/core/viewmodel"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type ListUsersItemViewModel struct {
	ID        uuid.UUID `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
}

type Role struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
type ListUsersViewModel struct {
	Items      []ListUsersItemViewModel `json:"data"`
	Pagination in.Pagination            `json:"pagination"`
	Roles      []Role                   `json:"roles"`
}

type ListUserOutPort interface {
	Present(resp in.ListUsersOutput) viewmodel.Response[ListUsersViewModel]
	PresentError(err error) viewmodel.Response[ListUsersViewModel]
}

type ListUserPresenter struct {
	rbac *policies.RBACPolicy
}

var _ ListUserOutPort = (*ListUserPresenter)(nil)

func (p *ListUserPresenter) Present(resp in.ListUsersOutput) viewmodel.Response[ListUsersViewModel] {
	roles := make([]Role, 0, len(p.rbac.Roles))

	for key, role := range p.rbac.Roles {
		roles = append(roles,
			Role{
				ID:   key,
				Name: role.Description,
			},
		)
	}

	sort.Slice(roles, func(i, j int) bool {
		return roles[i].Name < roles[j].Name
	})

	listViewModel := mapListUsersOutputToViewModel(resp)
	listViewModel.Pagination = resp.Pagination
	listViewModel.Roles = roles

	return viewmodel.Response[ListUsersViewModel]{
		Success: true,
		Data:    listViewModel,
	}
}

func (p *ListUserPresenter) PresentError(err error) viewmodel.Response[ListUsersViewModel] {
	return viewmodel.Response[ListUsersViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewListUserPresenter(i *do.Injector) (ListUserOutPort, error) {
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &ListUserPresenter{
		rbac: rbac,
	}, nil
}

func mapListUsersOutputToViewModel(resp in.ListUsersOutput) ListUsersViewModel {
	var viewModel ListUsersViewModel
	for _, user := range resp.Items {
		viewModel.Items = append(viewModel.Items, ListUsersItemViewModel(user))
	}

	return viewModel
}

package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type UpdateUserRoleViewModel struct {
	UserID    uuid.UUID `json:"user_id"`
	UserEmail string    `json:"user_email"`
	RoleID    string    `json:"role_id"`
}

type UpdateUserRoleOutPort interface {
	Present(resp in.UpdateUserRoleOutput) viewmodel.Response[UpdateUserRoleViewModel]
	PresentError(err error) viewmodel.Response[UpdateUserRoleViewModel]
}

type UpdateUserRolePresenter struct{}

var _ UpdateUserRoleOutPort = (*UpdateUserRolePresenter)(nil)

func (p *UpdateUserRolePresenter) Present(resp in.UpdateUserRoleOutput) viewmodel.Response[UpdateUserRoleViewModel] {
	return viewmodel.Response[UpdateUserRoleViewModel]{
		Success: true,
		Data:    UpdateUserRoleViewModel(resp),
	}
}

func (p *UpdateUserRolePresenter) PresentError(err error) viewmodel.Response[UpdateUserRoleViewModel] {
	return viewmodel.Response[UpdateUserRoleViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewUpdateUserRolePresenter(i *do.Injector) (UpdateUserRoleOutPort, error) {
	return &UpdateUserRolePresenter{}, nil
}

package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateUserViewModel struct {
	ID       uuid.UUID `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	Role     string    `json:"role"`
}

type CreateUserOutPort interface {
	Present(resp in.CreateUserOutput) viewmodel.Response[CreateUserViewModel]
	PresentError(err error) viewmodel.Response[CreateUserViewModel]
}

type CreateUserPresenter struct{}

var _ CreateUserOutPort = (*CreateUserPresenter)(nil)

func (p *CreateUserPresenter) Present(resp in.CreateUserOutput) viewmodel.Response[CreateUserViewModel] {
	return viewmodel.Response[CreateUserViewModel]{
		Success: true,
		Data:    CreateUserViewModel(resp),
	}
}

func (p *CreateUserPresenter) PresentError(err error) viewmodel.Response[CreateUserViewModel] {
	return viewmodel.Response[CreateUserViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewCreateUserPresenter(i *do.Injector) (CreateUserOutPort, error) {
	return &CreateUserPresenter{}, nil
}

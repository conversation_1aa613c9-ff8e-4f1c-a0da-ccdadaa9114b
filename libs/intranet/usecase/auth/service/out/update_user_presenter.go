package out

import (
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service/in"

	"github.com/samber/do"
)

type UpdateUserViewModel = CreateUserViewModel

type UpdateUserOutPort interface {
	Present(resp in.UpdateUserOutput) viewmodel.Response[UpdateUserViewModel]
	PresentError(err error) viewmodel.Response[UpdateUserViewModel]
}

type UpdateUserPresenter struct{}

var _ UpdateUserOutPort = (*UpdateUserPresenter)(nil)

func (p *UpdateUserPresenter) Present(resp in.UpdateUserOutput) viewmodel.Response[UpdateUserViewModel] {
	return viewmodel.Response[UpdateUserViewModel]{
		Success: true,
		Data:    UpdateUserViewModel(resp),
	}
}

func (p *UpdateUserPresenter) PresentError(err error) viewmodel.Response[UpdateUserViewModel] {
	return viewmodel.Response[UpdateUserViewModel]{
		Success: false,
		Errors: []viewmodel.ErrorItem{
			{
				Message:   err.Error(),
				ErrorCode: "422",
			},
		},
	}
}

func NewUpdateUserPresenter(i *do.Injector) (UpdateUserOutPort, error) {
	return &UpdateUserPresenter{}, nil
}

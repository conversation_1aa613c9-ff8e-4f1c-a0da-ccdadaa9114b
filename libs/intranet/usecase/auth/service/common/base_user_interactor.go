package common

import (
	"context"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// BaseUserInteractor contains common functionality for user interactors
type BaseUserInteractor struct {
	Repo     repository.UserRepository
	Validate *validator.Validate
	Rbac     *policies.RBACPolicy
}

// FindUser retrieves a user by ID
func (b *BaseUserInteractor) FindUser(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return b.Repo.Find(ctx, id)
}

// SaveUser saves a user to the repository
func (b *BaseUserInteractor) SaveUser(ctx context.Context, user *model.User, isNew bool) (*model.User, error) {
	opts := []corerepo.SaveOptionsFunc{
		corerepo.WithSaveNew(isNew),
	}

	return b.Repo.Save(ctx, user, opts...)
}

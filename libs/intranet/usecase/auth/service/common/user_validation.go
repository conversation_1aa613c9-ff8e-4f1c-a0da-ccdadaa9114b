package common

import (
	"errors"
	"fmt"

	"sa-intranet/policies"

	"github.com/go-playground/validator/v10"
)

// Define static errors
var (
	ErrEmptyRole     = errors.New("role cannot be empty")
	ErrRoleNotExists = errors.New("role does not exist in the RBAC policy")
)

// ValidateRole checks if a role exists in the RBAC policy
func ValidateRole(rbac *policies.RBACPolicy, role string) error {
	if role == "" {
		return ErrEmptyRole
	}

	if rbac != nil && role != "" {
		if _, exists := rbac.Roles[role]; !exists {
			return fmt.Errorf("%w: '%s'", ErrRoleNotExists, role)
		}
	}

	return nil
}

// ValidateUserInput validates user input using the provided validator
func ValidateUserInput(validate *validator.Validate, input interface{}, rbac *policies.RBACPolicy, role string) error {
	// Validate that the role exists in the RBAC policy
	if err := ValidateRole(rbac, role); err != nil {
		return err
	}

	// Validate struct using validator
	if err := validate.Struct(input); err != nil {
		return err
	}

	return nil
}

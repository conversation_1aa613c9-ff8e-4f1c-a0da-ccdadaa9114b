package service_test

import (
	"testing"

	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateUserTestCase struct {
	name    string
	input   in.CreateUserInput
	wantErr bool
	verify  func(t *testing.T, input in.CreateUserInput, output viewmodel.Response[out.CreateUserViewModel])
	setup   func(t *testing.T, input in.CreateUserInput)
}

func TestCreateUser(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)

	tests := []CreateUserTestCase{
		{
			name: "empty_input",
			input: in.CreateUserInput{
				Username: "",
				Email:    "",
				Role:     "",
			},
			wantErr: true,
		},
		{
			name: "invalid_role",
			input: in.CreateUserInput{
				Username: "testusername",
				Email:    "<EMAIL>",
				Role:     "test_role",
			},
			wantErr: true,
		},
		{
			name: "success",
			input: in.CreateUserInput{
				Username: "testusername_success",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			verify: func(t *testing.T, input in.CreateUserInput, output viewmodel.Response[out.CreateUserViewModel]) {
				if output.Data.Username != input.Username {
					t.Errorf("got username %v, want %v", output.Data.Username, input.Username)
				}
				if output.Data.Email != input.Email {
					t.Errorf("got email %v, want %v", output.Data.Email, input.Email)
				}
				if output.Data.Role != input.Role {
					t.Errorf("got role %v, want %v", output.Data.Role, input.Role)
				}
			},
		},
		{
			name: "duplicate_username",
			input: in.CreateUserInput{
				Username: "testusernameexample",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: true,
			setup: func(t *testing.T, input in.CreateUserInput) {
				t.Helper()
				// Create a user with the same username
				createOutput := svc.CreateUser(input)
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			if tt.setup != nil {
				tt.setup(t, tt.input)
			}

			output := svc.CreateUser(tt.input)
			if (!output.Success) != tt.wantErr {
				t.Errorf("CreateUser() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

type UpdateUserTestCase struct {
	name    string
	input   in.UpdateUserInput
	wantErr bool
	verify  func(t *testing.T, input in.UpdateUserInput, output viewmodel.Response[out.UpdateUserViewModel])
	setup   func(t *testing.T) uuid.UUID
}

func TestUpdateUser(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.UserService](injector)
	tests := []UpdateUserTestCase{
		{
			name: "empty_input",
			input: in.UpdateUserInput{
				Username: "",
				Email:    "",
				Role:     "",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "invalid_role",
			input: in.UpdateUserInput{
				Username: "updatedusername_invalid_role",
				Email:    "<EMAIL>",
				Role:     "invalid_role",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username_invalid_role",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
		},
		{
			name: "non_existent_user",
			input: in.UpdateUserInput{
				Username: "updatedusername",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: true,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()

				// Return a random UUID that doesn't exist
				return uuid.New()
			},
		},
		{
			name: "success",
			input: in.UpdateUserInput{
				Username: "updatedusername_success",
				Email:    "<EMAIL>",
				Role:     "guest",
			},
			wantErr: false,
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()

				// Create a user to update
				createOutput := svc.CreateUser(in.CreateUserInput{
					Username: "original_username_success",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			verify: func(t *testing.T, input in.UpdateUserInput, output viewmodel.Response[out.UpdateUserViewModel]) {
				if output.Data.Username != input.Username {
					t.Errorf("got username %v, want %v", output.Data.Username, input.Username)
				}
				if output.Data.Email != input.Email {
					t.Errorf("got email %v, want %v", output.Data.Email, input.Email)
				}
				if output.Data.Role != input.Role {
					t.Errorf("got role %v, want %v", output.Data.Role, input.Role)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup test data and get user ID
			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			} else {
				userID = uuid.New() // Default to a random ID if no setup
			}

			// Call the service method
			output := svc.UpdateUser(userID, tt.input)

			// Check if error status matches expectation
			if (!output.Success) != tt.wantErr {
				t.Errorf("UpdateUser() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			// Verify output if test expects success and has a verify function
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

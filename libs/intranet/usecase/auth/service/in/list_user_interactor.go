package in

import (
	"context"

	coreRepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type Pagination struct {
	TotalCount  int `json:"totalCount"`
	CurrentPage int `json:"currentPage"`
	PerPage     int `json:"perPage"`
}

type ListUsersInput struct {
	Email string `json:"email"`
}

type UserListItem struct {
	ID        uuid.UUID `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
}

type ListUsersOutput struct {
	Items      []UserListItem `json:"data"`
	Pagination Pagination     `json:"pagination"`
}

type ListUserInPort interface {
	ListUser(input ListUsersInput, page int, pageSize int) (ListUsersOutput, error)
}

type ListUserInteractor struct {
	common.BaseUserInteractor
}

func NewListUserInteractor(i *do.Injector) (ListUserInPort, error) {
	repo := do.MustInvoke[repository.UserRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &ListUserInteractor{
		BaseUserInteractor: common.BaseUserInteractor{
			Repo:     repo,
			Validate: validate,
			Rbac:     rbac,
		},
	}, nil
}

func (i *ListUserInteractor) ListUser(input ListUsersInput, page int, pageSize int) (ListUsersOutput, error) {
	var output ListUsersOutput

	if page == 0 {
		page = 1
	}

	ctx := context.Background()
	params := coreRepo.PaginationParams[repository.UserFilter]{
		Filters: repository.UserFilter{
			EmailCont: input.Email,
		},
		Page:     page,
		PageSize: pageSize,
	}

	result, err := i.Repo.List(ctx, params)
	if err != nil {
		return output, err
	}

	for _, user := range result.Items {
		output.Items = append(output.Items, UserListItem{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			FirstName: user.FirstName,
			LastName:  user.LastName,
		})
	}

	output.Pagination = Pagination{
		TotalCount:  int(result.TotalItems),
		CurrentPage: result.Page,
		PerPage:     result.PageSize,
	}

	return output, nil
}

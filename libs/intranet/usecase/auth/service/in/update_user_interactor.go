package in

import (
	"context"

	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type UpdateUserInput struct {
	Username string `json:"username" validate:"required"`
	Email    string `json:"email" validate:"required"`
	Role     string `json:"role" validate:"required"`
}

type UpdateUserOutput struct {
	ID       uuid.UUID `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	Role     string    `json:"role"`
}

type UpdateUserInPort interface {
	UpdateUser(id uuid.UUID, input UpdateUserInput) (UpdateUserOutput, error)
}

type UpdateUserInteractor struct {
	common.BaseUserInteractor
}

func NewUpdateUserInteractor(i *do.Injector) (UpdateUserInPort, error) {
	repo := do.MustInvoke[repository.UserRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &UpdateUserInteractor{
		BaseUserInteractor: common.BaseUserInteractor{
			Repo:     repo,
			Validate: validate,
			Rbac:     rbac,
		},
	}, nil
}

func (i *UpdateUserInteractor) UpdateUser(id uuid.UUID, input UpdateUserInput) (UpdateUserOutput, error) {
	var output UpdateUserOutput

	// Use common validation
	err := common.ValidateUserInput(i.Validate, input, i.Rbac, input.Role)
	if err != nil {
		return output, err
	}

	// Find existing user using base interactor
	existingUser, err := i.FindUser(context.Background(), id)
	if err != nil {
		return output, err
	}

	userModel := i.mapInputToModel(input, existingUser)

	// Use base interactor to save
	user, err := i.SaveUser(context.Background(), userModel, false)
	if err != nil {
		return output, err
	}

	output = i.mapUserToOutput(user)

	return output, nil
}

func (i *UpdateUserInteractor) mapUserToOutput(user *model.User) UpdateUserOutput {
	return UpdateUserOutput{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
	}
}

func (i *UpdateUserInteractor) mapInputToModel(input UpdateUserInput, existingUser *model.User) *model.User {
	existingUser.Username = input.Username
	existingUser.Email = input.Email
	existingUser.Role = input.Role

	return existingUser
}

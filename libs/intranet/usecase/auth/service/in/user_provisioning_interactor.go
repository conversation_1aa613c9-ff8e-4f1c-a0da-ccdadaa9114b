package in

import (
	"context"
	"encoding/json"
	"log"
	"strings"
	"time"

	"sa-intranet/core/cache"
	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
)

// UserProvisioningInteractor handles user provisioning from JWT data
type UserProvisioningInteractor struct {
	userRepo repository.UserRepository
	cache    cache.Cache
	// Default cache expiration time
	cacheExpiration time.Duration
	defaultRole     string
}

// NewUserProvisioningService creates a new user provisioning service
func NewUserProvisioningService(i *do.Injector) (*UserProvisioningInteractor, error) {
	userRepo := do.MustInvoke[repository.UserRepository](i)
	cache := do.MustInvoke[cache.Cache](i)
	authConfig := do.MustInvoke[config.AuthConfig](i)

	return &UserProvisioningInteractor{
		userRepo:        userRepo,
		cache:           cache,
		cacheExpiration: 60 * time.Minute, // Default cache expiration
		defaultRole:     authConfig.DefaultRole,
	}, nil
}

// GetOrCreateUserFromJWT retrieves a user from JWT data or creates one if it doesn't exist
func (s *UserProvisioningInteractor) GetOrCreateUserFromJWT(ctx context.Context, jwtData policies.JwtData) (*model.User, error) {
	jwtPayload := jwtData.Payload
	// Generate a cache key based on a unique identifier from JWT
	cacheKey := "user_jwt_" + jwtPayload.Email

	// Try to get from cache first
	cachedData, err := s.cache.Get(ctx, cacheKey)
	if err == nil {
		// Deserialize the cached user
		var user model.User
		if unmarshalErr := json.Unmarshal(cachedData, &user); unmarshalErr == nil {
			return &user, nil
		}
	}

	// Try to find user by email using List method with filter
	params := corerepo.PaginationParams[repository.UserFilter]{
		Page:     1,
		PageSize: 1,
		Filters: repository.UserFilter{
			Email: jwtPayload.Email,
		},
	}

	result, err := s.userRepo.List(ctx, params)
	if err == nil && len(result.Items) > 0 {
		user := &result.Items[0]

		// Serialize and cache the user
		userData, marshalErr := json.Marshal(user)
		if marshalErr == nil {
			if setErr := s.cache.Set(ctx, cacheKey, userData, s.cacheExpiration); setErr != nil {
				// Log the error but continue
				log.Printf("Failed to cache user: %v", setErr)
			}
		}

		return user, nil
	}

	// User not found, create a new one with default role
	newUser := &model.User{
		ID:        uuid.New(),
		Username:  jwtPayload.Username,
		Email:     jwtPayload.Email,
		FirstName: strings.TrimSpace(jwtPayload.GivenName),
		LastName:  strings.TrimSpace(jwtPayload.FamilyName),
		Role:      s.defaultRole, // Default role
	}

	// Save the new user
	savedUser, err := s.userRepo.Save(ctx, newUser)
	if err != nil {
		return nil, err
	}

	// Serialize and cache the new user
	userData, err := json.Marshal(savedUser)
	if err == nil {
		if setErr := s.cache.Set(ctx, cacheKey, userData, s.cacheExpiration); setErr != nil {
			// Log the error but continue
			log.Printf("Failed to cache user: %v", setErr)
		}
	}

	return savedUser, nil
}

package in

import (
	"context"

	"sa-intranet/core/validatorext"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

type CreateUserInput struct {
	Username string `json:"username" validate:"required,username_already_exists"`
	Email    string `json:"email" validate:"required"`
	Role     string `json:"role" validate:"required"`
}

type CreateUserOutput struct {
	ID       uuid.UUID `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	Role     string    `json:"role"`
}

type CreateUserInPort interface {
	CreateUser(input CreateUserInput) (CreateUserOutput, error)
}

type CreateUserInteractor struct {
	common.BaseUserInteractor
}

func NewCreateUserInteractor(i *do.Injector) (CreateUserInPort, error) {
	db := do.MustInvoke[*bun.DB](i)
	repo := do.MustInvoke[repository.UserRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())

	err := validate.RegisterValidationCtx("username_already_exists", validatorext.ValidateModelWithKeyAlredyExists[string](
		db,
		(*model.User)(nil),
		"username",
	))
	if err != nil {
		return nil, err
	}

	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &CreateUserInteractor{
		BaseUserInteractor: common.BaseUserInteractor{
			Repo:     repo,
			Validate: validate,
			Rbac:     rbac,
		},
	}, nil
}

func (i *CreateUserInteractor) CreateUser(input CreateUserInput) (CreateUserOutput, error) {
	var output CreateUserOutput

	// Use common validation
	err := common.ValidateUserInput(i.Validate, input, i.Rbac, input.Role)
	if err != nil {
		return output, err
	}

	userModel := i.mapInputToModel(input)

	// Use base interactor to save
	user, err := i.SaveUser(context.Background(), userModel, true)
	if err != nil {
		return output, err
	}

	output = i.mapUserToOutput(user)

	return output, nil
}

func (i *CreateUserInteractor) mapUserToOutput(user *model.User) CreateUserOutput {
	return CreateUserOutput{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
	}
}

func (i *CreateUserInteractor) mapInputToModel(input CreateUserInput) *model.User {
	return &model.User{
		Username: input.Username,
		Email:    input.Email,
		Role:     input.Role,
	}
}

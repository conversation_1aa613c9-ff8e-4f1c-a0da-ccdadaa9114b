package in

import (
	"context"
	"log/slog"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/common"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/samber/do"
)

type UpdateUserRoleInput struct {
	Role string `json:"role" validate:"required"`
}

type UpdateUserRoleOutput struct {
	UserID    uuid.UUID `json:"user_id"`
	UserEmail string    `json:"user_email"`
	RoleID    string    `json:"role_id"`
}

type UpdateUserRoleInPort interface {
	UpdateUserRole(id uuid.UUID, input UpdateUserRoleInput) (UpdateUserRoleOutput, error)
}

type UpdateUserRoleInteractor struct {
	common.BaseUserInteractor
}

func NewUpdateUserRoleInteractor(i *do.Injector) (UpdateUserRoleInPort, error) {
	repo := do.MustInvoke[repository.UserRepository](i)
	validate := validator.New(validator.WithRequiredStructEnabled())
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	return &UpdateUserRoleInteractor{
		BaseUserInteractor: common.BaseUserInteractor{
			Repo:     repo,
			Validate: validate,
			Rbac:     rbac,
		},
	}, nil
}

func (i *UpdateUserRoleInteractor) UpdateUserRole(id uuid.UUID, input UpdateUserRoleInput) (UpdateUserRoleOutput, error) {
	var output UpdateUserRoleOutput

	// Use common validation
	err := common.ValidateRole(i.Rbac, input.Role)
	if err != nil {
		return output, err
	}

	// Find existing user using base interactor
	existingUser, err := i.FindUser(context.Background(), id)
	if err != nil {
		return output, err
	}

	// Update user role
	existingUser.Role = input.Role

	slog.Info("Updating user role", "user_id", id, "role", input.Role)

	// Use base interactor to save
	user, err := i.Repo.Save(
		context.Background(),
		existingUser,
		corerepo.WithSaveNew(false),
		corerepo.WithSaveUpdateFields([]string{"role"}),
		corerepo.WithSaveAllowUpdateFields([]string{"role"}),
	)
	if err != nil {
		return output, err
	}

	output = i.mapUserToOutput(user)

	return output, nil
}

func (i *UpdateUserRoleInteractor) mapUserToOutput(user *model.User) UpdateUserRoleOutput {
	return UpdateUserRoleOutput{
		UserID:    user.ID,
		RoleID:    user.Role,
		UserEmail: user.Email,
	}
}

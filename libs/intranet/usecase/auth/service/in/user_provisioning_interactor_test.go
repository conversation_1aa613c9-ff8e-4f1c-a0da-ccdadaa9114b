package in_test

import (
	"context"
	"log/slog"
	"testing"
	"time"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/config"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"
	"sa-intranet/usecase/auth/service/in"

	"github.com/google/uuid"
	"github.com/samber/do"
)

func TestGetOrCreateUserFromJWT(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	svc := do.MustInvoke[*in.UserProvisioningInteractor](injector)
	userRepo := do.MustInvoke[repository.UserRepository](injector)
	authConfig := do.MustInvoke[config.AuthConfig](injector)
	defaultRole := authConfig.DefaultRole
	slog.Info("Default role:", "Role", defaultRole)

	// Create a test user to use in some test cases
	existingUser := &model.User{
		ID:        uuid.New(),
		Username:  "existing_user",
		Email:     "<EMAIL>",
		Role:      defaultRole,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	_, err := userRepo.Save(ctx, existingUser)
	if err != nil {
		t.Fatalf("Failed to create test user: %v", err)
	}

	tests := []struct {
		name     string
		jwtData  policies.JwtData
		wantUser *model.User
		wantErr  bool
	}{
		{
			name: "existing_user",
			jwtData: policies.JwtData{
				Payload: policies.JwtDataPayload{
					Email:    "<EMAIL>",
					Username: "existing_user",
				},
			},
			wantUser: &model.User{
				Email:    "<EMAIL>",
				Username: "existing_user",
				Role:     defaultRole,
			},
			wantErr: false,
		},
		{
			name: "new_user",
			jwtData: policies.JwtData{
				Payload: policies.JwtDataPayload{
					Email:    "<EMAIL>",
					Username: "new_user",
				},
			},
			wantUser: &model.User{
				Email:    "<EMAIL>",
				Username: "new_user",
				Role:     defaultRole,
			},
			wantErr: false,
		},
		{
			name: "cached_user",
			jwtData: policies.JwtData{
				Payload: policies.JwtDataPayload{
					Email:    "<EMAIL>",
					Username: "existing_user",
				},
			},
			wantUser: &model.User{
				Email:    "<EMAIL>",
				Username: "existing_user",
				Role:     defaultRole,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			user, err := svc.GetOrCreateUserFromJWT(ctx, tt.jwtData)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Expected error but got nil")
				}

				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)

				return
			}

			if user == nil {
				t.Errorf("Expected user but got nil")

				return
			}

			if user.Email != tt.wantUser.Email {
				t.Errorf("Expected email %s but got %s", tt.wantUser.Email, user.Email)
			}

			if user.Username != tt.wantUser.Username {
				t.Errorf("Expected username %s but got %s", tt.wantUser.Username, user.Username)
			}

			if user.Role != tt.wantUser.Role {
				t.Errorf("Expected role %s but got %s", tt.wantUser.Role, user.Role)
			}

			// For new users, verify they were actually created in the database
			if tt.name == "new_user" {
				params := corerepo.PaginationParams[repository.UserFilter]{
					Page:     1,
					PageSize: 1,
					Filters: repository.UserFilter{
						Email: tt.jwtData.Payload.Email,
					},
				}

				result, err := userRepo.List(ctx, params)
				if err != nil {
					t.Errorf("Failed to list users: %v", err)
					return
				}

				if len(result.Items) != 1 {
					t.Errorf("Expected 1 user but got %d", len(result.Items))
					return
				}

				if result.Items[0].Email != tt.wantUser.Email {
					t.Errorf("Expected email %s but got %s", tt.wantUser.Email, result.Items[0].Email)
				}
			}
		})
	}
}

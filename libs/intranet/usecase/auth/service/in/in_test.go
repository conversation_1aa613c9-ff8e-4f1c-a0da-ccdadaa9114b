package in_test

import (
	"context"
	"testing"

	testhelpers "sa-intranet/testing"

	"github.com/samber/do"
)

var injector *do.Injector

func TestMain(m *testing.M) {
	ctx := context.Background()
	injector = do.New()

	bootstrap := testhelpers.Bootstrap(
		ctx,
		injector,
		testhelpers.WithPostgres(ctx),
		testhelpers.WithMigrations(ctx), // Apply migrations, needs to be after WithPostgres
	)

	defer func() {
		if err := bootstrap.Shutdown(ctx); err != nil {
			panic(err)
		}
	}()

	// Run all tests
	m.Run()
}

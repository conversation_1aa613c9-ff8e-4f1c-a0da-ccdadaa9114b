package intranet

import (
	"database/sql"

	"sa-intranet/config"
	"sa-intranet/core"
	"sa-intranet/policies"

	"sa-intranet/usecase/auth"
	"sa-intranet/usecase/cqm"

	"github.com/samber/do"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
	"github.com/uptrace/bun/driver/pgdriver"
	"github.com/uptrace/bun/extra/bundebug"
)

func Register(i *do.Injector, conf config.Config) error {
	do.Provide(i, func(i *do.Injector) (config.Config, error) {
		return conf, nil
	})

	// Register database
	registerDatabase(i, conf)

	// Register core
	err := core.Register(i, conf.Cypher)
	if err != nil {
		return err
	}

	// Register policies
	err = policies.Register(i)
	if err != nil {
		return err
	}

	err = cqm.Register(i, conf)
	if err != nil {
		return err
	}

	// Register auth
	err = auth.Register(i, conf.Auth)
	if err != nil {
		return err
	}

	return nil
}

func registerDatabase(i *do.Injector, conf config.Config) {
	// Register database
	do.Provide(i, func(i *do.Injector) (config.DatabaseConfig, error) {
		return conf.Database, nil
	})

	do.Provide(i, func(i *do.Injector) (*bun.DB, error) {
		conf := do.MustInvoke[config.DatabaseConfig](i)
		dsn := conf.DatabaseURL
		sqldb := sql.OpenDB(pgdriver.NewConnector(pgdriver.WithDSN(dsn)))

		db := bun.NewDB(sqldb, pgdialect.New())
		if conf.Debug {
			db.AddQueryHook(bundebug.NewQueryHook(
				bundebug.WithVerbose(true),
			))
		}

		return db, nil
	})
}

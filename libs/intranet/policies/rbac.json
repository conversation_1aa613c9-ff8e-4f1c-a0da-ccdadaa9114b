{"scopes": {"all": {"description": "Access to all", "actions": ["read", "write", "delete"]}}, "roles": {"admin": {"description": "Admin role", "rules": {"default:ea-service:/*": {"actions": ["read", "write", "delete"], "allowed_scopes": ["all"], "auth_effect": "allow"}}}, "enterprise-architect": {"description": "Enterprise architect role", "rules": {"default:ea-service:/admin*": {"actions": ["read", "write", "delete"], "allowed_scopes": [], "auth_effect": "deny"}, "default:ea-service:/*": {"actions": ["read"], "allowed_scopes": ["all"], "auth_effect": "allow"}}}, "business-architect": {"description": "Business architect role", "rules": {"default:ea-service:/admin*": {"actions": ["read", "write", "delete"], "allowed_scopes": [], "auth_effect": "deny"}, "default:ea-service:/*": {"actions": ["read", "write"], "allowed_scopes": ["all"], "auth_effect": "allow"}}}, "application-architect": {"description": "Application architect role", "rules": {"default:ea-service:/admin*": {"actions": ["read", "write", "delete"], "allowed_scopes": [], "auth_effect": "deny"}, "default:ea-service:/*": {"actions": ["read", "write"], "allowed_scopes": ["all"], "auth_effect": "allow"}}}, "guest": {"description": "Guest role", "rules": {"default:ea-service:/wiki*": {"actions": ["read"], "allowed_scopes": ["all"], "auth_effect": "allow"}}}}, "metadata": {"version": "1.0", "description": "Role-based access control with reusable scopes", "fields": {"roles": {"description": "Role description", "rules": {"key_format": "tenant:service:resource_path", "actions": "Array of allowed HTTP methods/actions", "allowed_scopes": "Array of applicable scope names from the scopes object", "auth_effect": "Authorization effect (allow/deny)"}}}, "scope_fields": {"description": "Human-readable explanation of the scope", "filter": "SQL-like condition for filtering resources", "actions": "Allowed actions within this scope"}}}
package policies

import (
	"context"
	_ "embed"
	"errors"
	"fmt"

	"sa-intranet/core"

	"github.com/open-policy-agent/opa/v1/rego"
	"github.com/samber/do"
)

//go:embed aws-cognito.rego
var awsCognitoRego []byte

// Define static errors
var (
	ErrNoResults              = errors.New("query evaluation returned no results")
	ErrUnexpectedResult       = errors.New("unexpected result type")
	ErrUnexpectedJWTHeader    = errors.New("unexpected JWT header type")
	ErrUnexpectedJWTPayload   = errors.New("unexpected JWT payload type")
	ErrUnexpectedJWTSignature = errors.New("unexpected JWT signature type")
	ErrUnexpectedJWTData      = errors.New("unexpected JWT data type")
)

type JwtDataPayload struct {
	Email         string `json:"email"`
	EmailVerified string `json:"email_verified"`
	FamilyName    string `json:"family_name"`
	GivenName     string `json:"given_name"`
	ISS           string `json:"iss"`
	Sub           string `json:"sub"`
	Username      string `json:"username"`
	Exp           int    `json:"exp"`
	Identities    string `json:"identities"`
}

type JwtDataHeader struct {
	Alg    string `json:"alg"`
	Client string `json:"client"`
	Kid    string `json:"kid"`
	Exp    int    `json:"exp"`
	Typ    string `json:"typ"`
	ISS    string `json:"iss"`
	Signer string `json:"signer"`
}

func mapToJwtDataHeader(header map[string]interface{}) JwtDataHeader {
	var jwtHeader JwtDataHeader

	// Manual field assignment
	if alg, ok := header["alg"].(string); ok {
		jwtHeader.Alg = alg
	}

	if client, ok := header["client"].(string); ok {
		jwtHeader.Client = client
	}

	if kid, ok := header["kid"].(string); ok {
		jwtHeader.Kid = kid
	}

	if exp, ok := header["exp"].(int); ok {
		jwtHeader.Exp = exp
	}

	if typ, ok := header["typ"].(string); ok {
		jwtHeader.Typ = typ
	}

	if iss, ok := header["iss"].(string); ok {
		jwtHeader.ISS = iss
	}

	if signer, ok := header["signer"].(string); ok {
		jwtHeader.Signer = signer
	}

	return jwtHeader
}

func mapToJwtDataPayload(payload map[string]interface{}) JwtDataPayload {
	var jwtPayload JwtDataPayload

	// Manual field assignment
	if email, ok := payload["email"].(string); ok {
		jwtPayload.Email = email
	}

	if emailVerified, ok := payload["email_verified"].(string); ok {
		jwtPayload.EmailVerified = emailVerified
	}

	if familyName, ok := payload["family_name"].(string); ok {
		jwtPayload.FamilyName = familyName
	}

	if givenName, ok := payload["given_name"].(string); ok {
		jwtPayload.GivenName = givenName
	}

	if iss, ok := payload["iss"].(string); ok {
		jwtPayload.ISS = iss
	}

	if sub, ok := payload["sub"].(string); ok {
		jwtPayload.Sub = sub
	}

	if username, ok := payload["username"].(string); ok {
		jwtPayload.Username = username
	}

	if exp, ok := payload["exp"].(int); ok {
		jwtPayload.Exp = exp
	}

	if identities, ok := payload["identities"].(string); ok {
		jwtPayload.Identities = identities
	}

	return jwtPayload
}

type JwtData struct {
	Header    JwtDataHeader
	Payload   JwtDataPayload
	Signature string
}

func EvalCognitoPolicy(ctx context.Context, input any) (JwtData, error) {
	var jwtData JwtData
	r := rego.New(
		rego.Query("x=data.awscognito.allow; jwt=data.awscognito.jwt"),
		// rego.Package("awscognito"),
		rego.Module("aws-cognito.rego", string(awsCognitoRego)),
		rego.EnablePrintStatements(true),
	)

	query, err := r.PrepareForEval(ctx)
	if err != nil {
		return jwtData, fmt.Errorf("error preparing query: %w", err)
	}

	// Execute the prepared query.
	results, err := query.Eval(ctx, rego.EvalInput(input))
	if err != nil {
		return jwtData, fmt.Errorf("error evaluating query: %w", err)
	}

	if len(results) == 0 {
		return jwtData, ErrNoResults
	}

	allow, ok := results[0].Bindings["x"].(bool)
	if !ok {
		return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedResult, results[0].Bindings["x"])
	}

	jwt, ok := results[0].Bindings["jwt"].([]interface{})

	if len(jwt) == 3 {
		mapHeaderData, headerOk := jwt[0].(map[string]interface{})
		if !headerOk {
			return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTHeader, jwt[0])
		}

		jwtData.Header = mapToJwtDataHeader(mapHeaderData)

		mapPayloadData, payloadOk := jwt[1].(map[string]interface{})
		if !payloadOk {
			return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTPayload, jwt[1])
		}

		jwtData.Payload = mapToJwtDataPayload(mapPayloadData)

		signature, signatureOk := jwt[2].(string)
		if !signatureOk {
			return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTSignature, jwt[2])
		}

		jwtData.Signature = signature
	}

	if !ok {
		return jwtData, fmt.Errorf(core.ErrFormatWithType, ErrUnexpectedJWTData, results[0].Bindings["jwt"])
	}

	if !allow {
		return jwtData, ErrAccessDenied
	}

	return jwtData, nil
}

func Register(i *do.Injector) error {
	do.Provide(i, NewRBACPolicy)
	return nil
}

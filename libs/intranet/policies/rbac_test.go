package policies

import (
	"encoding/json"
	"testing"
)

// go test -v libs/intranet/policies/rbac_test.go libs/intranet/policies/rbac.go -run TestRBACPolicy
func TestRBACPolicy(t *testing.T) {
	var policy RBACPolicy
	if err := json.Unmarshal(rbacJSON, &policy); err != nil {
		t.Fatalf("Failed to unmarshal policy: %v", err)
	}

	tenant := "default"
	service := "ea-service"

	admin := User{
		ID:           "user1234a",
		DepartmentID: "dept4567a",
		Role:         "admin",
	}

	enterpriseArch := User{
		ID:           "user1234e",
		DepartmentID: "dept4567e",
		Role:         "enterprise-architect",
	}

	businessArch := User{
		ID:           "user1234b",
		DepartmentID: "dept4567b",
		Role:         "business-architect",
	}

	applicationArch := User{
		ID:           "user1234a",
		DepartmentID: "dept4567a",
		Role:         "application-architect",
	}

	guest := User{
		ID:           "user123",
		DepartmentID: "dept456",
		Role:         "guest",
	}

	tests := []struct {
		name                    string
		request                 *AuthRequest
		wantErr                 bool
		wantScopeLen            int
		disableScopesValidation bool
	}{
		// Enterprise Architect role tests
		{
			name: "Invalid enterprise-architect request with no actions",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{},
				AllowedScopes: []string{"all"},
				User:          enterpriseArch,
			},
			wantErr:                 true,
			disableScopesValidation: false,
			wantScopeLen:            0,
		},
		{
			name: "Valid enterprise-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          enterpriseArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},

		// Admin role tests
		{
			name: "Invalid admin request with no actions",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 true,
			disableScopesValidation: false,
			wantScopeLen:            0,
		},
		{
			name: "Valid admin request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid admin request to admin path",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          admin,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		// Business Architect role tests
		{
			name: "Valid business-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid business-architect request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Valid business-architect request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          businessArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		// Application Architect role tests
		{
			name: "Valid application-architect request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          applicationArch,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		// Guest role tests
		{
			name: "Valid guest request",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/wiki",
				Actions:       []string{"read"},
				AllowedScopes: []string{"all"},
				User:          applicationArch,
			},
			wantErr:                 false,
			disableScopesValidation: false,
			wantScopeLen:            1,
		},
		{
			name: "Valid guest request with no scopes validation",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/wiki/",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 false,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read cqm module with slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm/sonarqube/projects",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read cqm module without slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/cqm",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read admin module with slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin/",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
		{
			name: "Guest user is not allowed to read admin module without slash",
			request: &AuthRequest{
				TenantID:      tenant,
				Service:       service,
				Path:          "/admin",
				Actions:       []string{"read"},
				AllowedScopes: []string{},
				User:          guest,
			},
			wantErr:                 true,
			disableScopesValidation: true,
			wantScopeLen:            0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := policy.Validate(tt.request, ValidateOptions{
				DisableScopesValidation: tt.disableScopesValidation,
			})

			if (err != nil) != tt.wantErr {
				t.Errorf("RBACPolicy.Validate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if result == nil {
					t.Error("Expected validation result, got nil")
					return
				}

				if result.Role == nil {
					t.Error("Expected role information, got nil")
					return
				}

				if len(result.ActiveScopes) != tt.wantScopeLen {
					t.Errorf("Expected %d active scopes, got %d", tt.wantScopeLen, len(result.ActiveScopes))
				}
			}
		})
	}
}

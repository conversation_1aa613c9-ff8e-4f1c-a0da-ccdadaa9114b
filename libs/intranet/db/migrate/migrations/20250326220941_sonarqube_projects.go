package migrations

import (
	"context"
	"log"

	"sa-intranet/usecase/cqm/model"

	"github.com/uptrace/bun"
)

//nolint:gochecknoinits
func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] SonarqubeProject")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.SonarqubeProject)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add indexes for better query performance
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_sonarqube_projects_project_key ON sonarqube_projects (project_key)`)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_sonarqube_projects_jira_project_id ON sonarqube_projects (jira_project_id)`)
			if err != nil {
				return err
			}

			// Composite index for project_key and branch which might be used together in queries
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_sonarqube_projects_project_key_branch ON sonarqube_projects (project_key, branch)`)
			if err != nil {
				return err
			}

			// Composite index for project_key and jira_project_id
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_sonarqube_projects_project_key_jira_project_id ON sonarqube_projects (project_key, jira_project_id)`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] SonarqubeProject")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Drop the table
			_, err := tx.NewDropTable().
				Model((*model.SonarqubeProject)(nil)).
				IfExists().
				Exec(ctx)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

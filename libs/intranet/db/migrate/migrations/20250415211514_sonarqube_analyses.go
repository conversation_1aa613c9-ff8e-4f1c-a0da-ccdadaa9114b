package migrations

import (
	"context"
	"fmt"

	"sa-intranet/usecase/cqm/model"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [up migration] SonarqubeAnalysis")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.SonarqubeAnalysis)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add completed_at index (B-tree for range queries)
			_, err = tx.NewCreateIndex().
				Model((*model.SonarqubeAnalysis)(nil)).
				Index("idx_sonarqube_analyses_completed_at").
				Column("completed_at").
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add status index (Hash for exact matches)
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_status 
				ON sonarqube_analyses USING hash(status);
			`)
			if err != nil {
				return err
			}

			// Add index for sonarqube_project_id (B-tree for foreign key)
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_project_id 
				ON sonarqube_analyses(sonarqube_project_id);
			`)
			if err != nil {
				return err
			}

			// Add unique index for analysis_id
			_, err = tx.ExecContext(ctx, `
				CREATE UNIQUE INDEX idx_sonarqube_analyses_analysis_id 
				ON sonarqube_analyses(analysis_id);
			`)
			if err != nil {
				return err
			}

			// Add composite index for quality gate queries
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_quality_gate 
				ON sonarqube_analyses(quality_gate_status, quality_gate_name);
			`)
			if err != nil {
				return err
			}

			// Add check constraint for status
			_, err = tx.ExecContext(ctx, `
				ALTER TABLE sonarqube_analyses 
				ADD CONSTRAINT check_valid_status 
				CHECK (status IN ('processing', 'succeeded', 'failed'));
			`)
			if err != nil {
				return err
			}

			// Modify the column type and add constraints
			_, err = tx.ExecContext(ctx, `
				ALTER TABLE sonarqube_analyses 
				ALTER COLUMN quality_gate_status TYPE CHAR(5),
				ADD CONSTRAINT check_valid_quality_gate_status 
				CHECK (quality_gate_status IN ('OK', 'ERROR'));
			`)
			if err != nil {
				return err
			}

			// Create a hash index for exact matches
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_quality_gate_status 
				ON sonarqube_analyses USING hash(quality_gate_status);
			`)
			if err != nil {
				return err
			}

			// Add a boolean expression index for 'OK' status
			// This can speed up queries that filter for successful quality gates
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_quality_gate_ok 
				ON sonarqube_analyses ((quality_gate_status = 'OK'));
			`)
			if err != nil {
				return err
			}

			// Add a boolean expression index for 'ERROR' status
			_, err = tx.ExecContext(ctx, `
				CREATE INDEX idx_sonarqube_analyses_quality_gate_error 
				ON sonarqube_analyses ((quality_gate_status = 'ERROR'));
			`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [down migration] SonarqubeAnalysis")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.SonarqubeAnalysis)(nil)).
				IfExists().
				Exec(ctx)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

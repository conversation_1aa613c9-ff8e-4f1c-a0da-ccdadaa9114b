package migrations

import (
	"context"
	"fmt"

	"sa-intranet/usecase/cqm/model"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [up migration] SonarqubeIssue")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.SonarqubeIssue)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add foreign key constraint
			_, err = tx.ExecContext(ctx, `
                ALTER TABLE sonarqube_issues
                ADD CONSTRAINT fk_sonarqube_project
                FOREIGN KEY (sonarqube_project_id)
                REFERENCES sonarqube_projects(id)
                ON DELETE CASCADE
            `)
			if err != nil {
				return err
			}

			// Single-column indexes
			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_jira_issue_id
                ON sonarqube_issues(jira_issue_id)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_jira_issue_key
                ON sonarqube_issues(jira_issue_key)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_issue_id
                ON sonarqube_issues(issue_id)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_project_id
                ON sonarqube_issues(sonarqube_project_id)
            `)
			if err != nil {
				return err
			}

			// Composite indexes for common query patterns
			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_project_created
                ON sonarqube_issues(sonarqube_project_id, created_at DESC)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_project_jira_id
                ON sonarqube_issues(sonarqube_project_id, jira_issue_id)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_project_jira_key
                ON sonarqube_issues(sonarqube_project_id, jira_issue_key)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_project_issue_id
                ON sonarqube_issues(sonarqube_project_id, issue_id)
            `)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_sonarqube_issues_jira_composite
                ON sonarqube_issues(jira_issue_id, jira_issue_key)
            `)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [down migration] SonarqubeIssue")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.SonarqubeIssue)(nil)).
				IfExists().
				Exec(ctx)

			return err
		})
	})
}

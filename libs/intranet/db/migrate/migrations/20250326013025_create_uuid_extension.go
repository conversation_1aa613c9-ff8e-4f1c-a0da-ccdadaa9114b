package migrations

import (
	"context"
	"log"

	"github.com/uptrace/bun"
)

//nolint:gochecknoinits
func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] Enable UUID extension")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";")
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] Disable UUID extension")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.Exec("DROP EXTENSION IF EXISTS \"uuid-ossp\";")
			if err != nil {
				return err
			}

			return nil
		})
	})
}

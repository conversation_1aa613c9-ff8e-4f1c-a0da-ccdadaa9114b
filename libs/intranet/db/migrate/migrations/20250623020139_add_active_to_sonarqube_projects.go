package migrations

import (
	"context"
	"fmt"

	"github.com/uptrace/bun"
)

// ...existing code...
func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [up migration] ")
		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Add the 'active' column to the 'sonarqube_projects' table
			_, err := tx.ExecContext(ctx, `
							ALTER TABLE sonarqube_projects
							ADD COLUMN IF NOT EXISTS active BOOLEAN NOT NULL DEFAULT TRUE;
					`)
			if err != nil {
				return fmt.Errorf("failed to add 'active' column: %w", err)
			}

			// Add index for active column
			_, err = tx.ExecContext(ctx, `
							CREATE INDEX IF NOT EXISTS idx_sonarqube_projects_active
							ON sonarqube_projects(active);
					`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [down migration] ")
		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.ExecContext(ctx, `
							ALTER TABLE sonarqube_projects
							DROP COLUMN IF EXISTS active;
					`)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

package migrations

import (
	"context"
	"log"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] Enable pg_trgm extension")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.ExecContext(ctx, `CREATE EXTENSION IF NOT EXISTS pg_trgm;`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] Disable pg_trgm extension")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.ExecContext(ctx, `DROP EXTENSION IF EXISTS pg_trgm;`)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

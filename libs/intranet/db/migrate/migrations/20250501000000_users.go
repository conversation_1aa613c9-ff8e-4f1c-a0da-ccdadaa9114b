package migrations

import (
	"context"
	"log"

	"sa-intranet/usecase/auth/model"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] User")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.User)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add indexes for better query performance
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_users_username ON users (username)`)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_users_email ON users (email)`)
			if err != nil {
				return err
			}

			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_users_role ON users (role)`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] User")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.User)(nil)).
				IfExists().
				Exec(ctx)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

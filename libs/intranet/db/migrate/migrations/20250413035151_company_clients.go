package migrations

import (
	"context"
	"fmt"

	"sa-intranet/usecase/cqm/model"

	"github.com/uptrace/bun"
)

func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [up migration] CompanyClient")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			// Create the table
			_, err := tx.NewCreateTable().
				Model((*model.CompanyClient)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add B-tree index for name searches (case-insensitive)
			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_company_clients_name_lower 
                ON company_clients (LOWER(name));
            `)
			if err != nil {
				return err
			}

			// Add GiST index for pattern matching (LIKE queries)
			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_company_clients_name_trgm 
                ON company_clients USING gist (name gist_trgm_ops);
            `)
			if err != nil {
				return err
			}

			// Add index for timestamp range queries
			_, err = tx.ExecContext(ctx, `
                CREATE INDEX idx_company_clients_created_at 
                ON company_clients(created_at);
            `)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		fmt.Print(" [down migration] CompanyClient")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.CompanyClient)(nil)).
				IfExists().
				Exec(ctx)

			return err
		})
	})
}

package migrations

import (
	"context"
	"log"

	"sa-intranet/usecase/cqm/model"

	"github.com/uptrace/bun"
)

//nolint:gochecknoinits
func init() {
	Migrations.MustRegister(func(ctx context.Context, db *bun.DB) error {
		log.Print(" [up migration] JiraProject")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewCreateTable().
				Model((*model.JiraProject)(nil)).
				Exec(ctx)
			if err != nil {
				return err
			}

			// Add indexes for better query performance

			// Index on name for name-based searches
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_name ON jira_projects (name)`)
			if err != nil {
				return err
			}

			// Index on project_id for lookups by project ID
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_project_id ON jira_projects (project_id)`)
			if err != nil {
				return err
			}

			// Index on project_key for lookups by project key
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_project_key ON jira_projects (project_key)`)
			if err != nil {
				return err
			}

			// Index on company_client_id for the foreign key relationship
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_company_client_id ON jira_projects (company_client_id)`)
			if err != nil {
				return err
			}

			// GIN index for the jsonb meta field
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_meta ON jira_projects USING GIN (meta)`)
			if err != nil {
				return err
			}

			// Composite index for company_client_id and project_key
			_, err = tx.ExecContext(ctx, `CREATE INDEX idx_jira_projects_company_client_id_project_key ON jira_projects (company_client_id, project_key)`)
			if err != nil {
				return err
			}

			return nil
		})
	}, func(ctx context.Context, db *bun.DB) error {
		log.Print(" [down migration] JiraProject")

		return db.RunInTx(ctx, nil, func(ctx context.Context, tx bun.Tx) error {
			_, err := tx.NewDropTable().
				Model((*model.JiraProject)(nil)).
				IfExists().
				Exec(ctx)
			if err != nil {
				return err
			}

			return nil
		})
	})
}

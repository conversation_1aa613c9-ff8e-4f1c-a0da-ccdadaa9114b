package core_test

import (
	"context"
	"encoding/base64"
	"testing"

	"sa-intranet/core"
	"sa-intranet/core/cache"

	"github.com/samber/do"
)

func TestRegister(t *testing.T) {
	tests := []struct {
		name    string
		config  core.CypherConfig
		wantErr bool
		verify  func(t *testing.T, injector *do.Injector)
	}{
		{
			name: "successful registration with valid config",
			config: core.CypherConfig{
				Key: base64.StdEncoding.EncodeToString([]byte("1234567890123456")), // 16 bytes
			},
			wantErr: false,
			verify: func(t *testing.T, injector *do.Injector) {
				// Verify CypherConfig is registered
				config, err := do.Invoke[core.CypherConfig](injector)
				if err != nil {
					t.Errorf("Failed to invoke CypherConfig: %v", err)
				}
				if config.Key == "" {
					t.<PERSON><PERSON><PERSON>("CypherConfig.Key is empty")
				}

				// Verify cache is registered
				_, err = do.Invoke[cache.Cache](injector)
				if err != nil {
					t.Errorf("Failed to invoke Cache: %v", err)
				}

				// Verify cypher is registered
				_, err = do.Invoke[core.Cypher](injector)
				if err != nil {
					t.Errorf("Failed to invoke Cypher: %v", err)
				}
			},
		},
		{
			name: "registration with invalid cypher config",
			config: core.CypherConfig{
				Key: "invalid-base64-key",
			},
			wantErr: false, // Register itself doesn't fail, but invoking Cypher will fail
			verify: func(t *testing.T, injector *do.Injector) {
				// Verify that invoking Cypher fails with invalid config
				_, err := do.Invoke[core.Cypher](injector)
				if err == nil {
					t.Errorf("Expected error when invoking Cypher with invalid config, got nil")
				}
			},
		},
		{
			name: "registration with empty config",
			config: core.CypherConfig{
				Key: "",
			},
			wantErr: false, // Register itself doesn't fail
			verify: func(t *testing.T, injector *do.Injector) {
				// Verify that invoking Cypher fails with empty config
				_, err := do.Invoke[core.Cypher](injector)
				if err == nil {
					t.Errorf("Expected error when invoking Cypher with empty config, got nil")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			injector := do.New()

			err := core.Register(injector, tt.config)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Register() expected error, got nil")
					return
				}
				return
			}

			if err != nil {
				t.Errorf("Register() unexpected error = %v", err)
				return
			}

			// Run verification if provided
			if tt.verify != nil {
				tt.verify(t, injector)
			}
		})
	}
}

func TestConstants(t *testing.T) {
	tests := []struct {
		name     string
		constant string
		expected string
	}{
		{
			name:     "ErrFormatWithCause",
			constant: core.ErrFormatWithCause,
			expected: "%w: %w",
		},
		{
			name:     "ErrFormatWithString",
			constant: core.ErrFormatWithString,
			expected: "%w: %s",
		},
		{
			name:     "ErrFormatWithType",
			constant: core.ErrFormatWithType,
			expected: "%w: %T",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.constant != tt.expected {
				t.Errorf("Constant %s = %v, want %v", tt.name, tt.constant, tt.expected)
			}
		})
	}
}

func TestRegister_ServiceIntegration(t *testing.T) {
	// Test that all registered services work together
	injector := do.New()
	config := core.CypherConfig{
		Key: base64.StdEncoding.EncodeToString([]byte("1234567890123456")), // 16 bytes
	}

	err := core.Register(injector, config)
	if err != nil {
		t.Fatalf("Register() failed: %v", err)
	}

	// Test that cache works
	cache, err := do.Invoke[cache.Cache](injector)
	if err != nil {
		t.Fatalf("Failed to invoke Cache: %v", err)
	}

	// Test cache functionality
	ctx := context.Background()
	testKey := "test-key"
	testValue := []byte("test-value")

	err = cache.Set(ctx, testKey, testValue, 0)
	if err != nil {
		t.Errorf("Cache.Set() failed: %v", err)
	}

	retrievedValue, err := cache.Get(ctx, testKey)
	if err != nil {
		t.Errorf("Cache.Get() failed: %v", err)
	}

	if string(retrievedValue) != string(testValue) {
		t.Errorf("Cache round trip failed: got %v, want %v", string(retrievedValue), string(testValue))
	}

	// Test that cypher works
	cypher, err := do.Invoke[core.Cypher](injector)
	if err != nil {
		t.Fatalf("Failed to invoke Cypher: %v", err)
	}

	// Test cypher functionality
	plaintext := "test message"
	encrypted, err := cypher.Encrypt(plaintext)
	if err != nil {
		t.Errorf("Cypher.Encrypt() failed: %v", err)
	}

	decrypted, err := cypher.Decrypt(encrypted)
	if err != nil {
		t.Errorf("Cypher.Decrypt() failed: %v", err)
	}

	if decrypted != plaintext {
		t.Errorf("Cypher round trip failed: got %v, want %v", decrypted, plaintext)
	}
}

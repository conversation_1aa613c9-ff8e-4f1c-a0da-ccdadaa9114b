package cache_test

import (
	"context"
	"testing"
	"time"

	"sa-intranet/core/cache"

	"github.com/samber/do"
)

func TestDefaultCache(t *testing.T) {
	t.<PERSON>llel()

	tests := []struct {
		name     string
		testFunc func(*testing.T, cache.Cache)
	}{
		{
			name: "Clear",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				ctx := context.Background()

				// Set multiple keys
				err := c.Set(ctx, "clear-key1", []byte("clear-value1"), 0)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				err = c.Set(ctx, "clear-key2", []byte("clear-value2"), 0)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				// Clear the cache
				err = c.Clear(ctx)
				if err != nil {
					t.Fatalf("Clear() error = %v", err)
				}

				// Keys should not exist anymore
				_, err = c.Get(ctx, "clear-key1")
				if err != cache.ErrKeyNotFound {
					t.<PERSON>rrorf("Get() after Clear() error = %v, want %v", err, cache.ErrKeyNotFound)
				}

				_, err = c.Get(ctx, "clear-key2")
				if err != cache.ErrKeyNotFound {
					t.Errorf("Get() after Clear() error = %v, want %v", err, cache.ErrKeyNotFound)
				}
			},
		},
		{
			name: "Set and Get",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				ctx := context.Background()
				key := "test-key"
				value := []byte("test-value")

				err := c.Set(ctx, key, value, 0)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				got, err := c.Get(ctx, key)
				if err != nil {
					t.Fatalf("Get() error = %v", err)
				}

				if string(got) != string(value) {
					t.Errorf("Get() = %v, want %v", string(got), string(value))
				}
			},
		},
		{
			name: "Get non-existent key",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				ctx := context.Background()
				_, err := c.Get(ctx, "non-existent-key")
				if err != cache.ErrKeyNotFound {
					t.Errorf("Get() error = %v, want %v", err, cache.ErrKeyNotFound)
				}
			},
		},
		{
			name: "Set with expiration",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()
				ctx := context.Background()
				key := "expiring-key"
				value := []byte("expiring-value")

				err := c.Set(ctx, key, value, 50*time.Millisecond)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				// Should be available immediately
				_, err = c.Get(ctx, key)
				if err != nil {
					t.Errorf("Get() error = %v, want nil", err)
				}

				// Wait for expiration
				time.Sleep(80 * time.Millisecond)

				// Should be expired now
				_, err = c.Get(ctx, key)
				if err != cache.ErrKeyExpired {
					t.Errorf("Get() error = %v, want %v", err, cache.ErrKeyExpired)
				}
			},
		},
		{
			name: "Delete",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				ctx := context.Background()
				key := "delete-key"
				value := []byte("delete-value")

				err := c.Set(ctx, key, value, 0)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				err = c.Delete(ctx, key)
				if err != nil {
					t.Fatalf("Delete() error = %v", err)
				}

				_, err = c.Get(ctx, key)
				if err != cache.ErrKeyNotFound {
					t.Errorf("Get() after Delete() error = %v, want %v", err, cache.ErrKeyNotFound)
				}
			},
		},
		{
			name: "Exists",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				ctx := context.Background()
				key := "exists-key"
				value := []byte("exists-value")

				// Key should not exist initially
				exists, err := c.Exists(ctx, key)
				if err != nil {
					t.Fatalf("Exists() error = %v", err)
				}
				if exists {
					t.Errorf("Exists() = %v, want %v", exists, false)
				}

				// Set the key
				err = c.Set(ctx, key, value, 0)
				if err != nil {
					t.Fatalf("Set() error = %v", err)
				}

				// Key should exist now
				exists, err = c.Exists(ctx, key)
				if err != nil {
					t.Fatalf("Exists() error = %v", err)
				}
				if !exists {
					t.Errorf("Exists() = %v, want %v", exists, true)
				}
			},
		},
		{
			name: "Context cancellation",
			testFunc: func(t *testing.T, c cache.Cache) {
				t.Helper()

				// Create a cancelled context
				ctx, cancel := context.WithCancel(context.Background())
				cancel()

				// All operations should fail with context error
				err := c.Set(ctx, "ctx-key", []byte("ctx-value"), 0)
				if err == nil {
					t.Errorf("Set() with cancelled context should return error")
				}

				_, err = c.Get(ctx, "ctx-key")
				if err == nil {
					t.Errorf("Get() with cancelled context should return error")
				}

				err = c.Delete(ctx, "ctx-key")
				if err == nil {
					t.Errorf("Delete() with cancelled context should return error")
				}

				_, err = c.Exists(ctx, "ctx-key")
				if err == nil {
					t.Errorf("Exists() with cancelled context should return error")
				}

				err = c.Clear(ctx)
				if err == nil {
					t.Errorf("Clear() with cancelled context should return error")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// t.Parallel()
			// Do not parallelize these test cases.
			// The "Clear" test case removes all keys from the cache, which can interfere with
			// other tests like "Set with expiration" and "Exists" that rely on existing keys.
			// Running them in parallel may cause flaky tests.

			cache := do.MustInvoke[cache.Cache](injector)
			tt.testFunc(t, cache)
		})
	}
}

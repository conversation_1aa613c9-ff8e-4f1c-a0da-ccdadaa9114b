package cache

import (
	"context"
	"time"
)

// Cache defines the interface for cache operations
type Cache interface {
	// Get retrieves a value from cache by key
	Get(ctx context.Context, key string) ([]byte, error)

	// Set stores a value in cache with optional expiration
	Set(ctx context.Context, key string, value []byte, expiration time.Duration) error

	// Delete removes a key from cache
	Delete(ctx context.Context, key string) error

	// Exists checks if a key exists in cache
	Exists(ctx context.Context, key string) (bool, error)

	// Clear removes all keys from cache
	Clear(ctx context.Context) error
}

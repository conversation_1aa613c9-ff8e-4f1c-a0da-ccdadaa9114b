package core

import (
	"sa-intranet/core/cache"

	"github.com/samber/do"
)

const (
	ErrFormatWithCause  = "%w: %w"
	ErrFormatWithString = "%w: %s"
	ErrFormatWithType   = "%w: %T"
)

func Register(i *do.Injector, conf CypherConfig) error {
	// Register cypher config
	do.Provide(i, func(i *do.Injector) (CypherConfig, error) {
		return conf, nil
	})

	// Register cache
	do.Provide(i, cache.NewDefaultCache)

	do.Provide(i, NewAESCipher)

	return nil
}

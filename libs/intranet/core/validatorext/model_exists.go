package validatorext

import (
	"context"

	"sa-intranet/usecase/cqm/model"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

// CustomValidator holds the DB connection
type CustomValidator struct {
	DB *bun.DB
}

// ValidateModelExists dynamically checks if a model with the given UUID exists in the DB.
func (cv *CustomValidator) ValidateModelExists(modelName string) validator.FuncCtx {
	return func(ctx context.Context, fl validator.FieldLevel) bool {
		id, ok := fl.Field().Interface().(uuid.UUID)
		if !ok {
			return false
		}

		var modelInstance any

		switch modelName {
		case "CompanyClient":
			modelInstance = &model.CompanyClient{}
		case "JiraProject":
			modelInstance = &model.JiraProject{}
		default:
			return false
		}

		exists, err := cv.DB.NewSelect().Model(modelInstance).Where("id = ?", id).Exists(ctx)
		if err != nil {
			// Log or handle database error here
			return false
		}

		return exists
	}
}

func ValidateModelWithKeyAlredyExists[ColumnType any](db *bun.DB, modelInstance any, columnName string) validator.FuncCtx {
	return func(ctx context.Context, fl validator.FieldLevel) bool {
		columnValue, ok := fl.Field().Interface().(ColumnType)
		if !ok {
			return false
		}

		exists, err := db.NewSelect().Model(modelInstance).Where(columnName+" = ?", columnValue).Exists(ctx)
		if err != nil {
			// Log or handle database error here
			return false
		}

		return !exists
	}
}

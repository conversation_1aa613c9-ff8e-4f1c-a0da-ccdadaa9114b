package validatorext_test

import (
	"reflect"
	"testing"

	"sa-intranet/core/validatorext"

	"github.com/go-playground/validator/v10"
)

// Test struct for validation
type TestStruct struct {
	Name     string `json:"name" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
	Age      int    `json:"age" validate:"min=18,max=100"`
	Username string `json:"username" validate:"required,min=3"`
}

func TestFormatFormErrors(t *testing.T) {
	validate := validator.New()

	tests := []struct {
		name           string
		structInstance any
		input          any
		expectedErrors map[string]any
		wantErr        bool
	}{
		{
			name:           "valid struct - no errors",
			structInstance: TestStruct{},
			input: TestStruct{
				Name:     "<PERSON>",
				Email:    "<EMAIL>",
				Age:      25,
				Username: "johndoe",
			},
			expectedErrors: map[string]any{},
			wantErr:        false,
		},
		{
			name:           "required field missing",
			structInstance: TestStruct{},
			input: TestStruct{
				Email:    "<EMAIL>",
				Age:      25,
				Username: "johndo<PERSON>",
			},
			expectedErrors: map[string]any{
				"name": "Required",
			},
			wantErr: true,
		},
		{
			name:           "invalid email format",
			structInstance: TestStruct{},
			input: TestStruct{
				Name:     "John Doe",
				Email:    "invalid-email",
				Age:      25,
				Username: "johndoe",
			},
			expectedErrors: map[string]any{
				"email": "Email is invalid",
			},
			wantErr: true,
		},
		{
			name:           "age below minimum",
			structInstance: TestStruct{},
			input: TestStruct{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Age:      15,
				Username: "johndoe",
			},
			expectedErrors: map[string]any{
				"age": "Minimum is 18 characters",
			},
			wantErr: true,
		},
		{
			name:           "age above maximum",
			structInstance: TestStruct{},
			input: TestStruct{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Age:      150,
				Username: "johndoe",
			},
			expectedErrors: map[string]any{
				"age": "Maximum is 100 characters",
			},
			wantErr: true,
		},
		{
			name:           "username too short",
			structInstance: TestStruct{},
			input: TestStruct{
				Name:     "John Doe",
				Email:    "<EMAIL>",
				Age:      25,
				Username: "jo",
			},
			expectedErrors: map[string]any{
				"username": "Minimum is 3 characters",
			},
			wantErr: true,
		},
		{
			name:           "multiple validation errors",
			structInstance: TestStruct{},
			input: TestStruct{
				Email:    "invalid-email",
				Age:      15,
				Username: "jo",
			},
			expectedErrors: map[string]any{
				"name":     "Required",
				"email":    "Email is invalid",
				"age":      "Minimum is 18 characters",
				"username": "Minimum is 3 characters",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validate.Struct(tt.input)

			if !tt.wantErr {
				if err != nil {
					t.Errorf("Expected no validation errors, got: %v", err)
				}
				return
			}

			if err == nil {
				t.Errorf("Expected validation errors, got nil")
				return
			}

			validationErrors, ok := err.(validator.ValidationErrors)
			if !ok {
				t.Errorf("Expected validator.ValidationErrors, got %T", err)
				return
			}

			formErrors := validatorext.FormatFormErrors(tt.structInstance, validationErrors)

			if !reflect.DeepEqual(formErrors, tt.expectedErrors) {
				t.Errorf("FormatFormErrors() = %v, want %v", formErrors, tt.expectedErrors)
			}
		})
	}
}

func TestFormatFormErrors_SpecialCases(t *testing.T) {
	validate := validator.New()

	// Test struct with custom validation tags
	type SpecialStruct struct {
		JiraProject    string `json:"jira_project" validate:"jira_project_exists"`
		CompanyClient  string `json:"company_client" validate:"company_client_exists"`
		Website        string `json:"website" validate:"url"`
		CustomField    string `json:"custom_field" validate:"custom_validation"`
	}

	tests := []struct {
		name           string
		structInstance any
		input          any
		expectedErrors map[string]any
	}{
		{
			name:           "jira project validation error",
			structInstance: SpecialStruct{},
			input: SpecialStruct{
				JiraProject: "invalid-project",
			},
			expectedErrors: map[string]any{
				"jira_project": "Jira Project does not exists",
			},
		},
		{
			name:           "company client validation error",
			structInstance: SpecialStruct{},
			input: SpecialStruct{
				CompanyClient: "invalid-client",
			},
			expectedErrors: map[string]any{
				"company_client": "Company Client does not exists",
			},
		},
		{
			name:           "url validation error",
			structInstance: SpecialStruct{},
			input: SpecialStruct{
				Website: "not-a-url",
			},
			expectedErrors: map[string]any{
				"website": "URL is invalid",
			},
		},
		{
			name:           "unknown validation error",
			structInstance: SpecialStruct{},
			input: SpecialStruct{
				CustomField: "invalid",
			},
			expectedErrors: map[string]any{
				"custom_field": "Is invalid. Error: Key: 'SpecialStruct.CustomField' Error:Field validation for 'CustomField' failed on the 'custom_validation' tag",
			},
		},
	}

	// Register custom validation that always fails for testing
	validate.RegisterValidation("jira_project_exists", func(fl validator.FieldLevel) bool {
		return false
	})
	validate.RegisterValidation("company_client_exists", func(fl validator.FieldLevel) bool {
		return false
	})
	validate.RegisterValidation("custom_validation", func(fl validator.FieldLevel) bool {
		return false
	})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validate.Struct(tt.input)
			if err == nil {
				t.Errorf("Expected validation error, got nil")
				return
			}

			validationErrors, ok := err.(validator.ValidationErrors)
			if !ok {
				t.Errorf("Expected validator.ValidationErrors, got %T", err)
				return
			}

			formErrors := validatorext.FormatFormErrors(tt.structInstance, validationErrors)

			for expectedField, expectedMessage := range tt.expectedErrors {
				actualMessage, exists := formErrors[expectedField]
				if !exists {
					t.Errorf("Expected error for field %s, but not found", expectedField)
					continue
				}

				if actualMessage != expectedMessage {
					t.Errorf("Field %s: got %v, want %v", expectedField, actualMessage, expectedMessage)
				}
			}
		})
	}
}

func TestFormatFormErrors_StructWithoutJSONTags(t *testing.T) {
	validate := validator.New()

	// Test struct without JSON tags
	type NoJSONStruct struct {
		Name string `validate:"required"`
		Age  int    `validate:"min=18"`
	}

	input := NoJSONStruct{}
	err := validate.Struct(input)
	if err == nil {
		t.Errorf("Expected validation error, got nil")
		return
	}

	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		t.Errorf("Expected validator.ValidationErrors, got %T", err)
		return
	}

	formErrors := validatorext.FormatFormErrors(NoJSONStruct{}, validationErrors)

	// Should fallback to field names when no JSON tags
	expectedErrors := map[string]any{
		"Name": "Required",
		"Age":  "Minimum is 18 characters",
	}

	if !reflect.DeepEqual(formErrors, expectedErrors) {
		t.Errorf("FormatFormErrors() = %v, want %v", formErrors, expectedErrors)
	}
}

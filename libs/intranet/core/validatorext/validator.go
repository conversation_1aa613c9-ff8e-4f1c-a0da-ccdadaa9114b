package validatorext

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
)

func getJSONFieldName(structInstance any, fe validator.FieldError) string {
	t := reflect.TypeOf(structInstance)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	field, ok := t.<PERSON>(fe.StructField())
	if !ok {
		return fe.Field() // fallback to field name
	}

	jsonTag := field.Tag.Get("json")
	if jsonTag == "" {
		return fe.Field()
	}

	return strings.Split(jsonTag, ",")[0]
}

// FormatFormErrors converts validation errors into a map of field names to error messages.
// It uses the JSON field tags from the struct to determine the field names in the output map.
// The error messages are customized based on the validation tag that failed.
func FormatFormErrors(structInstance any, errors []validator.FieldError) map[string]any {
	validationErrors := map[string]any{}

	for _, error := range errors {
		var errorMessage string

		// Customize error messages based on the tag
		switch error.ActualTag() {
		case "required":
			errorMessage = "Required"
		case "min":
			errorMessage = fmt.Sprintf("Minimum is %s characters", error.Param())
		case "max":
			errorMessage = fmt.Sprintf("Maximum is %s characters", error.Param())
		case "email":
			errorMessage = "Email is invalid"
		case "jira_project_exists":
			errorMessage = "Jira Project does not exists"
		case "company_client_exists":
			errorMessage = "Company Client does not exists"
		case "url":
			errorMessage = "URL is invalid"
		default:
			errorMessage = fmt.Sprintf("Is invalid. Error: %s", error.Error())
		}

		jsonKey := getJSONFieldName(structInstance, error)
		validationErrors[jsonKey] = errorMessage
	}

	return validationErrors
}

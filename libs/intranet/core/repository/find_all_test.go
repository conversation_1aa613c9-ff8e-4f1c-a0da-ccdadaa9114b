package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/uptrace/bun"
)

type DummyFilter struct {
	Name string `json:"name"`
}

func TestFindAll(t *testing.T) {
	db := bunDB

	type args struct {
		params    repository.PaginationParams[DummyFilter]
		opts      []repository.FindAllOptionsFunc[DummyFilter]
		setupFunc func(db *bun.DB) error // optional setup function
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *repository.PaginatedResult[DummyModel])
	}{
		{
			name: "successful_find_all",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     1,
					PageSize: 10,
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test successful 1"},
						{Name: "Test successful 2"},
						{Name: "Test successful 3"},
					}
					_, err := db.NewInsert().Model(&models).Exec(context.Background())
					return err
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						names := []string{
							"Test successful 1",
							"Test successful 2",
							"Test successful 3",
						}
						q.Where("name IN (?)", bun.In(names))
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 3 {
					t.Errorf("expected 3 items, got %d", len(result.Items))
				}
				if result.TotalItems != 3 {
					t.Errorf("expected total of 3 items, got %d", result.TotalItems)
				}
				if result.Page != 1 {
					t.Errorf("expected page 1, got %d", result.Page)
				}
			},
		},
		{
			name: "pagination_works",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     2,
					PageSize: 2,
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test Item 1"},
						{Name: "Test Item 2"},
						{Name: "Test Item 3"},
						{Name: "Test Item 4"},
						{Name: "Test Item 5"},
					}
					_, err := db.NewInsert().Model(&models).Exec(context.Background())
					return err
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						names := []string{
							"Test Item 1",
							"Test Item 2",
							"Test Item 3",
							"Test Item 4",
							"Test Item 5",
						}
						q.Where("name IN (?)", bun.In(names))
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()

				if len(result.Items) != 2 {
					t.Errorf("expected 2 items on page 2, got %d", len(result.Items))
				}

				if result.TotalItems != 5 {
					t.Errorf("expected total of 5 items, got %d", result.TotalItems)
				}

				if result.TotalPages != 3 {
					t.Errorf("expected 3 total pages, got %d", result.TotalPages)
				}
			},
		},
		{
			name: "find_with_query_builder",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     1,
					PageSize: 10,
					Filters: DummyFilter{
						Name: "Test 1000",
					},
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						if params.Filters.Name != "" {
							q.Where("name = ?", params.Filters.Name)
						}
					}),
				},
				setupFunc: func(db *bun.DB) error {
					models := []DummyModel{
						{Name: "Test 1000"},
						{Name: "Test 2"},
						{Name: "Test 3"},
					}
					_, err := db.NewInsert().
						Model(&models).
						Returning("*").
						Exec(context.Background())
					if err != nil {
						t.Errorf("failed to insert initial records: %v", err)
					}
					return nil
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 1 {
					t.Errorf("expected 1 filtered item, got %d", len(result.Items))
				}
				if result.Items[0].Name != "Test 1000" {
					t.Errorf("expected 'Test 1000', got %s", result.Items[0].Name)
				}
			},
		},
		{
			name: "invalid_pagination_params",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Page:     0, // Invalid page number
					PageSize: 10,
				},
			},
			wantErr: true,
		},
		{
			name: "empty_result",
			args: args{
				params: repository.PaginationParams[DummyFilter]{
					Filters: DummyFilter{
						Name: "Nonexistent",
					},
					Page:     1,
					PageSize: 10,
				},
				opts: []repository.FindAllOptionsFunc[DummyFilter]{
					repository.WithFindAllQueryBuilder(func(q *bun.SelectQuery, params repository.PaginationParams[DummyFilter]) {
						if params.Filters.Name != "" {
							q.Where("name = ?", params.Filters.Name)
						}
					}),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, result *repository.PaginatedResult[DummyModel]) {
				t.Helper()
				if len(result.Items) != 0 {
					t.Errorf("expected empty result, got %d items", len(result.Items))
				}
				if result.TotalItems != 0 {
					t.Errorf("expected 0 total items, got %d", result.TotalItems)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Run setup if provided
			if tt.args.setupFunc != nil {
				if err := tt.args.setupFunc(db); err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			result, err := repository.FindAll[DummyModel, DummyFilter](
				context.Background(),
				db,
				tt.args.params,
				tt.args.opts...,
			)

			if (err != nil) != tt.wantErr {
				t.Errorf("FindAll() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, result)
			}
		})
	}
}

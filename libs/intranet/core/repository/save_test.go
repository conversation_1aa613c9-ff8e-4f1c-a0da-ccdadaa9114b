package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/uptrace/bun"
)

func TestSave(t *testing.T) {
	db := bunDB
	type args struct {
		model     *DummyModel
		opts      []repository.SaveOptionsFunc
		setupFunc func(db *bun.DB) error // optional setup function
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *bun.DB, *DummyModel) // verification function
	}{
		{
			name: "successful_insert",
			args: args{
				model: &DummyModel{
					Name: "Test Dummy",
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				// Verify the record was saved
				var saved DummyModel
				err := db.NewSelect().Model(&saved).Where("id = ?", model.ID).Scan(context.Background())
				if err != nil {
					t.<PERSON>("failed to fetch saved record: %v", err)
				}

				if saved.Name != model.Name {
					t.Errorf("got name %v, want %v", saved.Name, model.Name)
				}
			},
		},
		{
			name: "nil_model",
			args: args{
				model: nil,
			},
			wantErr: true,
		},
		{
			name: "upsert_existing_record",
			args: args{
				model: &DummyModel{
					ID:   "existing-id",
					Name: "Test Dummy",
				},
				opts: []repository.SaveOptionsFunc{
					repository.WithSaveUpsert(true),
				},
				setupFunc: func(db *bun.DB) error {
					// Insert initial record
					_, err := db.NewInsert().
						Model(&DummyModel{
							ID:   "existing-id",
							Name: "Original Name",
						}).
						Returning("*").
						Exec(context.Background())
					if err != nil {
						t.Errorf("failed to insert initial record: %v", err)
					}
					return nil
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				var count int
				count, err := db.NewSelect().
					Model(model).
					Where("id = ?", model.ID).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}

				if count != 1 {
					t.Errorf("got %d records, want 1", count)
				}
			},
		},
		{
			name: "save_with_custom_id_column",
			args: args{
				model: &DummyModel{
					Name: "Custom ID Test",
				},
				opts: []repository.SaveOptionsFunc{
					repository.WithSaveIDColumnName("id"),
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, model *DummyModel) {
				t.Helper()

				if model.ID == "" {
					t.Error("IDwas not set")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Run setup if provided
			if tt.args.setupFunc != nil {
				if err := tt.args.setupFunc(db); err != nil {
					t.Fatalf("setup failed: %v", err)
				}
			}

			err := repository.Save(context.Background(), db, tt.args.model, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, tt.args.model)
			}
		})
	}
}

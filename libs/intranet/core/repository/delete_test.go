package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

func TestDelete(t *testing.T) {
	db := bunDB

	type args struct {
		id        uuid.UUID
		opts      []repository.DeleteOptionsFunc
		setupFunc func(db *bun.DB) (*DummyModel, error) // optional setup function
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *bun.DB, uuid.UUID)
	}{
		{
			name: "successful_delete",
			args: args{
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Test Delete",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())
					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, id uuid.UUID) {
				t.Helper()
				var count int
				count, err := db.NewSelect().
					Model((*DummyModel)(nil)).
					Where("id = ?", id).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}
				if count != 0 {
					t.Errorf("record still exists after deletion")
				}
			},
		},
		{
			name: "delete_non_existent",
			args: args{
				id: uuid.New(), // Random non-existent ID
			},
			wantErr: true,
		},
		{
			name: "delete_with_custom_id_column",
			args: args{
				opts: []repository.DeleteOptionsFunc{
					repository.WithDeleteIDColumnName("id"),
				},
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Custom ID Test",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())
					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, id uuid.UUID) {
				t.Helper()
				var count int
				count, err := db.NewSelect().
					Model((*DummyModel)(nil)).
					Where("id = ?", id).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}
				if count != 0 {
					t.Errorf("record still exists after deletion")
				}
			},
		},
		{
			name: "delete_with_query_builder",
			args: args{
				opts: []repository.DeleteOptionsFunc{
					repository.WithDeleteQueryBuilder(func(q *bun.DeleteQuery) {
						q.Where("name = ?", "Test Delete")
					}),
				},
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Test Delete",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())
					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, db *bun.DB, id uuid.UUID) {
				t.Helper()
				var count int
				count, err := db.NewSelect().
					Model((*DummyModel)(nil)).
					Where("id = ?", id).
					Count(context.Background())
				if err != nil {
					t.Errorf("failed to count records: %v", err)
				}
				if count != 0 {
					t.Errorf("record still exists after deletion")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var testModel *DummyModel
			var err error

			// Run setup if provided
			if tt.args.setupFunc != nil {
				testModel, err = tt.args.setupFunc(db)
				if err != nil {
					t.Fatalf("setup failed: %v", err)
				}
				// Use the ID from the created model
				tt.args.id = uuid.MustParse(testModel.ID)
			}

			err = repository.Delete[DummyModel](context.Background(), db, tt.args.id, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Delete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, db, tt.args.id)
			}
		})
	}
}

package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

type FindOptions struct {
	IDColumnName string
	Base         BaseOptions
}

type FindOptionsFunc func(opts *FindOptions)

func WithFindIDColumnName(idColumnName string) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.IDColumnName = idColumnName
	}
}

func WithQueryBuilder(queryBuilderFunc QueryBuilderFunc) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.Base.QueryBuilderFunc = queryBuilderFunc
	}
}

func WithDefaultQueryBuilder(defaultQueryBuilderFunc QueryBuilderFunc) FindOptionsFunc {
	return func(opts *FindOptions) {
		opts.Base.DefaultQueryBuilderFunc = defaultQueryBuilderFunc
	}
}

func Find[T any](ctx context.Context, db bun.IDB, id uuid.UUID, opts ...FindOptionsFunc) (*T, error) {
	model := new(T)

	options := &FindOptions{
		IDColumnName: "id",
	}

	for _, opt := range opts {
		opt(options)
	}

	query := db.NewSelect().Model(model)

	if options.Base.DefaultQueryBuilderFunc != nil {
		options.Base.DefaultQueryBuilderFunc(query)
	}

	if options.Base.QueryBuilderFunc != nil {
		options.Base.QueryBuilderFunc(query)
	}

	err := query.
		Where(fmt.Sprintf("%s = ?", options.IDColumnName), id).
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return model, nil
}

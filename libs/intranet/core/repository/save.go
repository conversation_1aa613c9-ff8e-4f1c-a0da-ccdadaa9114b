package repository

import (
	"context"
	"errors"
	"fmt"
	"log/slog"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect"
)

// Define static errors
var (
	ErrModelNil       = errors.New("model cannot be nil")
	ErrNoRowsAffected = errors.New("no rows affected")
)

type SaveOptions struct {
	IDColumnName      string
	IsUpsert          bool
	IsNew             bool
	UpdateFields      []string
	AllowUpdateFields []string
	Base              BaseOptions
}

type SaveOptionsFunc func(opts *SaveOptions)

func WithSaveAllowUpdateFields(fields []string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.AllowUpdateFields = fields
	}
}

func WithSaveUpdateFields(fields []string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.UpdateFields = fields
	}
}

func WithSaveUpsert(isUpsert bool) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IsUpsert = isUpsert
	}
}

func WithSaveNew(isNew bool) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IsNew = isNew
	}
}

func WithSaveIDColumnName(idColumnName string) SaveOptionsFunc {
	return func(opts *SaveOptions) {
		opts.IDColumnName = idColumnName
	}
}

// Save persists a model to the database
func Save[T any](ctx context.Context, db bun.IDB, model *T, opts ...SaveOptionsFunc) error {
	options := &SaveOptions{
		IDColumnName: "id",
		IsNew:        true,
		IsUpsert:     false,
		UpdateFields: []string{},
	}

	if model == nil {
		return ErrModelNil
	}

	for _, opt := range opts {
		opt(options)
	}

	if options.IsNew || options.IsUpsert {
		return saveInsert(ctx, db, model, options)
	}

	return saveUpdate(ctx, db, model, options)
}

func saveInsert[T any](ctx context.Context, db bun.IDB, model *T, options *SaveOptions) error {
	inserQuery := db.NewInsert().Model(model)

	if options.IsUpsert {
		inserQuery = handleUpsert(db, inserQuery, options.IDColumnName)
	}

	_, err := inserQuery.
		Returning("*").
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to insert model: %w", err)
	}

	return nil
}

func handleUpsert(db bun.IDB, query *bun.InsertQuery, idColumnName string) *bun.InsertQuery {
	dialectName := db.Dialect().Name()
	switch dialectName {
	case dialect.PG:
		return query.On(fmt.Sprintf("CONFLICT (%s) DO UPDATE SET EXCLUDED.*", idColumnName))
	case dialect.SQLite:
		return query.Replace()
	case dialect.MySQL, dialect.MSSQL, dialect.Oracle:
		return query
	case dialect.Invalid:
		slog.Warn(fmt.Sprintf("Invalid dialect for upsert: %s", dialectName))
		return query
	default:
		slog.Warn(fmt.Sprintf("Unsupported dialect for upsert: %s", dialectName))
		return query
	}
}

func saveUpdate[T any](ctx context.Context, db bun.IDB, model *T, options *SaveOptions) error {
	updateQuery := db.NewUpdate().
		Model(model).
		WherePK().
		Returning("*")

	if len(options.UpdateFields) > 0 && len(options.AllowUpdateFields) > 0 {
		updateQuery = applyAllowedFields(updateQuery, options)
	}

	res, err := updateQuery.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update model: %w", err)
	}

	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrNoRowsAffected
	}

	return nil
}

func applyAllowedFields(query *bun.UpdateQuery, options *SaveOptions) *bun.UpdateQuery {
	allowedMap := make(map[string]bool)
	for _, field := range options.AllowUpdateFields {
		allowedMap[field] = true
	}

	var allowedFields []string

	for _, field := range options.UpdateFields {
		if allowedMap[field] {
			allowedFields = append(allowedFields, field)
		}
	}

	if len(allowedFields) > 0 {
		return query.Column(allowedFields...)
	}

	return query
}

package repository_test

import (
	"context"
	"database/sql"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/sqlitedialect"
	"github.com/uptrace/bun/driver/sqliteshim"
)

var bunDB *bun.DB

func TestMain(m *testing.M) {
	// Run tests
	// Setup fresh database for each test
	bunDB = newDB()
	defer bunDB.Close()

	if err := Migrate(bunDB); err != nil {
		panic(fmt.Sprintf("failed to migrate: %v", err))
	}

	m.Run()
}

type DummyModel struct {
	bun.BaseModel `bun:"table:dummies,alias:d"`

	ID        string    `bun:"id,pk,type:uuid"`
	Name      string    `bun:"name,notnull"`
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"`
}

func (d *DummyModel) BeforeAppendModel(ctx context.Context, query bun.Query) error {
	switch query.(type) {
	case *bun.InsertQuery:
		if d.ID == "" {
			d.ID = uuid.New().String()
		}

		d.CreatedAt = time.Now()
		d.UpdatedAt = time.Now()
	case *bun.UpdateQuery:
		d.UpdatedAt = time.Now()
	}

	return nil
}

func newDB() *bun.DB {
	sqldb, err := sql.Open(sqliteshim.ShimName, "file::memory:?cache=shared")
	if err != nil {
		panic(fmt.Sprintf("failed to open database: %v", err))
	}

	db := bun.NewDB(sqldb, sqlitedialect.New())

	return db
}

func Migrate(db *bun.DB) error {
	ctx := context.Background()

	if _, err := db.NewCreateTable().IfNotExists().Model((*DummyModel)(nil)).Exec(ctx); err != nil {
		return fmt.Errorf("failed to create table: %w", err)
	}

	if err := db.ResetModel(ctx, (*DummyModel)(nil)); err != nil {
		return fmt.Errorf("failed to reset model: %w", err)
	}

	return nil
}

package repository

import (
	"context"
	"errors"
	"fmt"

	"github.com/uptrace/bun"
)

// Define static errors
var (
	ErrInvalidPaginationParams = errors.New("invalid pagination parameters")
)

// PaginationParams defines the parameters for pagination
type PaginationParams[T any] struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	// Optional fields for sorting or filtering
	SortBy       string   `json:"sort_by,omitempty"`
	SortOrder    string   `json:"sort_order,omitempty"`
	Filters      T        `json:"filters,omitempty"`
	Fields       []string `json:"fields,omitempty"`
	AllowFields  []string `json:"-"`
	DisableCount bool     `json:"-"`
}

// PaginatedResult represents a paginated result set with generic item type
type PaginatedResult[T any] struct {
	Items      []T   `json:"items"`
	TotalItems int64 `json:"total_items"`
	TotalPages int   `json:"total_pages"`
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
}

// NewPaginatedResult creates a new PaginatedResult with initialized fields
func NewPaginatedResult[T any](items []T, totalItems int64, page, pageSize int) *PaginatedResult[T] {
	if items == nil {
		items = make([]T, 0) // Initialize as empty slice instead of nil
	}

	totalPages := int(totalItems) / pageSize
	if int(totalItems)%pageSize > 0 {
		totalPages++
	}

	return &PaginatedResult[T]{
		Items:      items,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   pageSize,
	}
}

func (p *PaginatedResult[T]) IsEmpty() bool {
	return len(p.Items) == 0
}

func (p *PaginatedResult[T]) First() (T, bool) {
	var zero T
	if p.IsEmpty() {
		return zero, false
	}

	return p.Items[0], true
}

func (p *PaginatedResult[T]) Last() (T, bool) {
	var zero T
	if p.IsEmpty() {
		return zero, false
	}

	return p.Items[len(p.Items)-1], true
}

type FindAllQueryBuilderFunc[F any] func(query *bun.SelectQuery, params PaginationParams[F])

type FindAllOptions[F any] struct {
	Base                    BaseOptions
	FindAllQueryBuilderFunc FindAllQueryBuilderFunc[F]
}
type FindAllOptionsFunc[F any] func(opts *FindAllOptions[F])

func WithFindAllQueryBuilder[F any](queryBuilderFunc FindAllQueryBuilderFunc[F]) FindAllOptionsFunc[F] {
	return func(opts *FindAllOptions[F]) {
		opts.FindAllQueryBuilderFunc = queryBuilderFunc
	}
}

// FindAll is a generic function that returns a paginated list of items
func FindAll[T any, F any](
	ctx context.Context,
	db bun.IDB,
	params PaginationParams[F],
	opts ...FindAllOptionsFunc[F],
) (*PaginatedResult[T], error) {
	// Validate pagination parameters
	if params.Page < 1 || params.PageSize < 1 {
		return nil, ErrInvalidPaginationParams
	}

	options := buildFindAllOptions(opts)
	query := buildBaseQuery[T](db, options, params)
	query = applyColumnSelection(query, params)

	items, err := executePaginatedQuery[T](ctx, query, params)
	if err != nil {
		return nil, err
	}

	totalItems, err := getTotalItemsCount(ctx, query, params)
	if err != nil {
		return nil, err
	}

	return buildPaginatedResult(items, totalItems, params), nil
}

func buildFindAllOptions[F any](opts []FindAllOptionsFunc[F]) *FindAllOptions[F] {
	options := &FindAllOptions[F]{}
	for _, opt := range opts {
		opt(options)
	}

	return options
}

func buildBaseQuery[T any, F any](db bun.IDB, options *FindAllOptions[F], params PaginationParams[F]) *bun.SelectQuery {
	query := db.NewSelect().Model((*T)(nil))

	// Apply default query modifications
	if options.Base.DefaultQueryBuilderFunc != nil {
		options.Base.DefaultQueryBuilderFunc(query)
	}

	// Apply the FindAll specific query builder
	if options.FindAllQueryBuilderFunc != nil {
		options.FindAllQueryBuilderFunc(query, params)
	}

	return query
}

func applyColumnSelection[F any](query *bun.SelectQuery, params PaginationParams[F]) *bun.SelectQuery {
	// Apply column selection if Fields and AllowFields are provided
	if len(params.Fields) > 0 && len(params.AllowFields) > 0 {
		allowedFields := filterAllowedFields(params.Fields, params.AllowFields)
		if len(allowedFields) > 0 {
			query = query.Column(allowedFields...)
		}
	}

	return query
}

func filterAllowedFields(requestedFields, allowedFields []string) []string {
	// Create a map of allowed fields for faster lookup
	allowedMap := make(map[string]bool)
	for _, field := range allowedFields {
		allowedMap[field] = true
	}

	// Only include fields that are in the allowed list
	filteredFields := make([]string, 0)

	for _, field := range requestedFields {
		if allowedMap[field] {
			filteredFields = append(filteredFields, field)
		}
	}

	return filteredFields
}

func executePaginatedQuery[T any, F any](ctx context.Context, query *bun.SelectQuery, params PaginationParams[F]) ([]T, error) {
	// Calculate offset for pagination
	offset := (params.Page - 1) * params.PageSize

	items := make([]T, 0)
	paginationQuery := query.
		Limit(params.PageSize).
		Offset(offset)

	if err := paginationQuery.Scan(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to list items in paginationQuery: %w", err)
	}

	return items, nil
}

func getTotalItemsCount[F any](ctx context.Context, query *bun.SelectQuery, params PaginationParams[F]) (int64, error) {
	if params.DisableCount {
		return 0, nil
	}

	countQuery := query.Clone()

	countResult, err := countQuery.Count(ctx)
	if err != nil {
		return 0, err
	}

	return int64(countResult), nil
}

func buildPaginatedResult[T any, F any](items []T, totalItems int64, params PaginationParams[F]) *PaginatedResult[T] {
	// Calculate total pages
	totalPages := int(totalItems) / params.PageSize
	if int(totalItems)%params.PageSize > 0 {
		totalPages++
	}

	return &PaginatedResult[T]{
		Items:      items,
		TotalItems: totalItems,
		TotalPages: totalPages,
		Page:       params.Page,
		PageSize:   params.PageSize,
	}
}

package repository_test

import (
	"context"
	"testing"

	"sa-intranet/core/repository"

	"github.com/google/uuid"
	"github.com/uptrace/bun"
)

func TestFind(t *testing.T) {
	db := bunDB

	type args struct {
		id        uuid.UUID
		opts      []repository.FindOptionsFunc
		setupFunc func(db *bun.DB) (*DummyModel, error) // optional setup function to create test data
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		verify  func(*testing.T, *DummyModel) // verification function
	}{
		{
			name: "successful_find",
			args: args{
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Test Find",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())
					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, model *DummyModel) {
				t.Helper()

				if model == nil {
					t.<PERSON>r("expected model, got nil")
					return
				}

				if model.Name != "Test Find" {
					t.<PERSON>("got name %v, want %v", model.Name, "Test Find")
				}
			},
		},
		{
			name: "not_found",
			args: args{
				id: uuid.New(), // Random non-existent ID
			},
			wantErr: true,
		},
		{
			name: "find_with_custom_id_column",
			args: args{
				opts: []repository.FindOptionsFunc{
					repository.WithFindIDColumnName("id"),
				},
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Custom ID Test",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())
					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, model *DummyModel) {
				t.Helper()

				if model == nil {
					t.Error("expected model, got nil")
					return
				}

				if model.Name != "Custom ID Test" {
					t.Errorf("got name %v, want %v", model.Name, "Custom ID Test")
				}
			},
		},
		{
			name: "find_with_query_builder",
			args: args{
				opts: []repository.FindOptionsFunc{
					repository.WithQueryBuilder(func(q *bun.SelectQuery) {
						q.Where("name = ?", "Test Find")
					}),
				},
				setupFunc: func(db *bun.DB) (*DummyModel, error) {
					model := &DummyModel{
						Name: "Test Find",
					}
					_, err := db.NewInsert().Model(model).Exec(context.Background())

					return model, err
				},
			},
			wantErr: false,
			verify: func(t *testing.T, model *DummyModel) {
				t.Helper()

				if model == nil {
					t.Error("expected model, got nil")
					return
				}

				if model.Name != "Test Find" {
					t.Errorf("got name %v, want %v", model.Name, "Test Find")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var testModel *DummyModel
			var err error

			// Run setup if provided
			if tt.args.setupFunc != nil {
				testModel, err = tt.args.setupFunc(db)
				if err != nil {
					t.Fatalf("setup failed: %v", err)
				}
				// Use the ID from the created model
				tt.args.id = uuid.MustParse(testModel.ID)
			}

			result, err := repository.Find[DummyModel](context.Background(), db, tt.args.id, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Find() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Run verification if provided and test didn't expect error
			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, result)
			}
		})
	}
}

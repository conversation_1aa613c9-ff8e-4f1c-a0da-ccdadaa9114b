package core_test

import (
	"encoding/base64"
	"strings"
	"testing"

	"sa-intranet/core"

	"github.com/samber/do"
)

func TestNewAESCipher(t *testing.T) {
	tests := []struct {
		name        string
		key         string
		wantErr     bool
		errContains string
	}{
		{
			name:    "valid 16-byte key (AES-128)",
			key:     base64.StdEncoding.EncodeToString([]byte("1234567890123456")), // 16 bytes
			wantErr: false,
		},
		{
			name:    "valid 24-byte key (AES-192)",
			key:     base64.StdEncoding.EncodeToString([]byte("123456789012345678901234")), // 24 bytes
			wantErr: false,
		},
		{
			name:    "valid 32-byte key (AES-256)",
			key:     base64.StdEncoding.EncodeToString([]byte("12345678901234567890123456789012")), // 32 bytes
			wantErr: false,
		},
		{
			name:        "invalid key length - 15 bytes",
			key:         base64.StdEncoding.EncodeToString([]byte("123456789012345")), // 15 bytes
			wantErr:     true,
			errContains: "key length must be 16, 24, or 32 bytes",
		},
		{
			name:        "invalid key length - 17 bytes",
			key:         base64.StdEncoding.EncodeToString([]byte("12345678901234567")), // 17 bytes
			wantErr:     true,
			errContains: "key length must be 16, 24, or 32 bytes",
		},
		{
			name:        "invalid base64 key",
			key:         "invalid-base64-key!@#",
			wantErr:     true,
			errContains: "illegal base64 data",
		},
		{
			name:        "empty key",
			key:         "",
			wantErr:     true,
			errContains: "key length must be 16, 24, or 32 bytes",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			injector := do.New()

			// Provide the cypher config
			do.Provide(injector, func(i *do.Injector) (core.CypherConfig, error) {
				return core.CypherConfig{Key: tt.key}, nil
			})

			cipher, err := core.NewAESCipher(injector)

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewAESCipher() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("NewAESCipher() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("NewAESCipher() unexpected error = %v", err)
				return
			}

			if cipher == nil {
				t.Errorf("NewAESCipher() returned nil cipher")
			}
		})
	}
}

func TestAESCipher_Encrypt(t *testing.T) {
	// Create a valid cipher for testing
	injector := do.New()
	validKey := base64.StdEncoding.EncodeToString([]byte("1234567890123456")) // 16 bytes
	do.Provide(injector, func(i *do.Injector) (core.CypherConfig, error) {
		return core.CypherConfig{Key: validKey}, nil
	})

	cipher, err := core.NewAESCipher(injector)
	if err != nil {
		t.Fatalf("Failed to create cipher: %v", err)
	}

	tests := []struct {
		name      string
		plaintext string
		wantErr   bool
	}{
		{
			name:      "encrypt simple text",
			plaintext: "hello world",
			wantErr:   false,
		},
		{
			name:      "encrypt empty string",
			plaintext: "",
			wantErr:   false,
		},
		{
			name:      "encrypt long text",
			plaintext: "This is a very long text that should be encrypted properly without any issues",
			wantErr:   false,
		},
		{
			name:      "encrypt special characters",
			plaintext: "!@#$%^&*()_+-=[]{}|;':\",./<>?",
			wantErr:   false,
		},
		{
			name:      "encrypt unicode text",
			plaintext: "Hello 世界 🌍",
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			encrypted, err := cipher.Encrypt(tt.plaintext)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Encrypt() expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("Encrypt() unexpected error = %v", err)
				return
			}

			if encrypted == "" {
				t.Errorf("Encrypt() returned empty string")
			}

			// Verify that encrypted text is different from plaintext
			if encrypted == tt.plaintext {
				t.Errorf("Encrypt() returned same as plaintext")
			}

			// Verify that we can decrypt it back
			decrypted, err := cipher.Decrypt(encrypted)
			if err != nil {
				t.Errorf("Failed to decrypt encrypted text: %v", err)
				return
			}

			if decrypted != tt.plaintext {
				t.Errorf("Decrypt() = %v, want %v", decrypted, tt.plaintext)
			}
		})
	}
}

func TestAESCipher_Decrypt(t *testing.T) {
	// Create a valid cipher for testing
	injector := do.New()
	validKey := base64.StdEncoding.EncodeToString([]byte("1234567890123456")) // 16 bytes
	do.Provide(injector, func(i *do.Injector) (core.CypherConfig, error) {
		return core.CypherConfig{Key: validKey}, nil
	})

	cipher, err := core.NewAESCipher(injector)
	if err != nil {
		t.Fatalf("Failed to create cipher: %v", err)
	}

	tests := []struct {
		name          string
		encryptedText string
		wantErr       bool
		errContains   string
		setup         func() string // Function to setup encrypted text
	}{
		{
			name:    "decrypt valid encrypted text",
			wantErr: false,
			setup: func() string {
				encrypted, _ := cipher.Encrypt("test message")
				return encrypted
			},
		},
		{
			name:          "decrypt invalid base64",
			encryptedText: "invalid-base64!@#",
			wantErr:       true,
			errContains:   "illegal base64 data",
		},
		{
			name:          "decrypt too short ciphertext",
			encryptedText: base64.StdEncoding.EncodeToString([]byte("short")),
			wantErr:       true,
			errContains:   "ciphertext too short",
		},
		{
			name:          "decrypt empty string",
			encryptedText: "",
			wantErr:       true,
			errContains:   "ciphertext too short",
		},
		{
			name:        "decrypt corrupted ciphertext",
			wantErr:     true,
			errContains: "cipher: message authentication failed",
			setup: func() string {
				// Create valid encrypted text then corrupt it
				encrypted, _ := cipher.Encrypt("test")
				decoded, _ := base64.StdEncoding.DecodeString(encrypted)
				// Corrupt the last byte
				if len(decoded) > 0 {
					decoded[len(decoded)-1] ^= 0xFF
				}
				return base64.StdEncoding.EncodeToString(decoded)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			encryptedText := tt.encryptedText
			if tt.setup != nil {
				encryptedText = tt.setup()
			}

			decrypted, err := cipher.Decrypt(encryptedText)

			if tt.wantErr {
				if err == nil {
					t.Errorf("Decrypt() expected error, got nil")
					return
				}
				if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Decrypt() error = %v, should contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("Decrypt() unexpected error = %v", err)
				return
			}

			// For valid decryption, verify the result
			if tt.name == "decrypt valid encrypted text" && decrypted != "test message" {
				t.Errorf("Decrypt() = %v, want %v", decrypted, "test message")
			}
		})
	}
}

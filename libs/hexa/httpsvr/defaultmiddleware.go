package httpsvr

import (
	"log"
	"net/http"
	"time"
)

func RequestLoggerMiddleware(enabled bool) Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if enabled {
				startLocal := time.Now()
				startUTC := startLocal.UTC()

				// Create response wrapper to capture status code
				rw := &requestLoggerResponseWriter{ResponseWriter: w, status: http.StatusOK}

				// Process request
				next.ServeHTTP(rw, r)

				// Calculate duration
				duration := time.Since(startUTC)

				// Rails-style log format:
				// Started GET "/api/users" for 127.0.0.1 at 2023-11-21 10:30:45 +0700
				// Completed 200 OK in 1.2ms
				log.Printf("Started %s \"%s\" for %s at %s (local time: %s)",
					r.Method,
					r.URL.Path,
					r.RemoteAddr,
					startUTC.Format("2006-01-02 15:04:05 UTC"),
					startLocal.Format("2006-01-02 15:04:05 MST"))

				log.Printf("Completed %d %s in %v",
					rw.status,
					http.StatusText(rw.status),
					duration)
			}
		})
	}
}

// requestLoggerResponseWriter wraps http.ResponseWriter to capture status code
type requestLoggerResponseWriter struct {
	http.ResponseWriter
	status int
}

func (rw *requestLoggerResponseWriter) WriteHeader(code int) {
	rw.status = code
	rw.ResponseWriter.WriteHeader(code)
}

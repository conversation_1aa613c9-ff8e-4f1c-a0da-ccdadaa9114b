package httpsvr

import (
	"context"
	"errors"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

var (
	ErrServerNotRunning     = errors.New("server is not running")
	ErrServerForcedShutdown = errors.New("server forced shutdown")
)

type App interface {
	IsRunning() bool
	Running() <-chan bool
	Run() error
	Stop() error
}

type DefaultApp struct {
	server  HTTPServer
	Router  Router
	Config  DefaultConfig
	runChan chan bool
	running bool
}

func NewApp() *DefaultApp {
	router := NewDefaultRouter()
	router.Use(RequestLoggerMiddleware(true))
	config := NewDefaultConfig(router)

	return &DefaultApp{
		Router:  router,
		Config:  config,
		running: false,
		runChan: make(chan bool, 1),
		server:  nil,
	}
}

func (a *DefaultApp) IsRunning() bool {
	return a.running
}

func (a *DefaultApp) Stop() error {
	if !a.running {
		return ErrServerNotRunning
	}

	return a.server.Stop(context.Background())
}

func (a *DefaultApp) Running() <-chan bool {
	return a.runChan
}

func (a *DefaultApp) Run() error {
	config := a.Config
	// Create the HTTP server
	a.server = NewDefaultHTTPServer(config)
	server := a.server

	// Define shutdown timeout
	const shutdownTimeout = 30 * time.Second

	// Create a context for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), shutdownTimeout)
	defer cancel()

	// Channel to listen for interrupt signals
	done := make(chan os.Signal, 1)
	// Channel to listen for server errors
	errorChan := make(chan error, 1)

	signal.Notify(done, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

	log.Println("Server is running...")

	a.running = true
	a.runChan <- a.running

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on %s\n", config.Addr)

		if err := server.Start(ctx); err != nil && !errors.Is(err, http.ErrServerClosed) {
			errorChan <- err
			return
		}
	}()

	// Block until interrupt signal is received or error occurs
	select {
	case <-done:
		log.Println("Server is shutting down...")

		// Graceful shutdown in a goroutine to prevent blocking
		shutdownComplete := make(chan struct{})
		go func() {
			if err := server.Stop(ctx); err != nil {
				log.Printf("Server shutdown error: %v\n", err)
			}

			close(shutdownComplete)
		}()

		// Wait for shutdown to complete or timeout
		select {
		case <-shutdownComplete:
			log.Println("Server gracefully shutdown completed")
			goto successfulShutdown
		case <-time.After(shutdownTimeout):
			log.Println("Shutdown timeout exceeded. Forcing exit.")
			return ErrServerForcedShutdown
		}
	case err := <-errorChan:
		log.Printf("Server error: %v\n", err)
		return err
	}

successfulShutdown:
	a.running = false
	<-a.runChan // drain existing value
	a.runChan <- a.running

	log.Println("Server stopped gracefully")

	return nil
}

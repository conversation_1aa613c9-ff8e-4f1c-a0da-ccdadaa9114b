package cmd

import (
	"context"
	"errors"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/go-cmd/cmd"
)

type Runner interface {
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Healthy() chan bool
	Config() ProcessConfig
	Name() string
	Run(ctx context.Context) error
}

type ProcessConfig struct {
	Name    string
	Command string
	Args    []string
	WorkDir string
}

type ProcessRunner struct {
	name    string
	config  ProcessConfig
	cmd     *cmd.Cmd
	done    chan struct{}
	stopped bool
	status  chan cmd.Status
	stdout  chan string
	stderr  chan string
}

func NewProcessRunner(config ProcessConfig) *ProcessRunner {
	if config.Name == "" {
		config.Name = "default"
	}
	return &ProcessRunner{
		name:    config.Name,
		config:  config,
		done:    make(chan struct{}),
		status:  make(chan cmd.Status, 1),
		stopped: false,
		stdout:  make(chan string, 1),
		stderr:  make(chan string, 1),
	}
}

func (r *ProcessRunner) Config() ProcessConfig {
	return r.config
}

func (r *ProcessRunner) Start(ctx context.Context) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		if r.cmd != nil {
			return errors.New("process already running")
		}

		// r.cmd = cmd.NewCmd(r.config.Command, r.config.Args...)
		r.cmd = cmd.NewCmdOptions(cmd.Options{
			Buffered:  false,
			Streaming: true,
		}, r.config.Command, r.config.Args...)

		if r.config.WorkDir != "" {
			r.cmd.Dir = r.config.WorkDir
		}

		status := r.cmd.Start()

		go func() {
			r.status <- <-status
			close(r.done)
		}()

		return nil
	}
}

func (r *ProcessRunner) Stop(ctx context.Context) error {
	if r.stopped {
		return nil
	}

	r.stopped = true
	defer func() {
		r.cmd = nil
		close(r.status)
	}()

	if r.cmd != nil {
		r.cmd.Stop()
	}

	select {
	case <-r.done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func (r *ProcessRunner) Status() cmd.Status {
	if r.cmd == nil {
		return cmd.Status{}
	}
	return r.cmd.Status()
}

func (r *ProcessRunner) Stdout() chan string {
	return r.stdout
}

func (r *ProcessRunner) Stderr() chan string {
	return r.stderr
}

func (r *ProcessRunner) CmdStdout() chan string {
	if r.cmd == nil {
		return nil
	}
	return r.cmd.Stdout
}

func (r *ProcessRunner) CmdStderr() chan string {
	if r.cmd == nil {
		return nil
	}
	return r.cmd.Stderr
}

func (r *ProcessRunner) Name() string {
	return r.name
}

func (r *ProcessRunner) Wait(ctx context.Context) (cmd.Status, error) {
	if r.cmd == nil {
		return cmd.Status{}, errors.New("no process running")
	}

	select {
	case status := <-r.status:
		return status, nil
	case <-ctx.Done():
		return cmd.Status{}, ctx.Err()
	}
}

func (r *ProcessRunner) PrintLogs(errChan chan<- error) {
	for {
		select {
		case stdout := <-r.CmdStdout():
			fmt.Printf("%s: %v\n", r.Name(), stdout)
			r.stdout <- stdout
		case stderr := <-r.CmdStderr():
			r.stderr <- stderr
			errChan <- fmt.Errorf("process error: %v", stderr)
			fmt.Println("Initiating graceful shutdown...")
			return
		}
	}
}

func (r *ProcessRunner) Run(ctx context.Context) error {
	done := make(chan os.Signal, 1)
	signal.Notify(done, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

	errChan := make(chan error, 1)

	if err := r.Start(ctx); err != nil {
		return err
	}

	go func() {
		r.PrintLogs(errChan)
	}()

	go func() {
		status, err := r.Wait(ctx)
		if err != nil {
			errChan <- fmt.Errorf("process wait error: %v", err)
			return
		}
		if status.Exit != 0 {
			errChan <- fmt.Errorf("process exited with code: %d", status.Exit)
			return
		}
	}()

	fmt.Println("Process manager for " + r.Name() + " is running...")

	select {
	case <-done:
		fmt.Println("Received shutdown signal for " + r.Name())
	case err := <-errChan:
		fmt.Printf("Error detected in "+r.Name()+": %v\n", err)
	}

	if err := r.Stop(ctx); err != nil {
		log.Printf("Shutdown error in "+r.Name()+": %v\n", err)
		return err
	}

	fmt.Println("Process stopped gracefully for " + r.Name())
	return nil
}

package bootstrap

import (
	"context"
	"fmt"
	"log/slog"
	"sync"

	"github.com/samber/do"
)

type TeardownFunc func(ctx context.Context) error

type Container interface {
	Injector() *do.Injector
}

type ServiceProvider interface {
	Register(ctx context.Context, container Container) error
	Boot(ctx context.Context, container Container) error
	Teardown(ctx context.Context, container Container) error
}

type BootStrapper interface {
	Container() Container
	Register(ctx context.Context) error
	Boot(ctx context.Context) error
	Shutdown(ctx context.Context) error
	ServiceProviders() []ServiceProvider
}

var _ Container = (*DefaulContainer)(nil)

type DefaulContainer struct {
	injector *do.Injector
}

func NewContainer(i *do.Injector) *DefaulContainer {
	return &DefaulContainer{
		injector: i,
	}
}

func (c *DefaulContainer) Injector() *do.Injector {
	return c.injector
}

var _ ServiceProvider = (*DefaultServiceProvider)(nil)

type DefaultServiceProvider struct{}

func (s *DefaultServiceProvider) Register(ctx context.Context, container Container) error {
	return nil
}

func (s *DefaultServiceProvider) Boot(ctx context.Context, container Container) error {
	return nil
}

func (s *DefaultServiceProvider) Teardown(ctx context.Context, container Container) error {
	return nil
}

var _ BootStrapper = (*DefaultBootStrapper)(nil)

type DefaultBootStrapper struct {
	container    *DefaulContainer
	services     []ServiceProvider
	booted       bool
	serviceMutex sync.Mutex
	registered   bool
	shutdown     bool
}

func NewDefaultBootStrapper(i *do.Injector, services ...ServiceProvider) *DefaultBootStrapper {
	return &DefaultBootStrapper{
		container:    NewContainer(i),
		services:     services,
		booted:       false,
		registered:   false,
		serviceMutex: sync.Mutex{},
	}
}

func (b *DefaultBootStrapper) Container() Container {
	return b.container
}

func (b *DefaultBootStrapper) Register(ctx context.Context) error {
	if b.registered {
		slog.Warn("bootstrapper is already registered")
		return nil
	}
	b.registered = true
	for _, service := range b.services {
		if err := service.Register(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Boot(ctx context.Context) error {
	if b.booted {
		slog.Warn("bootstrapper is already booted")
		return nil
	}
	b.booted = true
	for _, service := range b.services {
		if err := service.Boot(ctx, b.container); err != nil {
			return err
		}
	}
	return nil
}

func (b *DefaultBootStrapper) Shutdown(ctx context.Context) error {
	var errs []error
	if b.shutdown {
		slog.Warn("bootstrapper already shut down")
		return nil
	}
	b.shutdown = true

	// Iterate through services in reverse order
	for i := len(b.services) - 1; i >= 0; i-- {
		if err := b.services[i].Teardown(ctx, b.container); err != nil {
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		err := fmt.Errorf("multiple teardown errors: %v", errs)
		slog.Error("shutdown failed", slog.Any("errors", errs))
		return err
	}
	return nil
}

func (b *DefaultBootStrapper) ServiceProviders() []ServiceProvider {
	b.serviceMutex.Lock()
	defer b.serviceMutex.Unlock()
	return b.services
}

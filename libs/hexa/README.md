# Hexa

## HTTP Server

Default implementation of the HTTP Server.

```go
package main

import (
    "github.com/emilioforrer/hexa/httpsvr"
)

func main() {
	// Create a new router
	router := httpsvr.NewDefaultRouter()

	// Register some test routes
	router.GET("/", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Welcome to the server!")
	})


	// Create server config with the router as handler
	config := httpsvr.NewDefaultConfig(router)

	// Create the HTTP server
	server := httpsvr.NewDefaultHTTPServer(config)

	// Create a context for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Channel to listen for interrupt signals
	done := make(chan os.Signal, 1)
	signal.Notify(done, os.Interrupt, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		if err := server.Start(ctx); err != nil && err != http.ErrServerClosed {
			fmt.Printf("Server error: %v\n", err)
		}
	}()

	fmt.Println("Server is running...")

	// Block until interrupt signal is received
	<-done
	fmt.Println("Server is shutting down...")

	// Initiate graceful shutdown
	if err := server.Stop(ctx); err != nil {
		fmt.Printf("Server shutdown error: %v\n", err)
	}

	fmt.Println("Server stopped gracefully")
}

```
-- Function to classify a numeric score into a quality level
CREATE OR <PERSON>EPLACE FUNCTION classify_score(score numeric)
RETURNS text AS $$
BEGIN
    IF score >= 90 THEN
        RETURN 'Excellent';
    ELSIF score >= 80 THEN
        RETURN 'Good';
    ELSIF score >= 70 THEN
        RETURN 'Fair';
    ELSIF score >= 40 THEN
        RETURN 'Poor';
    ELSE
        RETURN 'Critical';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to get the minimum boundary score for a given quality label
CREATE OR REPLACE FUNCTION score_min_boundary(label text)
RETURNS numeric AS $$
BEGIN
    CASE UPPER(label)
        WHEN 'EXCELLENT' THEN RETURN 90;
        WHEN 'GOOD' THEN RETURN 80;
        WHEN 'FAIR' THEN RETURN 70;
        WHEN 'POOR' THEN RETURN 40;
        WHEN 'CRITICAL' THEN RETURN 0;
        ELSE RAISE EXCEPTION 'Invalid quality label: %', label;
    END CASE;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION number_to_grade(n NUMERIC)
RETURNS TEXT AS $$
BEGIN
  CASE TRUNC(n)::INT
    WHEN 1 THEN RETURN 'A';
    WHEN 2 THEN RETURN 'B';
    WHEN 3 THEN RETURN 'C';
    WHEN 4 THEN RETURN 'D';
    WHEN 5 THEN RETURN 'E';
    ELSE
      RETURN '';
  END CASE;
END;
$$ LANGUAGE plpgsql;


DROP VIEW vw_sonarqube_projects_with_latest_analysis;
DROP VIEW vw_latest_sonarqube_analyses;
DROP VIEW vw_sonarqube_analyses;


CREATE OR REPLACE VIEW vw_sonarqube_analyses AS
SELECT 
    sa.*,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "open_issues").value')#>>'{}')::numeric as measure_open_issues,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "coverage").value')#>>'{}')::numeric as measure_coverage,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "reliability_rating").value')#>>'{}')::numeric as measure_reliability_rating,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "security_review_rating").value')#>>'{}')::numeric as measure_security_review_rating,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "sqale_rating").value')#>>'{}')::numeric as measure_sqale_rating,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "duplicated_lines").value')#>>'{}')::numeric as measure_duplicated_lines,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "duplicated_lines_density").value')#>>'{}')::numeric as measure_duplicated_lines_density,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "code_smells").value')#>>'{}')::numeric as measure_code_smells,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "vulnerable_dependencies").value')#>>'{}')::numeric as measure_vulnerable_dependencies,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "total_vulnerabilities").value')#>>'{}')::numeric as measure_total_vulnerabilities,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "vulnerabilities").value')#>>'{}')::numeric as measure_vulnerabilities,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "medium_severity_vulns").value')#>>'{}')::numeric as measure_medium_severity_vulns,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "low_severity_vulns").value')#>>'{}')::numeric as measure_low_severity_vulns,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "high_severity_vulns").value')#>>'{}')::numeric as measure_high_severity_vulns,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "violations").value')#>>'{}')::numeric as measure_violations,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "minor_violations").value')#>>'{}')::numeric as measure_minor_violations,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "major_violations").value')#>>'{}')::numeric as measure_major_violations,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "critical_violations").value')#>>'{}')::numeric as measure_critical_violations,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "blocker_violations").value')#>>'{}')::numeric as measure_blocker_violations,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "bugs").value')#>>'{}')::numeric as measure_bugs,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "security_hotspots").value')#>>'{}')::numeric as measure_security_hotspots,
    (jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "lines").value')#>>'{}')::numeric as measure_lines
FROM sonarqube_analyses sa;


CREATE OR REPLACE VIEW vw_latest_sonarqube_analyses AS
SELECT
        sa.*,
        ROW_NUMBER() OVER (PARTITION BY sonarqube_project_id ORDER BY analysed_at DESC) AS rn
    FROM
        vw_sonarqube_analyses AS sa
    WHERE
        status = 'succeeded' AND score IS NOT NULL;



CREATE OR REPLACE VIEW vw_sonarqube_projects_with_latest_analysis AS
SELECT 
    sp.id,
    sp.jira_project_id,
    sp.project_name AS sonar_project_name,
    sp.project_key AS sonar_project_key,
    lsa.quality_gate_name AS quality_gate_name,
    TRIM(lsa.quality_gate_status) AS quality_gate_status,
    TRIM(lsa.status) AS status,
    lsa.analysed_at,
    lsa.score AS score,
    lsa.measure_open_issues AS measure_open_issues,
    lsa.measure_coverage AS measure_coverage,
    lsa.measure_reliability_rating AS measure_reliability_rating,
    lsa.measure_security_review_rating AS measure_security_review_rating,
    lsa.measure_sqale_rating AS measure_sqale_rating,
    lsa.measure_duplicated_lines AS measure_duplicated_lines,
    lsa.measure_duplicated_lines_density AS measure_duplicated_lines_density,
    lsa.measure_code_smells AS measure_code_smells,
    lsa.measure_vulnerable_dependencies AS measure_vulnerable_dependencies,
    lsa.measure_total_vulnerabilities AS measure_total_vulnerabilities,
    lsa.measure_vulnerabilities AS measure_vulnerabilities,
    lsa.measure_medium_severity_vulns AS measure_medium_severity_vulns,
    lsa.measure_low_severity_vulns AS measure_low_severity_vulns,
    lsa.measure_high_severity_vulns AS measure_high_severity_vulns,
    lsa.measure_violations AS measure_violations,
    lsa.measure_minor_violations AS measure_minor_violations,
    lsa.measure_major_violations AS measure_major_violations,
    lsa.measure_critical_violations AS measure_critical_violations,
    lsa.measure_blocker_violations AS measure_blocker_violations,
    lsa.measure_bugs AS measure_bugs,
    lsa.measure_security_hotspots AS measure_security_hotspots,
    lsa.measure_lines AS measure_lines,
    number_to_grade(lsa.measure_reliability_rating) AS measure_reliability_rating_grade,
    number_to_grade(lsa.measure_security_review_rating) AS measure_security_review_rating_grade,
    number_to_grade(lsa.measure_sqale_rating) AS measure_sqale_rating_grade,
    jp.name AS jira_project_name,
    jp.project_key AS jira_project_key,
    jp.jira_url AS jira_url,
    cc.name AS company_client_name,
    cc.id AS company_client_id
FROM sonarqube_projects sp
INNER JOIN jira_projects jp ON sp.jira_project_id = jp.id
INNER JOIN company_clients cc ON jp.company_client_id = cc.id
INNER JOIN vw_latest_sonarqube_analyses lsa ON sp.id = lsa.sonarqube_project_id AND lsa.rn = 1;
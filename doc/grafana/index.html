<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Project Portfolio</title>
  <script src="https://cdn.jsdelivr.net/npm/handlebars@latest/dist/handlebars.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-6">
  <div class="container mx-auto">
    <h1 class="text-3xl font-bold mb-6">Project Portfolio</h1>
    
    <div id="project-container"></div>
  </div>

  <script id="project-template" type="text/x-handlebars-template">
    {{#each this}}
     <div class="bg-white shadow-lg rounded-lg mb-6 border-l-8 {{#if (eq quality_gate_status "OK")}}border-green-500{{else}}border-red-500{{/if}}">
  <div class="p-4">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-bold">
        <a class="text-black hover:underline" target="_blank" href="https://sonarqube.applaudostudios.com/dashboard?id={{sonar_project_key}}">
          {{sonar_project_name}}
        </a>
      </h2>
      <span class="text-sm font-semibold px-3 py-1 rounded-full {{#if (eq quality_gate_status "OK")}}bg-green-100 text-green-800{{else}}bg-red-100 text-red-800{{/if}}">
        {{#if (eq quality_gate_status "OK")}}PASSED{{else}}FAILED{{/if}}
      </span>
    </div>

    <div class="grid grid-cols-6 gap-4 text-sm">
      <div>
        <p class="text-gray-500 font-medium">RELIABILITY</p>
        <p class="text-lg font-bold">{{measure_bugs}} Bugs</p>
        <p class="text-lg font-semibold text-yellow-600">
          {{#if (eq measure_reliability_rating 1)}}
            <span class="text-green-600">A</span>
          {{else if (eq measure_reliability_rating 2)}}
            <span class="text-lime-600">B</span>
          {{else if (eq measure_reliability_rating 3)}}
            <span class="text-yellow-500">C</span>
          {{else if (eq measure_reliability_rating 4)}}
            <span class="text-orange-500">D</span>
          {{else if (eq measure_reliability_rating 5)}}
            <span class="text-red-600">E</span>
          {{/if}}
        </p>
      </div>

      <div>
        <p class="text-gray-500 font-medium">SECURITY</p>
        <p class="text-lg font-bold">{{measure_total_vulnerabilities}} Vulnerabilities</p>
        <p class="text-lg font-semibold">
          {{#if (gte measure_blocker_violations 1)}}
            <span class="text-red-600">E</span>
          {{else if (gte measure_critical_violations 1)}}
            <span class="text-orange-500">D</span>
          {{else if (gte measure_major_violations 1)}}
            <span class="text-yellow-500">C</span>
          {{else if (gte measure_minor_violations 1)}}
            <span class="text-lime-600">B</span>
          {{else if (eq measure_vulnerabilities 0)}}
            <span class="text-green-600">A</span>
          {{/if}}
        </p>
        <p class="text-md text-gray-600 font-bold">{{measure_open_issues}} Open Issues</p>
      </div>

      <div>
        <p class="text-gray-500 font-medium">MAINTAINABILITY</p>
        <p class="text-lg font-bold">{{measure_code_smells}} Code smells</p>
        <p class="text-lg font-semibold text-green-600">{{measure_sqale_rating_grade}}</p>
        <p class="text-lg font-semibold">
          {{#if (and (gte measure_sqale_rating 0) (lte measure_sqale_rating 0.05))}}
            <span class="text-green-600">A</span>
          {{else if (and (gt measure_sqale_rating 0.05) (lte measure_sqale_rating 0.1))}}
            <span class="text-lime-600">B</span>
          {{else if (and (gt measure_sqale_rating 0.1) (lte measure_sqale_rating 0.2))}}
            <span class="text-yellow-500">C</span>
          {{else if (and (gt measure_sqale_rating 0.2) (lte measure_sqale_rating 0.5))}}
            <span class="text-orange-500">D</span>
          {{else if (gt measure_sqale_rating 0.5)}}
            <span class="text-red-600">E</span>
          {{/if}}
        </p>
      </div>

      <div>
        <p class="text-gray-500 font-medium">SECURITY REVIEW</p>
        <p class="text-lg font-bold">{{measure_security_hotspots}} Security Hot Spots</p>
        <p class="text-lg font-semibold">
          {{#if (gte measure_security_review_rating 0.8)}}
            <span class="text-green-600">A</span>
          {{else if (and (gte measure_security_review_rating 0.7) (lt measure_security_review_rating 80))}}
            <span class="text-lime-600">B</span>
          {{else if (and (gte measure_security_review_rating 0.5) (lt measure_security_review_rating 70))}}
            <span class="text-yellow-500">C</span>
          {{else if (and (gte measure_security_review_rating 0.3) (lt measure_security_review_rating 50))}}
            <span class="text-orange-500">D</span>
          {{else}}
            <span class="text-red-600">E</span>
          {{/if}}
        </p>

      </div>

      <div>
        <p class="text-gray-500 font-medium">COVERAGE</p>
        <p class="text-lg font-bold">{{measure_coverage}}%</p>
      </div>

      <div>
        <p class="text-gray-500 font-medium">DUPLICATION</p>
        <p class="text-lg font-bold">{{measure_duplicated_lines_density}}% Line Density</p>
      </div>
    </div>

    <div class="mt-4 text-xs text-gray-600">
      <p>Last analysis: {{analysed_at}}</p>
      <p>Quality Gate: {{quality_gate_name}}</p>
    </div>
  </div>
</div>
    {{/each}}
  </script>

  <script>
    // Register helper for equality check
    Handlebars.registerHelper("eq", function(a, b) {
      return a === b;
    });

    // Example Data Array
    const data = [
      {
        sonar_project_name: "Apache Airflow",
        quality_gate_status: "FAILED",
        quality_gate_name: "Sonar way",
        analysed_at: "2025-03-21",
        measure_bugs: 9,
        measure_total_vulnerabilities: 1,
        measure_code_smells: 695,
        measure_security_hotspots: 48,
        measure_coverage: 0,
        measure_duplicated_lines_density: 3,
        measure_reliability_rating_grade: "D",
        measure_security_review_rating_grade: "B",
        measure_sqale_rating_grade: "A"
      },
      {
        sonar_project_name: "WebScarab",
        quality_gate_status: "OK",
        quality_gate_name: "Sonar way",
        analysed_at: "2025-04-19",
        measure_bugs: 182,
        measure_total_vulnerabilities: 18,
        measure_code_smells: 3900,
        measure_security_hotspots: 95,
        measure_coverage: 0.1,
        measure_duplicated_lines_density: 3.7,
        measure_reliability_rating_grade: "E",
        measure_security_review_rating_grade: "E",
        measure_sqale_rating_grade: "A"
      },
      {
        sonar_project_name: "WebGoat",
        quality_gate_status: "OK",
        quality_gate_name: "Sonar way",
        analysed_at: "2025-03-10",
        measure_bugs: 18,
        measure_total_vulnerabilities: 29,
        measure_code_smells: 684,
        measure_security_hotspots: 77,
        measure_coverage: 0,
        measure_duplicated_lines_density: 1.5,
        measure_reliability_rating_grade: "E",
        measure_security_review_rating_grade: "E",
        measure_sqale_rating_grade: "A"
      }
    ];

    const templateSource = document.getElementById("project-template").innerHTML;
    const template = Handlebars.compile(templateSource);
    const compiledHtml = template(data);
    document.getElementById("project-container").innerHTML = compiledHtml;
  </script>
</body>
</html>

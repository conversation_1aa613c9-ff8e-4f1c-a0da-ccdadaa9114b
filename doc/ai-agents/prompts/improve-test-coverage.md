# Task: Improve Test Coverage for libs/intranet Go Module

## Objective
Increase overall test coverage for the `libs/intranet` module from current level to 80% or higher.

## Project Guidelines Compliance
1. **Check project-specific instructions:**
   - Review `.augment-guidelines` for project-specific testing standards
   - Follow established patterns and conventions from existing codebase
   - Adhere to team coding standards and practices

2. **File Creation/Modification Rules:**
   - **ONLY** create and update `*_test.go` files
   - Do NOT modify source code files unless explicitly required for testability
   - Follow Go naming conventions for test files (e.g., `user.go` → `user_test.go`)

## Testing Standards and Best Practices
- **Prefer table-driven tests** using Go best practices:
  ```go
  func TestFunctionName(t *testing.T) {
      tests := []struct {
          name     string
          input    InputType
          expected ExpectedType
          wantErr  bool
      }{
          {
              name:     "valid case",
              input:    validInput,
              expected: expectedOutput,
              wantErr:  false,
          },
          // ... more test cases
      }
      
      for _, tt := range tests {
          t.Run(tt.name, func(t *testing.T) {
              result, err := FunctionName(tt.input)
              if (err != nil) != tt.wantErr {
                  t.Errorf("FunctionName() error = %v, wantErr %v", err, tt.wantErr)
                  return
              }
              if !reflect.DeepEqual(result, tt.expected) {
                  t.Errorf("FunctionName() = %v, want %v", result, tt.expected)
              }
          })
      }
  }
  ```

- **Follow Go testing standards:**
  - Use `testing.T` for unit tests
  - Use `testing.B` for benchmarks when appropriate
  - Implement proper test setup and teardown
  - Use subtests with `t.Run()` for organized test execution
  - Include meaningful test names that describe the scenario
  - Test both success and failure paths
  - Use testify/assert or similar libraries if already established in the project

## Current State Analysis Required
1. **Check project guidelines first:**
   ```bash
   cat .augment-guidelines 2>/dev/null || echo "No .augment-guidelines found"
   ```

2. **Run initial coverage assessment:**
   ```bash
   task intranet:run-sonar-scanner-prepare
   ```
   
3. **Analyze coverage reports:**
   - Coverage profile: `libs/intranet/coverage/profile.out`
   - HTML report: `libs/intranet/coverage/coverage-pretty.html`

## Action Plan for AI Agent

### Phase 1: Assessment
- [ ] Review `.augment-guidelines` for project-specific requirements
- [ ] Execute coverage command and parse current coverage percentage
- [ ] Identify uncovered files, functions, and code blocks
- [ ] Analyze existing test structure and patterns in `*_test.go` files
- [ ] Categorize missing coverage by:
  - Untested functions/methods
  - Uncovered error handling paths
  - Missing edge cases
  - Integration points without tests

### Phase 2: Test Implementation Strategy
- [ ] Prioritize coverage improvements by:
  - Critical business logic (high priority)
  - Error handling paths (medium priority)
  - Edge cases and boundary conditions (medium priority)
  - Utility functions (low priority)

### Phase 3: Implementation Guidelines
- **STRICT RULE: Only create/modify `*_test.go` files**
- Follow existing test patterns and conventions in the codebase
- Implement table-driven tests as the preferred testing pattern
- Ensure tests are meaningful, not just coverage-driven
- Include both unit tests and integration tests where appropriate
- Add comprehensive test cases for error scenarios and edge cases
- Use descriptive test names and organize with subtests
- Maintain test performance and avoid flaky tests
- Follow Go testing best practices and standards

### Phase 4: Validation
- [ ] Re-run coverage after each batch of new tests
- [ ] Verify coverage percentage increases toward 80% target
- [ ] Ensure all new tests pass consistently with `go test`
- [ ] Validate that existing functionality remains unbroken
- [ ] Confirm adherence to project guidelines from `.augment-guidelines`

## Success Criteria
- Overall test coverage reaches 80% or higher
- All new tests pass reliably
- No regression in existing functionality
- Coverage improvements focus on meaningful code paths
- All new tests follow table-driven test patterns
- Compliance with project guidelines in `.augment-guidelines`
- Only `*_test.go` files are created or modified

## Commands Reference
```bash
# Check project guidelines
cat .augment-guidelines

# Run tests and generate coverage report
task intranet:run-sonar-scanner-prepare

# Run specific package tests
go test ./libs/intranet/... -v

# Run tests with coverage
go test ./libs/intranet/... -cover -coverprofile=coverage.out

# View coverage in browser (if HTML report exists)
cat libs/intranet/coverage/coverage-pretty.html
```

## File Locations
- Project guidelines: `.augment-guidelines`
- Source code: `libs/intranet/`
- Test files: `libs/intranet/*_test.go` (create/modify only these files)
- Coverage output: `libs/intranet/coverage/`

## Table-Driven Test Template
Use this template for consistent test structure with custom verification:

```go
func TestFunctionName(t *testing.T) {
    tests := []struct {
        name        string
        // input fields
        arg1        Type1
        arg2        Type2
        // expected output fields  
        want        ReturnType
        wantErr     bool
        errContains string // optional: for error message validation
        verify      func(t *testing.T, got ReturnType, input struct{arg1 Type1; arg2 Type2}) // custom verification function
    }{
        {
            name: "success case - description",
            arg1: validValue1,
            arg2: validValue2,
            want: expectedResult,
            wantErr: false,
            verify: func(t *testing.T, got ReturnType, input struct{arg1 Type1; arg2 Type2}) {
                // Custom verification logic that can access both result and input
                if got.SomeField != input.arg1 {
                    t.Errorf("Expected result.SomeField to match input.arg1: got %v, want %v", got.SomeField, input.arg1)
                }
                // Additional custom validations...
            },
        },
        {
            name: "error case - description", 
            arg1: invalidValue1,
            arg2: invalidValue2,
            want: zeroValue,
            wantErr: true,
            errContains: "expected error message",
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := FunctionName(tt.arg1, tt.arg2)
            
            if tt.wantErr {
                if err == nil {
                    t.Errorf("FunctionName() expected error, got nil")
                    return
                }
                if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
                    t.Errorf("FunctionName() error = %v, should contain %v", err, tt.errContains)
                }
                return
            }
            
            if err != nil {
                t.Errorf("FunctionName() unexpected error = %v", err)
                return
            }
            
            // Standard comparison
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("FunctionName() = %v, want %v", got, tt.want)
            }
            
            // Custom verification if provided
            if tt.verify != nil {
                input := struct{arg1 Type1; arg2 Type2}{
                    arg1: tt.arg1,
                    arg2: tt.arg2,
                }
                tt.verify(t, got, input)
            }
        })
    }
}
```

## Alternative Simplified Verify Function Template
For cases where you want a simpler verify function signature:

```go
func TestFunctionName(t *testing.T) {
    tests := []struct {
        name        string
        arg1        Type1
        arg2        Type2
        want        ReturnType
        wantErr     bool
        errContains string
        verify      func(t *testing.T, got ReturnType, arg1 Type1, arg2 Type2) // direct parameter access
    }{
        {
            name: "success case with custom verification",
            arg1: validValue1,
            arg2: validValue2,
            want: expectedResult,
            wantErr: false,
            verify: func(t *testing.T, got ReturnType, arg1 Type1, arg2 Type2) {
                // Verify result against inputs
                if got.CalculatedField != arg1 + arg2 {
                    t.Errorf("Expected calculated field to be %v, got %v", arg1 + arg2, got.CalculatedField)
                }
                // Additional validations...
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := FunctionName(tt.arg1, tt.arg2)
            
            if tt.wantErr {
                if err == nil {
                    t.Errorf("FunctionName() expected error, got nil")
                    return
                }
                if tt.errContains != "" && !strings.Contains(err.Error(), tt.errContains) {
                    t.Errorf("FunctionName() error = %v, should contain %v", err, tt.errContains)
                }
                return
            }
            
            if err != nil {
                t.Errorf("FunctionName() unexpected error = %v", err)
                return
            }
            
            // Standard comparison
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("FunctionName() = %v, want %v", got, tt.want)
            }
            
            // Custom verification if provided
            if tt.verify != nil {
                tt.verify(t, got, tt.arg1, tt.arg2)
            }
        })
    }
}
```

## Verify Function Best Practices
- Use the `verify` function for complex validations that require access to both input and output
- Implement custom business logic validations that go beyond simple equality checks
- Validate relationships between input parameters and result fields
- Check computed values, transformations, or derived properties
- Verify side effects or state changes when applicable
- Keep verify functions focused and well-documented
- Use meaningful error messages that explain what was expected vs. what was received


# Copilot Instructions for Intranet & Wiki Monorepo

This project is a **Golang mono repo** that hosts:
- A backend service (`services/backend`) exposing a REST API.
- A frontend service (`services/frontend`) using **https://github.com/romsar/gonertia** (Go + Inertia.js) with React (Vite + TypeScript).
- A Hugo static site (`services/wiki`) for documentation.
- Domain libraries:
  - `libs/intranet` for Intranet domain logic (SonarQube webhooks, Jira issues, Grafana dashboards).
  - `libs/hexa` for hexagonal architecture support.
- Uses **Clean Architecture** and **Domain-Driven Design (DDD)** principles.

## Key Conventions

✅ **Go Project Structure**
- Use `context.Context` for request lifecycle.
- Use DTOs for input and output data.
- Implement interactors (in ports) for core logic.
- Use presenters (out ports) to transform outputs for controllers.
- Use services as orchestrators for interactors and presenters.
- Use repositories for database interactions.
- Use idiomatic error handling (`errors`, `fmt.Errorf`).

✅ **Frontend (services/frontend)**
- Use functional React components and hooks.
- Use TypeScript with Vite for builds.
- Serve `/` with the Intranet app, `/wiki` with the Hugo site.
- No business logic in frontend; all domain logic in `libs/intranet`.

✅ **Testing**
- Use Go’s built-in `testing` for unit tests.
- Use `testcontainers-go` for service integration tests (e.g., Postgres).
- Lint with `golangci-lint`.
- SonarQube for code quality analysis.
- Trivy for container security scans.

✅ **Dependency Management**
- Use `github.com/samber/do` for DI.
- Use `viper` for config.
- Use `bun` for DB.
- Use `opa` for policy evaluation.
- Use `validator` for validation.

✅ **CI and DevOps**
- Use `Taskfile.yml` for automation (`task dev`, `task test`, etc.).
- Use Docker and Docker Compose (`devops/`).
- Conventional Commits (max 100 chars for commit titles).

## Example Project Layout

```
services/
    backend/ # Go REST API
    frontend/ # gonertia web server
    wiki/ # Hugo static site
libs/
    intranet/ # Intranet domain library (Clean Architecture)
    hexa/ # Hexagonal architecture library
doc/ # Diagrams, dashboards
devops/ # Docker, Compose, Taskfile
```

## Example of a typical clean architecture layout for a usecase:

```
usecase/
    auth/
        ├── auth.go
        ├── config
        │   └── config.go # Config for the usecase (viper)
        ├── model
        │   └── model.go
        ├── repository
        │   ├── repository.go
        │   └── user_repository.go
        └── service
            ├── common
            │   ├── base_user_interactor.go
            │   └── user_validation.go
            ├── in # Input ports 
            │   ├── create_user_interactor.go
            │   ├── in_test.go
            │   ├── list_user_interactor.go
            │   ├── update_user_interactor.go
            │   ├── user_provisioning_interactor.go
            │   ├── user_provisioning_interactor_test.go
            │   └── user_role_interactor.go
            ├── out # Output ports 
            │   ├── create_user_presenter.go
            │   ├── list_user_presenter.go
            │   ├── update_user_presenter.go
            │   ├── user_provisioning.go
            │   └── user_role_presenter.go
            ├── service_test.go
            ├── user_service.go
            └── user_service_test.go
```

## When Suggesting Code

🟩 **For Go Code:**
- Structure using Clean Architecture (model, repository, usecase, service, presenter, validator, input, output).
- Use idiomatic Go (interfaces, error wrapping).
- Favor small, single-responsibility functions.
- Use DI container (`do`) to wire dependencies.
- Use `viper` for config.
- Respect the rules configured in `golangci-lint` in the root folder

🟩 **For Frontend Code:**
- Use React functional components.
- Follow best practices for TailwindCSS and Vite.
- Keep frontend “thin”; do not add much business logic.
- since we use https://github.com/romsar/gonertia, most of the logic is in the backend (libs/intranet) injected via gonertia as react properties in the page component.

🟩 **For CI/DevOps:**
- Reference `Taskfile.yml` commands for builds and tests.
- Use Compose (`docker-compose.yaml`) for local dev.
- Use `docker-compose.ci.yaml` for CI tasks.

## Commit Conventions

- Use Conventional Commits.
- Max 100 characters for the title.
- Examples:
  - `feat(auth): add user provisioning interactor`
  - `fix(db): handle nil config in migration`

## Git Workflow

- The project uses Trunk Based Development and semver for versioning tags.

## Summary

Follow:
- **Clean Architecture** for Go code.
- **Functional React with TypeScript** for frontend.
- Use project-specific libraries (`viper`, `bun`, `do`, `gonertia`).
- Automation via Taskfile and Compose.
- Coding style and conventions most follow best practices and focused on security and maintainability with a min of 80% coverage for the intranet module.

If unsure about patterns, refer to:
- `libs/intranet/usecase/auth/` for clean architecture.
- `services/frontend/` for GoNertia + React.
- `devops/` for CI and local development.


✅ **Documentation and Monitoring**
- `doc/` contains diagrams (`Project.drawio`) and Grafana dashboards (`grafana/`).
- Intranet domain includes Grafana dashboards to monitor code quality (e.g., for SonarQube data).

✅ **Misc Files**
- `go.work` and `go.work.sum` for Go workspace management.
- `TODO` for tracking project tasks.
- `README.md` for project overview.
- `services/frontend/CHANGELOG.md` for frontend changelog is auto generated using `git-chglog` with a template located in `services/frontend/.chglog/CHANGELOG.tpl.md`.

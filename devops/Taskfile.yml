# https://taskfile.dev

version: "3"

vars:
  FRONTEND_DIR: "./services/frontend"
  WIKI_DIR: "./services/wiki"
  HEXA_DIR: "./libs/hexa"
  INTRANET_DIR: "./libs/intranet"

includes:
  intranet:
    dir: "{{.INTRANET_DIR}}"
    taskfile: "{{.INTRANET_DIR}}/Taskfile.yml"
  hexa:
    dir: "{{.HEXA_DIR}}"
    taskfile: "{{.HEXA_DIR}}/Taskfile.yml"
  wiki:
    dir: "{{.WIKI_DIR}}"
    taskfile: "{{.WIKI_DIR}}/Taskfile.yml"
  frontend:
    dir: "{{.FRONTEND_DIR}}"	
    taskfile: "{{.FRONTEND_DIR}}/Taskfile.yml"



tasks:
  build:
    desc: "Build the project"
    cmds:
    - task: frontend:build-prod
  setup:
    desc: "Setup the project"
    cmds:
    - task: wiki:setup
    - task: wiki:build
    - task: frontend:setup
    - mkdir -p {{.FRONTEND_DIR}}/public/wiki
    - cp -r {{.WIKI_DIR}}/dist/* {{.FRONTEND_DIR}}/public/wiki/
  docker-compose-exec-dev-frontend:
    cmds:
    - docker-compose -f devops/docker-compose.yaml exec dev-frontend sh
  docker-compose-build:
    cmds:
    - docker-compose -f devops/docker-compose.yaml build --no-cache
  docker-compose-up:
    cmds:
    - docker-compose -f devops/docker-compose.yaml up
    silent: true
  run-check:
    cmds:
      - task: run-check-intranet
      - task: run-check-backend
      - task: run-check-frontend
    silent: false
  run-check-intranet:
    cmds:
      - golangci-lint run --config ../../.golangci.yml
    dir: "{{.INTRANET_DIR}}"
    silent: false
  run-check-backend:
    cmds:
      - golangci-lint run --config ../.golangci.yml
    dir: ./backend
    silent: false
  run-check-frontend:
    cmds:
      - golangci-lint run --config ../.golangci.yml
    dir: "{{.FRONTEND_DIR}}"
    silent: false
  run-test:
    cmds:
      - task: intranet:run-sonar-scanner-prepare
      # Create test coverage directory and initialize main coverage file
      - mkdir -p devops
      - "echo \"mode: atomic\" > devops/coverage.out"
      # Run tests for each module and merge coverage files
      - |
        modules=("libs/intranet" "libs/hexa" "services/backend" "services/frontend")
        for module in "${modules[@]}"; do
          if [ -f "$module/coverage.out" ]; then
            echo "Merging coverage from $module..."
            # Append all lines except the first one (mode line) from each coverage file
            tail -n +2 "$module/coverage.out" >> devops/coverage.out
          else
            echo "No coverage file found in $module"
          fi
        done
    env:
      GO_TEST_TIMEOUT: 5m
      CGO_ENABLED: 1
    silent: true
  run-security-scan:
    cmds:
      - docker-compose -f devops/docker-compose.ci.yaml run trivy
      - task: "intranet:run-security-scan"
      - task: "frontend:run-security-scan"
    silent: false
  run-sonar-scan-intranet:
    dotenv: ['.env']
    vars:
      SONAR_HOST_URL: "${SONAR_HOST_URL}"
      SONAR_TOKEN: "${SONAR_TOKEN}"
    cmds:
    - echo "{{.SONAR_HOST_URL}}"
    - docker run -w /usr/src/libs/intranet -v ./:/usr/src -v ./coverage.out:/usr/src/coverage.out -e SONAR_TOKEN={{.SONAR_HOST_URL}} -e SONAR_HOST_URL={{.SONAR_TOKEN}} --rm sonarsource/sonar-scanner-cli sonar-scanner -Dsonar.host.url=${SONAR_HOST_URL:-http://sonarqube:9000} -Dsonar.login=${SONAR_TOKEN} -X
    silent: false
  run-sonar-scan:
    cmds:
      # - golangci-lint run --out-format checkstyle > golangci-lint-report.xml || true
      - docker-compose -f devops/docker-compose.ci.yaml run sonar-scanner
    silent: true
  go-mod-rm-replace:
    cmds:
      - go mod edit -dropreplace github.com/emilioforrer/hexa@v0.0.0
    silent: true
  go-mod-add-replace:
    cmds:
      - go mod edit -replace=github.com/emilioforrer/hexa@v0.0.0=./hexa
    silent: true
  run-ci:
    cmds:
    - task: run-check
    - task: run-test
    - task: run-security-scan
    - task: run-sonar-scan

  docker-login:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      # - docker login ${REGISTRY_URL} -u ${REGISTRY_USER} -p {{.REGISTRY_PASSWORD}}
      - echo "{{.REGISTRY_PASSWORD}}" | docker login ${REGISTRY_URL} -u ${REGISTRY_USER} --password-stdin

    silent: true
    vars:
      REGISTRY_AWS_PROFILE: "${REGISTRY_AWS_PROFILE}"
      REGISTRY_AWS_REGION: "${REGISTRY_AWS_REGION}"
      REGISTRY_PASSWORD:
        sh: echo '$(aws ecr get-login-password --region {{.REGISTRY_AWS_REGION}} --profile {{.REGISTRY_AWS_PROFILE}})'
        # sh: echo "some-command-to-get-password-dynamically" 
        # For AWS: 
        # echo "$(aws ecr get-login-password --region us-east-1 --profile myprofile)"
        # For Google Cloud Platform (GCP):
        # echo "$(gcloud auth print-access-token)"
        # For Azure Container Registry (ACR):
        # echo "$(az acr login --name myregistry --expose-token --output tsv --query accessToken)"

  docker-build:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      # - docker build -t ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG} .
      - docker build -t ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG} --file devops/Dockerfile --target prod-frontend-ssr .
    silent: true

  docker-push:
    dotenv: ['{{.FRONTEND_DIR}}/.env']
    cmds:
      - docker push ${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG}
    silent: true

  docker-publish:
    deps: [docker-login, docker-build]
    cmds:
      - task: docker-push
    silent: true

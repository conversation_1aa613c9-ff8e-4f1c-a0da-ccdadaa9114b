version: '3.8'

# Define services.
services:
  postgres:
    image: postgres:16.3-alpine
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: sa_intranet
      POSTGRES_PASSWORD: sa_password
      POSTGRES_DB: sa_intranet_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sa_intranet -d sa_intranet_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
  pgadmin:
    image: dpage/pgadmin4
    ports:
      - '5050:80'
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
  dev-frontend:
    build:
      context: ../
      dockerfile: devops/Dockerfile
      target: dev-frontend
    ports:
      - '8000:8000'
    env_file: ../.env
    volumes:
      - ../:/workspace:cached
    depends_on:
      dev-frontend-js:
        condition: service_healthy
      postgres:
        condition: service_healthy
  dev-frontend-js:
    build:
      context: ../
      dockerfile: devops/Dockerfile
      target: dev-frontend
    command: sh -c "npm install && npm run dev"
    ports:
      - '13714:13714'
    env_file: ../.env
    volumes:
      - ../:/workspace:cached
    healthcheck:
      test: ["CMD", "curl", "http://dev-frontend-js:13714"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 5s
  # dev-backend:
  #   build:
  #     context: ../
  #     dockerfile: devops/Dockerfile
  #     target: dev-backend
  #   ports:
  #     - '4000:4000'
  #   env_file: ../.env
  #   volumes:
  #     - ../:/workspace:cached
  # prod-backend:
  #   build:
  #     context: ../
  #     dockerfile: devops/Dockerfile
  #     target: prod-backend
  #   ports:
  #     - '4002:4000'
  #   env_file: ../.env
  # prod-frontend:
  #   build:
  #     context: ../
  #     dockerfile: devops/Dockerfile
  #     target: prod-frontend
  #   ports:
  #     - '4003:8000'
  #   env_file: ../.env
  # prod-frontend-ssr:
  #   build:
  #     context: ../
  #     dockerfile: devops/Dockerfile
  #     target: prod-frontend-ssr
  #   ports:
  #     - '4004:8000'
  #   env_file: ../.env
volumes:
  postgres_data:
  pgadmin_data:

# Intranet

## Setup the project

```bash
task setup
```

## Run the project (docker)

```bash
task docker-compose-up
```

## Enter to frontend container

```bash
task docker-compose-exec-dev-frontend
```

## CI

### Run tests

Run tests with coverage report for intranet and prepare files for SonarQube scanner

```bash
task intranet:run-sonar-scanner-prepare
```

### Run Security Scans

Run security scans whole project using trivy

```bash
task run-security-scan
```

### Run SonarQube Scanner

Run SonarQube scanner for intranet

```bash
task run-sonar-scan-intranet
```

## CD

### Update Changelog

```bash
git-chglog --next-tag v1.6.0
```

### Update version

```bash
git tag -a v1.6.0 -m "Release version 1.6.0"
```

## Before Deploy

Run the following tasks

```bash
task run-security-scan
task run-check-intranet
task intranet:run-sonar-scanner-prepare
task run-sonar-scan-intranet
```


### Deploy

Builds and publishes docker images to container registry

```bash
task docker-publish
```
